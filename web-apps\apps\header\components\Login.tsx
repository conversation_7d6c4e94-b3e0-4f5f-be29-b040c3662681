import { useAuth0 } from "@auth0/auth0-react";
import { useLayoutEffect } from "react";
import jwtDecode, { JwtPayload } from "jwt-decode";

export default function LoginRedirect() {
	const { isAuthenticated, loginWithRedirect, isLoading, user, getAccessTokenSilently } = useAuth0();
	const isCompanyDomain = localStorage.getItem('isCompanyDomain');
	useLayoutEffect(() => {
		(async () => {

			try {
				if (!isLoading && isAuthenticated && user) {
					const token = await getAccessTokenSilently({
						ignoreCache: true,
						scope: 'openid profile email offline_access'
					});
					const data = jwtDecode<JwtPayload>(token);
					const url = /:\/\/([^\/]+)/.exec(window.location.href)[1];
					const domain = url.split('.')[0];
					const allowedHostNames = ['localhost', 'app.development.urecruits.com'];
					if (!allowedHostNames.includes(window.location.hostname)) {
						if (domain != data['https://urecruits.com/tenantId']) {
							const newUrl = window.location.href.replace(domain, `${data['https://urecruits.com/tenantId']}`)
							window.location.replace(newUrl);
						}
					}
					if (domain == data['https://urecruits.com/tenantId'] || window.location.hostname == 'localhost' || window.location.hostname == 'app.development.urecruits.com') {
						localStorage.setItem('isCompanyDomain', 'true');

					} else {
						localStorage.setItem('isCompanyDomain', 'false');
					}
					localStorage.setItem('token', token);
					localStorage.setItem('auth0Id', user.sub);

					// Check for job apply URL redirect after authentication
					const jobApplyRedirectUrl = localStorage.getItem('jobApplyRedirectUrl');
					const jobApplyParams = localStorage.getItem('jobApplyParams');

					if (jobApplyRedirectUrl && jobApplyParams) {
						console.log('User authenticated, redirecting back to job apply URL:', jobApplyRedirectUrl);

						// Parse and log the parameters
						try {
							const paramData = JSON.parse(jobApplyParams);
							console.group('🎯 Job Apply Parameters - After Login Success');
							console.log('📋 Summary:', {
								jobId: paramData.jobId,
								applicant_guid: paramData.applicant_guid,
								source: paramData.source,
								timestamp: paramData.timestamp
							});

							if (paramData.applicant_guid) {
								console.log('🆔 Applicant GUID:', paramData.applicant_guid);
							}

							if (paramData.source) {
								console.log('📍 Source:', paramData.source);
							}

							if (paramData.utm_source || paramData.utm_medium || paramData.utm_campaign) {
								console.log('📊 UTM Parameters:', {
									utm_source: paramData.utm_source,
									utm_medium: paramData.utm_medium,
									utm_campaign: paramData.utm_campaign
								});
							}

							if (paramData._jtochash || paramData._jtocprof) {
								console.log('🎯 JobTarget Parameters:', {
									_jtochash: paramData._jtochash,
									_jtocprof: paramData._jtocprof
								});
							}

							console.log('🔗 Original URL:', paramData.originalUrl);
							console.log('📦 All Parameters:', paramData.allParams);
							console.groupEnd();
						} catch (error) {
							console.error('Error parsing job apply parameters:', error);
						}

						// Clear the stored redirect URL and params after successful authentication
						localStorage.removeItem('jobApplyRedirectUrl');
						localStorage.removeItem('jobApplyParams');

						// Create clean URL without parameters for redirect
						const urlObj = new URL(jobApplyRedirectUrl);
						const cleanUrl = `${urlObj.origin}${urlObj.pathname}`;

						// Redirect back to the clean job apply URL (without parameters)
						window.location.href = cleanUrl;
						return; // Exit early to prevent further execution
					}

					const tokenExpiry = (data.exp * 1000) - Date.now();
					const refreshInterval = tokenExpiry > 0 ? Math.max(tokenExpiry - (1 * 60 * 1000), 0) : 2 * 60 * 1000;

					setInterval(async () => {
						await getAccessTokenSilently({
							ignoreCache: true,
							scope: 'openid profile email offline_access',
						}).then(newAccessToken => {
							// Use the new access token
							localStorage.setItem('token', newAccessToken);
						}).catch(error => {
							loginWithRedirect().then()
						});
					}, refreshInterval);
				} else if (!isLoading && !isAuthenticated) {
					// Check if this is a job apply URL with parameters before redirecting to login
					const currentUrl = window.location.href;
					const urlObj = new URL(currentUrl);
					const params = new URLSearchParams(urlObj.search);

					// Check if this is a job apply URL with parameters
					const isJobApplyUrl = urlObj.pathname.includes('/job/') && (urlObj.pathname.includes('/apply') || params.size > 0);

					if (isJobApplyUrl && params.size > 0) {
						// Extract and store parameters
						const allParams = {};
						params.forEach((value, key) => {
							allParams[key] = value;
						});

						const trackingData = {
							applicant_guid: params.get('applicant_guid'),
							source: params.get('source'),
							utm_source: params.get('utm_source'),
							utm_medium: params.get('utm_medium'),
							utm_campaign: params.get('utm_campaign'),
							_jtochash: params.get('_jtochash'),
							_jtocprof: params.get('_jtocprof'),
							jobId: urlObj.pathname.split('/job/')[1]?.split('/')[0],
							allParams: allParams,
							originalUrl: currentUrl,
							timestamp: new Date().toISOString()
						};

						// Store the URL and parameters for after authentication
						localStorage.setItem('jobApplyRedirectUrl', currentUrl);
						localStorage.setItem('jobApplyParams', JSON.stringify(trackingData));

						console.group('🎯 Job Apply Parameters - Storing Before Login');
						console.log('📋 Summary:', {
							jobId: trackingData.jobId,
							applicant_guid: trackingData.applicant_guid,
							source: trackingData.source,
							timestamp: trackingData.timestamp
						});

						if (trackingData.applicant_guid) {
							console.log('🆔 Applicant GUID:', trackingData.applicant_guid);
						}

						if (trackingData.source) {
							console.log('📍 Source:', trackingData.source);
						}

						if (trackingData.utm_source || trackingData.utm_medium || trackingData.utm_campaign) {
							console.log('📊 UTM Parameters:', {
								utm_source: trackingData.utm_source,
								utm_medium: trackingData.utm_medium,
								utm_campaign: trackingData.utm_campaign
							});
						}

						if (trackingData._jtochash || trackingData._jtocprof) {
							console.log('🎯 JobTarget Parameters:', {
								_jtochash: trackingData._jtochash,
								_jtocprof: trackingData._jtocprof
							});
						}

						console.log('🔗 Original URL:', trackingData.originalUrl);
						console.log('📦 All Parameters:', trackingData.allParams);
						console.groupEnd();
					}

					loginWithRedirect().then()
				}
			} catch (e) {
				console.error("Unable to request access token", e);
			}
		})();
	}, [getAccessTokenSilently, isAuthenticated, isLoading, user]);


	return (
		<>
		</>
	)
}