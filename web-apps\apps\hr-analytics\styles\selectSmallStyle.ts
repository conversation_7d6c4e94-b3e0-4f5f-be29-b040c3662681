export const selectSmallStyle = {
  control: (provided: any, state: any) => ({
    ...provided,
    borderRadius: '4px',
    borderColor: '#DFE2E6',
    fontSize: '12px',
    width: '100%',
    minHeight: '32px',
    boxShadow: state.isFocused ? '#ACD8D1' : '',
    ':hover': {
      borderColor: '#ACD8D1',
    },
    '> div:first-of-type': {
      display: 'flex',
      width: '50px',
    },
  }),
  multiValue: (provided: any, state: any) => ({
    ...provided,
    borderRadius: '4px',
    background: 'rgba(2,156,165,0.1)',
  }),
  multiValueLabel: (provided: any, state: any) => ({
    ...provided,
    fontSize: '14px',
    color: '#2A2C33',
  }),
  indicatorsContainer: (provided: any, state: any) => ({
    ...provided,
    justifyContent: 'flex-end',
    "div svg": {
      width: '14px',
      height: '14px',
    }
  }),
  multiValueRemove: (provided: any, state: any) => ({
    ...provided,
    color: '#015462',
    borderRadius: 0,
    cursor: 'pointer',
    paddingRight: '2px',
    paddingLeft: '6px',
    '&:hover': {
      backgroundColor: 'unset',
      color: '#2A2C33',
    },
    '>svg': {
      width: '14px',
      minWidth: '14px',
      height: '14px',
    },
  }),
  clearIndicator: (provided: any, state: any) => ({
    ...provided,
    color: '#737980',
    cursor: 'pointer',
    '&:hover': {
      color: '#2A2C33',
    },
  }),
  dropdownIndicator: (provided: any, state: any) => ({
    ...provided,
    color: '#737980',
    cursor: 'pointer',
    '&:hover': {
      color: '#099C73',
    },
  }),
  option: (provided: any, state: any) => ({
    ...provided,
    margin: '5px 0',
    color: '#343B43',
    fontSize: '14px',
    display: 'flex',
    alignItems: 'center',
    cursor: 'pointer',
    backgroundColor: state.isFocused ? '#fff' : '#fff',
    ':focus': {
      backgroundColor: '#fff',
    },
    ':hover': {
      backgroundColor: '#fff',
    },
  }),
  placeholder: (provided: any) => ({
    ...provided,
    color: '#999EA5',
    fontSize: '14px',
    paddingRight: 16,
    position: 'absolute',
  }),
  valueContainer: (provided: any) => ({
    ...provided,
    padding: '2px 12px',
    fontSize: '12px',
  }),
  menu: (provided: any) => ({
    ...provided,
    zIndex: 20,
  }),
  menuList: (provided: any) => ({
    ...provided,
    '>div': {
      fontSize: "12px",
      padding: "4px 12px",
      ':hover': {
        backgroundColor: '#029ca51a',
      },
    },
  }),
  singleValue: (provided: any) => ({
      ...provided, maxWidth: '90%'
    }
  )
}