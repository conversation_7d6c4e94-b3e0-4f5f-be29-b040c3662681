import {useState} from "react";
import {Link} from "react-router-dom";
import tempImg from "../../image/temp_help-centr.png";
import phoneIc from "../../image/icon/phone-solid_green_ic.svg";
import mailIc from "../../image/icon/email-solid_green_ic.svg";

const DashboardDropdown = () => {
	const [activeTab, setActiveTab] = useState("Help Center");

	return (
		<div className="dashboard-dropdown">
			<p className="dashboard-dropdown__headline">
				Company links
			</p>
			<div className="dashboard-dropdown__list">
				<div className={`dashboard-dropdown__item ${activeTab === "activeTab" ? "active" : ""}`}>
					<div className="dashboard-dropdown__head" onClick={() => setActiveTab(activeTab === 'Help Center' ? '': 'Help Center')}>
						<p className="dashboard-dropdown__head__name">Help Center</p>
						<div className="dashboard-dropdown__head__arrow">
							<ArrowSVG/>
						</div>
					</div>
					{
						activeTab === "Help Center" && (
							<div className="dashboard-dropdown__body">
								<div className="help-center">
									<div className="help-center__item">
										<img src={tempImg} alt="item img" className="help-center__item__img"/>
										<div className="help-center__item__info">
											<div className="help-center__item__top">
												<p className="help-center__item__name">In 23 days</p>
												<Link to="/" className="help-center__item__link">more</Link>
											</div>
											<p className="help-center__item__text">
												Commodo tristique massa amet urna Commodo tristique massa amet urna Commodo tristique massa amet urna Commodo
												tristique massa amet urna
											</p>
										</div>
									</div>
									<div className="help-center__item">
										<img src={tempImg} alt="item img" className="help-center__item__img"/>
										<div className="help-center__item__info">
											<div className="help-center__item__top">
												<p className="help-center__item__name">In 23 days</p>
												<Link to="/" className="help-center__item__link">more</Link>
											</div>
											<p className="help-center__item__text">
												Commodo tristique massa amet urna Commodo tristique massa amet urna Commodo tristique massa amet urna Commodo
												tristique massa amet urna
											</p>
										</div>
									</div>
								</div>
							</div>
						)
					}
				</div>
				<div className={`dashboard-dropdown__item ${activeTab === "Company" ? "active" : ""}`}>
					<div className="dashboard-dropdown__head" onClick={() => setActiveTab(activeTab === 'Company' ? '': 'Company')}>
						<p className="dashboard-dropdown__head__name">Company</p>
						<div className="dashboard-dropdown__head__arrow">
							<ArrowSVG/>
						</div>
					</div>
					{
						activeTab === "Company" && (
							<div className="dashboard-dropdown__body">
								<div className="company-links">
									<a href="tel:+61 2 3456 7890" className="company-links__item">
										<img src={phoneIc} alt="phone ic"/>
										+61 2 3456 7890
									</a>
									<a href="mailto:<EMAIL>" className="company-links__item">
										<img src={mailIc} alt="phone ic"/>
										<EMAIL>
									</a>
								</div>
							</div>
						)
					}
				</div>
				<div className={`dashboard-dropdown__item ${activeTab === "News" ? "active" : ""}`}>
					<div className="dashboard-dropdown__head" onClick={() => setActiveTab(activeTab === 'News' ? '': 'News')}>
						<p className="dashboard-dropdown__head__name">News</p>
						<div className="dashboard-dropdown__head__arrow">
							<ArrowSVG/>
						</div>
					</div>
					{
						activeTab === "News" && (
							<div className="dashboard-dropdown__body">
								<div className="news">
									<div className="news__item">
										<div className="news__item__head">
											<p className="news__item__date">Nov 19</p>
											<Link to="/" className="news__item__link">more</Link>
										</div>
										<div className="news__item__info">
											<p className="news__item__info__title">Congratulations!</p>
											<p className="news__item__info__text">Commodo tristique massa amet urna, egestas maecenas...</p>
										</div>
									</div>
								</div>
							</div>
						)
					}
				</div>
			</div>
		</div>
	);
};

export default DashboardDropdown;

const ArrowSVG = () => {
	return (
		<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M5.99993 4.38184H2.56059C1.89241 4.38184 1.55778 5.27856 2.03026 5.803L5.4696 9.62067C5.76249 9.94578 6.23736 9.94578 6.53026 9.62067L9.9696 5.803C10.4421 5.27856 10.1074 4.38184 9.43927 4.38184H5.99993Z"
				fill="#737980"/>
		</svg>

	);
};