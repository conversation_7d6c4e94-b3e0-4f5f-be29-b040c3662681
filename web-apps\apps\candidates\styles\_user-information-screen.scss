@use "config" as *;
@use "mixins" as *;

.user-information {
  padding: 36px;
  @include media(xs){
    padding: 25px 20px;
  }
  &__inner {
    max-width: 912px;
    border-radius: 12px;
    border: 1px solid $grayTone2;
    background: $white;
    margin: 0 auto;
    padding: 36px;
    position: relative;
    @include media(md) {
      border-radius: 0;
      padding: 16px;
    }

    @include media(xs) {
      border-radius: 12px;
      border-top: 1px solid $grayTone2;
    }
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28px;
    &__left {
      display: flex;
    }

    &__headline {
      line-height: 1;
    }
    
    &__right {
      display: flex;
      a {
        color: $grayTone4;
        font-size: 14px;
      }
    }
    
    @include media(xs) {
      align-items: flex-start;
      flex-direction: column;
      gap: 16px;
    }
  }

  &__head {
    width: 100%;
    margin-bottom: 24px;

    &__headline {
      color: $mainGreen;
      font-family: 'Poppins', 'sans-serif';
      font-size: 20px;
      font-weight: 600;
      @include media(xs) {
        font-size: 16px;
      }
    }
  }

  .summary-personal-info {
    border: 1px solid $grayTone2;
    border-radius: 8px;
    padding: 24px;
    display: flex;
    margin-bottom: 24px;

    @include media(sm) {
      flex-direction: column;
    }

    &__left, &__rigth {
      width: 50%;
      display: flex;
      flex-direction: column;
      @include media(sm) {
        width: 100%;
      }
    }

    &__left {
      padding-right: 16px;
      border-right: 1px solid $grayTone2;
      @include media(sm) {
        padding-right: 0;
        border-right: 0;
      }

      &__name {
        font-weight: 900;
        font-size: 16px;
        line-height: 1.5;
        color: $black;
      }

      &__skills-list {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        align-items: center;
      }

      &__text {
        font-size: 14px;
        line-height: 1.4;
        color: $black;
        margin-top: 4px;

        &.skills {
          margin-bottom: 0;
          margin-top: 12px;
          margin-right: 12px;
          padding: 4px 8px;
          background: rgba(2, 156, 165, 0.1);
          border-radius: 3px;
        }
      }

      &__avatar {
        width: 100%;
        margin-bottom: 12px;

        img {
          width: 64px;
          height: 64px;
          border-radius: 50%;
        }
      }

    }

    &__right {
      padding-left: 16px;
      @include media(sm) {
        padding-left: 0;
        padding-top: 24px;
      }

      &__row {
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        font-size: 14px;
        line-height: 1.4;
        color: $black;

        &.disabled {
          color: #999EA5;

          svg {
            path {
              stroke: $grayTone4;
            }
          }
        }

        svg {
          width: 20px;
          height: 20px;
          margin-right: 8px;
        }

      }

      &__ctc {
        display: flex;
        flex-direction: column;
      }

      &__ctc-title {
        font-weight: 900;
        font-size: 16px;
        line-height: 1.5;
        color: $black;
      }

      &__ctc-text {
        font-weight: 400;
        font-size: 14px;
        line-height: 1.4;
        color: $black;
      }

    }
  }

  .summary {
    display: flex;
    flex-direction: column;

    &__agreement {
      font-weight: 900;
      font-size: 16px;
      line-height: 1.5;
      color: $grayTone6;
      margin-bottom: 16px;
    }

    &__agreement_desc {
      font-size: 14px;
      line-height: 1.4;
      color: $grayTone7;
      margin-bottom: 16px;
    }

    &__item {
      margin-bottom: 24px;
      display: flex;
      flex-direction: column;

      &:after {
        margin-top: 24px;
        background: $grayTone2;
        width: 100%;
        height: 1px;
        content: '';
        border-radius: 4px;
      }

    }

    &__item.last {
      margin-bottom: 0;

      &:after {
        margin-bottom: 0;
        display: none;
      }
    }

    &__title {
      margin-bottom: 16px;
      font-weight: 900;
      font-size: 16px;
      line-height: 1.5;
      color: $grayTone6;
    }

    &__question {
      width: 100%;
      display: flex;
      flex-direction: column;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      &__answer {
        font-weight: 900;
        font-size: 14px;
        line-height: 19px;
        color: $grayTone7;
        margin-top: 4px;
      }

      &__title {
        font-size: 14px;
        line-height: 1.4;
        color: $grayTone7;
      }
    }
  }
}

