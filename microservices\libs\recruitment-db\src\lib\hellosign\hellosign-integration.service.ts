import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";

import { Op } from "sequelize";
import { HellosignIntegration } from "./hellosign-integration.model";
import { HellosignIntegrationDto } from "./dto/hellosign-integration.dto";

@Injectable()
export class HellosignIntegrationService {
  constructor(
    @InjectModel(HellosignIntegration)
    private hellosignRepository: typeof HellosignIntegration
  ) {}

  async create(dto: HellosignIntegrationDto, companyId: number) {
    const data = await this.hellosignRepository.findOne({
      where: {
        [Op.or]: { clientId: dto.clientId, key: dto.key },
        companyId,
      },
      attributes: ["id"],
    });

    if (data) {
      throw new HttpException("Record already exists!", HttpStatus.CONFLICT);
    } else {
      const credentials = await this.hellosignRepository.create({
        ...dto,
        companyId,
      });
      return credentials;
    }
  }

  async getHellosignIntegration(companyId: number) {
    return await this.hellosignRepository.findOne({
      where: {
        companyId,
      },
      attributes: ["clientId", "key"],
    });
  }

  async removeHellosignIntegration(companyId: number) {
    return await this.hellosignRepository.destroy({
      where: {
        companyId,
      },
    });
  }
}
