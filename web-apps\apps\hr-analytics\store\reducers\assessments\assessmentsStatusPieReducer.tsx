import type { PayloadAction } from "@reduxjs/toolkit";
// eslint-disable-next-line no-duplicate-imports
import { createSlice } from "@reduxjs/toolkit";
import { IAssessmentsStatusPie } from "../../../types/redux/assessments";

const initialStateATM: IAssessmentsStatusPie = {
  totalAssessments: 0,
  assessmentsData: [],
};

export const assessmentsStatusPie = createSlice({
  name: "assessments_pie",
  initialState: initialStateATM,
  reducers: {
    setAssessmentsStatusPie: (
      state,
      action: PayloadAction<IAssessmentsStatusPie>
    ) => {
      state.totalAssessments = action.payload.totalAssessments;
      state.assessmentsData = action.payload.assessmentsData;
    }
  },
});

export const { setAssessmentsStatusPie } = assessmentsStatusPie.actions;

export default assessmentsStatusPie.reducer;
