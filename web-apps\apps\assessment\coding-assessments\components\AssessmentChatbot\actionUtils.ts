import axios from 'axios';
import { getConfig } from '@ucrecruits/globalstyle/src/ucrecruits-globalstyle';
import { getEnv } from '@urecruits/api';

const { API_ASSESSMENT, API_RECRUITMENT } = getEnv();

// Function to get company ID
export const getCompanyId = async () => {
    try {
        const response = await axios.get(`${API_RECRUITMENT}/api/company`, getConfig());
        return response.data?.id || null;
    } catch (error) {
        return null;
    }
};

export const callOpenAI = async (message, testcase = false, chatContext = null) => {
    try {
        // Get company ID
        const companyId = await getCompanyId();
       
        const response = await axios.post(
            `${API_ASSESSMENT}/api/assessment-chatbot/generate-question`,
            JSON.stringify({ 
                userInput: message, 
                isTestCaseIncluded: testcase,
                companyId: companyId,
                chatContext: chatContext
            }),
            getConfig()
        );
   
        return response.data;
    } catch (error) {
        throw new Error("Failed to communicate with chatbot.");
    }
};

export const generateCodingQuestions = async (assessmentDetails) => {
    try {
        // Get company ID
        const companyId = await getCompanyId();
        
        // Create a prompt for generating questions based on assessment details
        const prompt = `Generate ${assessmentDetails.numQuestions} coding questions for an assessment with the following details:
        Name: ${assessmentDetails.name}
        Goal/Purpose: ${assessmentDetails.goal}
        Difficulty: ${assessmentDetails.difficulty}
        
        Please provide a list of questions with title and brief description for each.`;
        
        const response = await axios.post(
            `${API_ASSESSMENT}/api/assessment-chatbot/generate-questions`,
            JSON.stringify({
                prompt: prompt,
                numQuestions: assessmentDetails.numQuestions,
                companyId: companyId
            }),
            getConfig()
        );
        
        return response.data;
    } catch (error) {
        throw new Error("Failed to generate coding questions.");
    }
};

export const createFinalAssessment = async (assessmentDetails, selectedQuestion, includeTestCases) => {
    try {
        // Get company ID
        const companyId = await getCompanyId();
        
        const response = await axios.post(
            `${API_ASSESSMENT}/api/assessment-chatbot/create-assessment`,
            JSON.stringify({
                assessmentDetails: {
                    name: assessmentDetails.name,
                    goal: assessmentDetails.goal,
                    difficulty: assessmentDetails.difficulty,
                    language: assessmentDetails.language
                },
                question: selectedQuestion,
                includeTestCases: includeTestCases,
                companyId: companyId
            }),
            getConfig()
        );
        
        return response.data;
    } catch (error) {
        throw new Error("Failed to create the final assessment.");
    }
};

export const extractAssessmentDetails = (assessmentDetails, chatAssessmentData) => {
  // Extract assessment details based on the response from the API
  const updatedAssessmentDetails = {
    ...(assessmentDetails.name && { name: assessmentDetails.name }),
    ...(assessmentDetails.description && { description: assessmentDetails.description }),
    ...(assessmentDetails.assessmentType && { assessmentType: assessmentDetails.assessmentType }),
    ...(assessmentDetails.languageId && { languageId: assessmentDetails.languageId }),
    ...(assessmentDetails.difficulty && { difficulty: assessmentDetails.difficulty }),
    ...(assessmentDetails.timeLimit && { timeLimit: assessmentDetails.timeLimit }),
    ...(assessmentDetails.instructions && { instructions: assessmentDetails.instructions }),
    ...(assessmentDetails.sampleInput && { sampleInput: assessmentDetails.sampleInput }),
    ...(assessmentDetails.sampleOutput && { sampleOutput: assessmentDetails.sampleOutput }),
    ...(assessmentDetails.testCases && { testCases: assessmentDetails.testCases }),
  };

  // Extract questions if available (for take-home assessments)
  const updatedQuestions = assessmentDetails.questions || [];

  return {
    ...updatedAssessmentDetails,
    questions: updatedQuestions
  };
};

export const chatWithAI = async (message, conversationHistory) => {
    try {
        // Get company ID
        const companyId = await getCompanyId();
       
        const response = await axios.post(
            `${API_ASSESSMENT}/api/assessment-chatbot/chat`,
            JSON.stringify({ 
                message: message,
                conversationHistory: conversationHistory,
                companyId: companyId
            }),
            getConfig()
        );
   
        return response.data;
    } catch (error) {
        throw new Error("Failed to communicate with chatbot.");
    }
}; 