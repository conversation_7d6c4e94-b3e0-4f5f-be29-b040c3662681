import { filter as types } from "../../types";
import * as actions from "../../types/filter/actions.types";

export const setFilterLanguages = (
  payload: types.State["filterLanguages"]
): actions.SetFilterLanguages => ({
  type: types.actionTypes.SET_FILTER_LANGUAGES,
  payload,
});

export const setFilterPackage = (
  payload: types.State["filterPackage"]
): actions.SetFilterPackage => ({
  type: types.actionTypes.SET_FILTER_PACKAGE,
  payload,
});

export const setFilterQuestionType = (
  payload: types.State["filterPackage"]
): actions.SetFilterQuestionType => ({
  type: types.actionTypes.SET_FILTER_QUESTION_TYPE,
  payload,
});

export const setFilterCreatedBy = (
  payload: types.State["filterCreatedBy"]
): actions.SetFilterCreatedBy => ({
  type: types.actionTypes.SET_FILTER_CREATED_BY,
  payload,
});

export const setFilterCreatedOn = (
  payload: types.State["filterCreatedOn"]
): actions.SetFilterCreatedOn => ({
  type: types.actionTypes.SET_FILTER_CREATED_ON,
  payload,
});

export const setIsOnline = (
  payload: types.State["isOnline"]
): actions.SetIsOnline => ({
  type: types.actionTypes.SET_IS_ONLINE,
  payload,
});
