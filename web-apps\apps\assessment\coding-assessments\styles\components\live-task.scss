@use "../config" as *;
@use "../mixins" as *;

.create-live-task {
  // width: calc(100vw - 131px);
  // height: calc(100vh - 60px);
  // margin: 0 auto;

  @include media(xs) {
    width: 100%;
    height: 100%;
  }

  &__container {
    width: 100%;
    height: calc(85vh - 131px);

    @include media(xs) {
      height: 100%;
    }
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 0 33px 0;

    @include media(md) {
      flex-direction: column;
      align-items: start;
      gap: 15px;
    }
  }
}

.task {
  display: block;
  position: relative;
  right: 280px;
  &__header {
    display: flex;
    gap: 20px;
    align-items: center;

    @include media(md) {
      align-items: start;
    }

    h1 {
      font-size: 28px;

      @include media(md) {
        font-size: 22px;
      }

      @include media(xs) {
        font-size: 20px;
      }
    }
    &__select {
      display: inline-flex;
      align-items: center;
      cursor: pointer;
      position: initial;
    }
  }
  &__take {
    position: absolute;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: 0.3s ease-in, 0.3s ease-in-out;
    margin-right: 32px;
    width: 243px;
    background: $white;
    box-shadow: 0 6px 12px rgba(7, 7, 12, 0.08);
    border-radius: 4px;
    border: 1px solid $grayTone2;
    transform: translate(24px, 19px);
    z-index: 1;
    flex-direction: column;

    @include media(md) {
      width: 173px;
    }

    @include media(lg) {
      transform: translate(50%, 19px);
    }
  }
  &__head {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  &__item {
    width: 100%;

    &:hover {
      background: $grayTone1;
    }

    &:first-child {
      padding: 24px 24px 12px 24px;

      @include media(md) {
        padding: 12px 12px 12px 12px;
      }
    }

    &:last-child {
      padding: 12px 24px 24px 24px;

      @include media(md) {
        padding: 12px 12px 12px 12px;
      }
    }
  }

  &__header-name {
    font-weight: 400;
    color: $mainGreen;
    margin-right: 13px;

    @include media(md) {
      font-size: 21px;
    }

    @include media(xs) {
      font-size: 16px;
    }
  }

  &__name {
    font-weight: 300;
    font-size: 18px;
    color: $grayTone5;

    @include media(md) {
      font-size: 14px;
    }
  }
}

.turn-arrow {
  transform: rotate(180deg);
}

.task-live {
  z-index: -1;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;

  &__container {
    width: 910px;
    padding: 36px;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #dfe2e6;
    margin: 1rem 0 5rem 0;
    color: #343b43;
    position: relative;

    @include media(md) {
      width: 100%;
      margin: 1rem 0 1rem 0;
    }

    @include media(xs) {
      width: 100%;
      margin: 1rem 0 0 0;
      padding: 0;
      border: 0;
    }
  }

  &__title {
    color: $mainGreen;
    font-size: 20px;

    @include media(xs) {
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.task-second-live {
  z-index: -1;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  &__container {
    padding: 36px;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #dfe2e6;
    margin: 0 2rem 0 5rem;
    width: 910px;
    color: #343b43;
  }
}

.pen-icon {
  width: 16px;
}
.send-button-task {
  font-weight: 900;
  font-size: 14px;
  color: $grayTone4;
  padding: 10px;
  cursor: pointer;
  border-radius: 4px;
  margin-right: 20px;
  min-width: 137px;
  display: flex;

  &:hover {
    color: $grayTone7;
  }

  .pen-icon {
    margin-right: 10px;
  }
}

.button-group {
  display: flex;
  align-items: center;
  position: relative;

  @include media(xs) {
    margin-top: 16px;
    justify-content: space-between;
    width: 100%;
    .button{
      width: 100%;
    }
  }

  &__create {
    @include media(xs) {
      width: 100%;
    }
  }

  &__visibility-switcher {
    padding: 10px;
    border: 2px solid $mainGreen;
    border-radius: 4px;
    margin-left: 16px;

    svg {
      width: 24px !important;
    }

    &.active {
      background: linear-gradient(125.2deg, #099c73 8.04%, #015462 127.26%);
      border: none;
      padding: 12px;

      svg {
        path {
          stroke: #fff;
        }
      }
    }
  }

  &__cancel-button-task {
    font-weight: 900;
    font-size: 14px;
    color: $grayTone4;
    padding: 10px;
    cursor: pointer;
    border-radius: 4px;
    margin-right: 10px;

    &:hover {
      color: $grayTone7;
    }

    @include media(xs) {
      padding: 10px 16px;
      border: 2px solid $mainGreen;
      margin-right: 16px;
      min-width: 46px;
    }
  }
  &__edit-button-task {
    @include media(xs) {
      position: absolute;
      top: 54px;
      right: 0;
      background-color: #fff;
      border: 1px solid $grayTone2;
      border-radius: 4px;
      box-shadow: 0px 6px 12px rgba(7, 7, 12, 0.08);
      z-index: 1;

      .send-button-task {
        color: $grayTone7;
        margin: 0;
      }
    }
  }
}

.cancel-button-task {
  color: $grayTone4;
  padding: 10px;
  cursor: pointer;
  border-radius: 4px;
  margin-right: 20px;
}

.first-group-button-task {
  text-align: right;
}

.group-button-task {
  display: flex;
  justify-content: space-between;
}

.group-button-task-home-three {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
}

.back-button-task {
  font-size: 14px;
  font-weight: 900;
  line-height: 100%;
  color: $grayTone4;
  padding: 16px 28px;
  cursor: pointer;
  border-radius: 4px;
  margin-top: 20px;
  transition: 0.3s;

  &:hover {
    color: #000;
  }
}

.bar-container {
  position: relative;
}

.details-content-bar {
  position: inherit;
  display: flex;
  // width: calc(100vw - 106px);
  height: 92px;
  background: #ffffff;
  border-top: 1px solid #dfe2e6;
  border-bottom: 1px solid #dfe2e6;
  margin-bottom: 10px;
  justify-content: space-around;
  flex-wrap: wrap;
  flex-direction: column;
  align-items: center;

  @include media(md) {
    width: calc(100vw - 136px);
  }

  @include media(xs) {
    width: 100%;
  }
}

.timeline {
  list-style-type: none;
  display: flex;
  align-items: center;
}

.li {
  transition: all 200ms ease-in;
}

.status {
  width: 450px;
  padding: 10px 0;
  display: flex;
  justify-content: center;
  border-top: 2px solid #d6dce0;
  position: relative;
  transition: all 200ms ease-in;
  margin-top: 20px;

  @include media(md) {
    width: 270px;
  }

  @include media(xs) {
    width: 170px;
  }
}

.text-test-timeline {
  display: flex;
  justify-content: space-between;
  width: 910px;
  margin-top: -21px;

  @include media(md) {
    width: 100%;
  }
}

.text-test-timeline h4 {
  font-weight: 400;
  font-size: 14px;
  color: $grayTone4;
}

.status:before {
  content: "";
  width: 25px;
  height: 25px;
  background-color: $grayTone3;
  border-radius: 25px;
  border: 1px solid $grayTone3;
  position: absolute;
  top: -15px;
  left: 0;
  transition: all 200ms ease-in;
}

.status:last-child::before {
  left: 97%;

  @include media(xs) {
    left: 85%;
  }
}

//--------- pre-complete-first-dot
.li.pre-complete .status {
  border-top: 2px solid $mainGreen;
}

.li.pre-complete .status:before {
  background-color: $white;
  border: 3px solid $mainGreen;
  transition: all 200ms ease-in;
  left: 0;
}

.li.pre-complete .status h4 {
  color: $mainGreen;
}
//---------

//--------- pre-complete-last-dot
.pre-complete-next:last-child::before {
  background-color: $white;
  border: 2px solid $mainGreen;
  transition: all 200ms ease-in;
  left: 97%;
}

.pre-complete-next .status {
  border-top: 2px solid $mainGreen;
}

.li.pre-complete-next .status:before {
  background-color: $white;
  border: 2px solid $mainGreen;
  transition: all 200ms ease-in;
  left: 97%;

  @include media(xs) {
    left: 85%;
  }
}
//---------

//--------- complete-first-dot
.li.complete .status {
  border-top: 2px solid $mainGreen;
}

.li.complete .status:before {
  background-color: $mainGreen;
  border: 2px solid $mainGreen;
  transition: all 200ms ease-in;
  left: 0;
}

.li.complete .status h4 {
  color: $mainGreen;
}
//---------

//--------- complete-last-dot
.complete-next:last-child::before {
  background-color: $mainGreen;
  border: 2px solid $mainGreen;
  transition: all 200ms ease-in;
  left: 97%;
}

.complete-next .status {
  border-top: 2px solid $mainGreen;
}

.li.complete-next .status:before {
  background-color: $mainGreen;
  border: 2px solid $mainGreen;
  transition: all 200ms ease-in;
  left: 97%;

  @include media(xs) {
    left: 85%;
  }
}
//---------

.label-task-font {
  font-family: "Inter", "Avenir LT Std", sans-serif;
  font-style: normal;
  font-weight: 900;
  font-size: 14px;
  line-height: 19px;
  margin-top: 20px;
  display: inline-block;
}

.live-task-input {
  resize: vertical;
  @include input;

  &__wrap {
    white-space: pre-line;
  }

  &.error {
    border: 1px solid $red;
  }

  &.readonly {
    background: $grayTone1;
    border-color: $grayTone1;
    cursor: default;
  }

  &.password {
    margin-bottom: 12px;
  }

  &.online-profile {
    padding-left: 159px;
  }

  &--url {
    position: absolute;
    color: $grayTone4;
    font-size: 14px;
    bottom: 0;
    left: 0;
    transform: translate(16px, -12px);
  }
}

.editor-class.rdw-editor-main {
  background: white;
}

.live-task-input-textarea {
  @include input;
  margin-bottom: -10px;
  resize: vertical;

  @-moz-document url-prefix() {
    margin-bottom: 0;
  }

  &.error {
    border: 1px solid $red;
  }

  &.readonly {
    background: $grayTone1;
    border-color: $grayTone1;
    cursor: default;
  }

  &.password {
    margin-bottom: 12px;
  }

  &.online-profile {
    padding-left: 159px;
  }

  &--url {
    position: absolute;
    color: $grayTone4;
    font-size: 14px;
    bottom: 0;
    left: 0;
    transform: translate(16px, -12px);
  }
}

.error-data {
  // position: absolute;
  font-size: 12px;
  color: $red;
  margin-top: 4px;
}

.button-save {
  font-size: 14px;
  font-weight: 900;
  line-height: 100%;
  background: $white;
  color: $mainGreen;
  padding: 14px 28px;
  cursor: pointer;
  border-radius: 4px;
  border: 2px solid $mainGreen;
  margin-top: 23px;
  transition: 0.3s;

  &:hover {
    color: $greenBlue2;
    border: 2px solid $greenBlue2;
  }
}

.public-DraftStyleDefault-block {
  margin: 0 !important;
}
