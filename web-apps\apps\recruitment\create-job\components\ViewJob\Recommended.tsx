import { Link } from 'react-router-dom'
import image from '../../image/tempUser.png'
import  paperclip from '../../image/icon/paperclip.svg'

const Recommended = () => {
  return(
    <div className="recommended">
      <p className="recommended__headline">
        Also recommended these job
      </p>
      <div className="recommended__list">
        <div className="recommended__item">
          <div className="recommended__top">
            <Link to="/" className="recommended__top__link">
              Candidate matched: 98%
            </Link>
          </div>
          <div className="recommended__body">
            <img src={image} alt="" className="recommended__body__image"/>
            <div className="recommended__body__inner">
              <p className="recommended__body__title">
                Alex <PERSON>
              </p>
              <ul className="recommended__info">
                <li className="recommended__info__item">
                  London
                </li>
                <li className="recommended__info__item">
                  6 years
                </li>
                <li className="recommended__info__item">
                  $20,000 - $30,000 PA
                </li>
                <li className="recommended__info__item">
                  <img src={paperclip} alt="download" className="recommended__info__download"/>
                  Alex Walling CV.pdf
                </li>
              </ul>
            </div>
          </div>
          <div className="recommended__bottom">
            <ul className="recommended__tags">
              <li className="recommended__tags__item">
                Kubernetes
              </li>
              <li className="recommended__tags__item">
                Docker
              </li>
              <li className="recommended__tags__item">
                Cloud Foundry
              </li>
              <li className="recommended__tags__item">
                Techical Sales
              </li>
            </ul>
          </div>
        </div>
        <div className="recommended__item">
          <div className="recommended__top">
            <Link to="/" className="recommended__top__link">
              Candidate matched: 98%
            </Link>
          </div>
          <div className="recommended__body">
            <img src={image} alt="" className="recommended__body__image"/>
            <div className="recommended__body__inner">
              <p className="recommended__body__title">
                Alex Walling
              </p>
              <ul className="recommended__info">
                <li className="recommended__info__item">
                  London
                </li>
                <li className="recommended__info__item">
                  6 years
                </li>
                <li className="recommended__info__item">
                  $20,000 - $30,000 PA
                </li>
                <li className="recommended__info__item">
                  <img src={paperclip} alt="download" className="recommended__info__download"/>
                  Alex Walling CV.pdf
                </li>
              </ul>
            </div>
          </div>
          <div className="recommended__bottom">
            <ul className="recommended__tags">
              <li className="recommended__tags__item">
                Kubernetes
              </li>
              <li className="recommended__tags__item">
                Docker
              </li>
              <li className="recommended__tags__item">
                Cloud Foundry
              </li>
              <li className="recommended__tags__item">
                Techical Sales
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Recommended