import { isInterview, isRoundInProgress, isRoundNotCompleted } from "./checks";
export const findActiveRound = (rounds)=>{
    let lastRound;

    for(const round of rounds){
      if(isRoundInProgress(round)) {
        return round;
      }
    }
    return lastRound;
}

export const findActiveInterview = (rounds) => {
  let lastInterview;
  
  for(const round of rounds){
    if (isInterview(round) && isRoundNotCompleted(round)) 
          return round;
    lastInterview = round;
  }
   
  return lastInterview;
};


