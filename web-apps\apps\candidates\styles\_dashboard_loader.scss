@use "config" as *;
@use "mixins" as *;

.dashboard__loader {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8); 
    z-index: 9999; 
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 200px;
    gap: 50px;
    background-color: white;
    opacity: 1;
    visibility: visible;
    transition: opacity 2s ease-out, visibility 3s ease-out

    img {
        width: 50vw;
    }

    h2 {
        font-size: 25px;
    }

    &.fade-out {
        opacity: 0;
        visibility: hidden;
    }

    @include media(md) {
        padding: 100px;
        img{
            width: 80vw;
        }
        h2{
            font-size: 16px;
        }
    }

        @media (min-width: 1440px) {
        img{
            width:55vw
        }
    }

    @include media(sm) {
        padding: 33px;

        h2 {
            font-size: 10px;
        }

        img {
            width: 85vw;
        }
    }
}

// Global Body styling to prevent interaction while loading
body {
    &.loading {
        overflow: hidden;
        pointer-events: none; // Disable interaction with body elements
    }
}