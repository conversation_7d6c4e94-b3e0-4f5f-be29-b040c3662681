import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from "@nestjs/common";
import * as DropboxSign from "@dropbox/sign";

const mergeFieldsOptions = [
  "candidateFirstname",
  "candidateLastname",
  "candidateEmail",
  "candidatePhone",
  "ctc",
  "candidateAddress",
  "jobTitle",
  "companyName",
  "joiningDate",
];
@Injectable()
export class HellosignService {
  private readonly signatureRequestApi;
  private readonly embeddedApi;
  private readonly templateApi;
  private readonly key = process.env.HELLOSIGN_KEY;
  private readonly clientId = process.env.HELLOSIGN_CLIENT_ID;
  private readonly testMode = process.env.HELLOSIGN_TEST_MODE;

  constructor() {
    this.signatureRequestApi = new DropboxSign.SignatureRequestApi();
    this.embeddedApi = new DropboxSign.EmbeddedApi();
    this.templateApi = new DropboxSign.TemplateApi();
    this.signatureRequestApi.username = this.key;
    this.embeddedApi.username = this.key;
    this.templateApi.username = this.key;
  }

  async createEmbeddedTemplateDraft(signers, file, options) {
    try {
      const mergeFields = mergeFieldsOptions.map((fieldName) => ({
        name: fieldName,
        type: DropboxSign.SubMergeField.TypeEnum.Text,
      }));

      const opts: DropboxSign.TemplateCreateEmbeddedDraftRequest = {
        clientId: this.clientId,
        fileUrls: [file],
        signerRoles: signers,
        allowReassign: true,
        showPreview: true,
        showProgressStepper: true,
        skipMeNow: false,
        mergeFields,
        ...options,
      };
      if (this.testMode) {
        opts["testMode"] = true;
      }

      if (!opts.clientId)
        throw Error("Please configure the Hellosign environment variables");
      const response = await this.templateApi.templateCreateEmbeddedDraft(opts);
      return response?.response?.data;
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException(
        error?.response?.data,
        "Failed to Execurte in Hellosign"
      );
    }
  }
  async sendSignatureRequest(
    signers: DropboxSign.SubSignatureRequestTemplateSigner[],
    mergeFields,
    templateId,
    options
  ) {
    try {
      const opts: DropboxSign.SignatureRequestCreateEmbeddedWithTemplateRequest = {
        clientId: this.clientId,
        templateIds: [templateId],
        signers,
        customFields: mergeFields,
        ...options,
      };
      if (this.testMode) {
        opts["testMode"] = true;
      }
      const response =
        await this.signatureRequestApi.signatureRequestCreateEmbeddedWithTemplate(opts);
      return response?.response?.data;
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException(
        error?.response?.data,
        "Failed to Execurte in Hellosign"
      );
    }
  }

  async templateDelete(templateId) {
    try {
      return await this.templateApi.templateDelete(templateId);
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException(
        error?.response?.data,
        "Failed to Execurte in Hellosign"
      );
    }
  }

  async getSignatureRequest(signatureRequestId) {
    try {
      return await this.signatureRequestApi.signatureRequestGet(
        signatureRequestId
      );
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException(
        error?.response?.data,
        "Failed to Execurte in Hellosign"
      );
    }
  }

  async getSignatureDocumentPreview(signatureRequestId) {
    try {
      const fileType = "pdf";
      return await this.signatureRequestApi.signatureRequestFiles(
        signatureRequestId,
        fileType
      );
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException(
        error?.response?.data,
        "Failed to Execurte in Hellosign"
      );
    }
  }

  async getTemplateDocumentPreview(templateRequestId) {
    try {
      const fileType = "pdf";
      return await this.templateApi.templateFiles(templateRequestId, fileType);
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException(
        error?.response?.data,
        "Failed to Execurte in Hellosign"
      );
    }
  }

  async getTemplate(templateRequestId) {
    try {
      return await this.templateApi.templateGet(templateRequestId);
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException(
        error?.response?.data,
        "Failed to Execurte in Hellosign"
      );
    }
  }


  async getSignUrl(signatureId) {
    // embeddedSignUrl
    try {

      return await this.embeddedApi.embeddedSignUrl(signatureId);
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException(
        error?.response?.data,
        "Failed to Execurte in Hellosign"
      );
    }
  }
}
