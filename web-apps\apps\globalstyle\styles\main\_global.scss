@use "../config" as *;
@use "../mixins" as *;
html {
  overflow: hidden;
}

body.grabbing * {
  cursor: grabbing !important;
}

body {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  font-family: "Inter", "Avenir LT Std", sans-serif;
  -webkit-font-smoothing: antialiased;
  color: $grayTone5;
  @include media(xs) {
    font-size: 16px;
  }

  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 {
  font-weight: 700;
  color: $black;
  line-height: 1.4;
  font-family: "Inter", "Poppins", sans-serif;
}

h1,
.h1 {
  font-size: 48px;
  @include media(lg) {
    font-size: 38px;
  }
  @include media(xs) {
    font-size: 24px;
  }
}

h2,
.h2 {
  font-size: 28px;
  @include media(lg) {
    font-size: 36px;
  }
  @include media(xs) {
    font-size: 26px;
  }
}

h3,
.h3 {
  font-size: 24px;
  @include media(lg) {
    font-size: 22px;
  }
  @include media(xs) {
    font-size: 20px;
  }
}

h4,
.h4 {
  font-size: 22px;
  @include media(lg) {
    font-size: 20px;
  }
  @include media(xs) {
    font-size: 18px;
  }
}

h5,
.h5 {
  font-size: 20px;
}

h6,
.h6 {
  font-size: 18px;
}

b,
strong {
  font-weight: bolder;
}

small,
.small {
  font-size: 0.875em;
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

img,
svg {
  vertical-align: middle;
}

picture {
  width: 100%;

  img {
    display: block;
  }
}

.main {
  min-height: 100vh;
  display: flex;
  flex-wrap: wrap;
}

.app {
  width: 100%;
  margin-left: 68px;
  height: calc(100vh - 52px);
  background: $grayTone1;
  overflow-y: scroll;

  &.dark,
  &.white {
    transition: 0.3s ease-in, 0.3s ease-in-out;

    .app__inner {
      padding: 0;
    }
  }

  &.dark {
    background: #2f2e34;
  }

  &.white {
    background: $white;
  }

  &__inner {
    padding: 28px 32px;
    @include media(sm) {
      padding: 25px 16px;
      margin-bottom: 50px;
    }
    &.open {
      margin-left: 160px;

      @include media(md) {
        margin-left: 0;
      }
    }
    .no-padding {
      padding: 0 !important;
    }

    &.candidate {
      padding: 0;
    }
  }

  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  @include media(md) {
    margin-left: 0;
    background: $white;
  }
}

.workflow-select-option {
  a {
    width: 100%;
    font-size: 14px;
    padding: 8px 12px;
    background-color: #fff;
    color: $mainGreen;
    cursor: pointer;
    display: flex;
    box-sizing: border-box;
    margin: 5px 0;
    align-items: center;

    &:hover {
      background-color: #029ca51a;
    }
  }
}

.tagline {
  font-size: 14px;
  line-height: 24px;
  font-family: "Inter", "Poppins", sans-serif;
  font-weight: 500;
  color: $greenBlue1;
  margin-bottom: 12px;
  text-transform: uppercase;
  @include media(xs) {
    margin-bottom: 8px;
  }

  span {
    text-transform: lowercase;
  }
}

.button--empty,
.button--empty-disable {
  line-height: 1;
  padding: 13px 26px;
  color: $mainGreen;
  font-weight: 800;
  border-radius: 4px;
  font-size: 14px;
  border: 2px solid $mainGreen;
  transition: 0.3s ease-in, 0.3s ease-in-out;

  &:hover {
    color: $greenBlue2;
    border-color: $greenBlue2;
  }
}

.button--empty {
  cursor: pointer;
  &:disabled {
    cursor: not-allowed;
  }
}

.button--empty-disable {
  color: unset;
  border-color: unset;
  cursor: default;

  &:hover {
    color: unset;
    border-color: unset;
  }
}

.button--filled,
.button--filled-disable {
  line-height: 1;
  padding: 14px 28px;
  color: $white;
  font-weight: 700;
  font-size: 14px;
  border-radius: 4px;
  background: $greenButton;
  transition: 0.3s ease-in, 0.3s ease-in-out;

  &.flat{
    display: flex;
    flex-shrink: 0;
  }

  &:hover {
    background: $greenButtonHover;
  }
  &:disabled {
    background: $greenButtonDisabled;
  }
}

.button--filled {
  &:disabled {
    cursor: not-allowed;
  }
  transition: unset;
}
$greenbuttondisabled: #dff0f2;

.button--filled.button--filled-disable {
  background: $greenbuttondisabled;

  &:hover {
    border-color: unset;
  }}

.password-inner {
  position: relative;

  input {
    padding-right: 32px;
  }

  .password-type {
    width: 20px;
    height: 12px;
    position: absolute;
    right: 0;
    top: 0;
    transform: translate(-9px, 17px);
    cursor: pointer;
  }
}

.custom-checkbox {
  &__input {
    display: none;

    &:checked ~ label {
      .custom-checkbox__item {
        border-color: $mainGreen;
      }

      span {
        width: 10px;
        min-width: 10px;
        height: 7px;
        background-size: contain;
        display: block;
        background: url("../images/icons/done_ic.svg") no-repeat;
        background-size: contain;
      }
    }
  }

  &__label {
    display: flex;
    cursor: pointer;
    width: fit-content;
  }

  &__item {
    background: #fff;
    border: 1px solid #c1c5cb;
    box-sizing: border-box;
    border-radius: 2px;
    width: 16px;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-top: 3px;
  }

  &__text {
    font-size: 14px;
    margin-top: 2px;
    padding-left: 8px;
  }
}
