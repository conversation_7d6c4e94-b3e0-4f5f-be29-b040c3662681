@use './config' as *;
@use './mixins' as *;
.loader {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
}

@keyframes ldio-scm1tro0qmg {
  0% {
    transform: rotate(0)
  }
  100% {
    transform: rotate(360deg);
  }
}

.ldio-scm1tro0qmg > div {
  animation: ldio-scm1tro0qmg 1s infinite linear;
  transform-origin: 100px 100px;
}

.ldio-scm1tro0qmg > div div {
  position: absolute;
}

.ldio-scm1tro0qmg > div div:nth-child(1), .ldio-scm1tro0qmg > div div:nth-child(2) {
  width: 154px;
  height: 154px;
  border: 14px solid;
  border-radius: 50%;
  border-color: transparent #099c73 #099c73 #099c73;
  box-sizing: border-box;
  position: absolute;
  transform: rotate(45deg);
  transform-origin: 100px 100px;
}

.ldio-scm1tro0qmg > div div:nth-child(1) {
  transform: rotate(45deg) translate(23px, 23px);
}

.ldio-scm1tro0qmg > div div:nth-child(2) {
  transform: rotate(0deg) translate(23px, 23px);;
}

.ldio-scm1tro0qmg > div div:nth-child(3) {
  width: 0;
  height: 0;
  border: 18px solid;
  border-color: transparent transparent transparent #099c73;
  transform: translate(100px, 14px);
}

.loadingio-spinner-reload-tbhhuwfj8na {
  width: 200px;
  height: 200px;
  display: inline-block;
  overflow: hidden;
  background: #ffffff;
}

.ldio-scm1tro0qmg {
  width: 100%;
  height: 100%;
  position: relative;
  transform: translateZ(0) scale(1);
  backface-visibility: hidden;
  transform-origin: 0 0; /* see note above */
}

.ldio-scm1tro0qmg div {
  box-sizing: content-box;
}




