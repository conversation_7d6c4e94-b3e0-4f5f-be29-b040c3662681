import type { PayloadAction } from '@reduxjs/toolkit';
// eslint-disable-next-line no-duplicate-imports
import { createSlice } from '@reduxjs/toolkit';
import { IAssessmentsTable, IAssessmentsTableItem, IAssessmentsDateOfCreationItem } from '../../../types/redux/assessments';
import { AssessmentsTab } from '../../../enums/assessments/assessmentsEnums';
import { ITabsFilter } from '../../../types/global/global';

export const defaultFiltersAssessments = {
  searchValue: '',
  sortBy: 'assessmentId',
  sortType: 'asc',
  assessmentType: [],
  status: [],
  dateOfCreation: {
    from: null,
    to: null,
    duration: 'All time' as IAssessmentsDateOfCreationItem['duration']
  },
};

const initialStateATM: IAssessmentsTable = {
  activeTab: 'allAssessments',
  isLoading: false,
  emptySearch: false,
  emptyTable: false,
  emptyFilter: false,
  filters: defaultFiltersAssessments,
  tableItems: [],
  fixedTab: {
    id: 1,
    displayName: AssessmentsTab.NAME,
    active: true,
    dbName: ''
  },
  tabFilter: [
    {
      id: 2,
      displayName: AssessmentsTab.ID,
      active: true,
      dbName: ''
    },
    {
      id: 3,
      displayName: AssessmentsTab.TYPE,
      active: true,
      dbName: ''
    },
    {
      id: 4,
      displayName: AssessmentsTab.STATUS,
      active: true,
      dbName: ''
    },
    {
      id: 5,
      displayName: AssessmentsTab.LANGUAGE_ID,
      active: false,
      dbName: ''
    },
    {
      id: 6,
      displayName: AssessmentsTab.PACKAGE_ID,
      active: false,
      dbName: ''
    },
    {
      id: 7,
      displayName: AssessmentsTab.CREATED_AT,
      active: true,
      dbName: ''
    },
    {
      id: 8,
      displayName: AssessmentsTab.UPDATED_AT,
      active: false,
      dbName: ''
    },
  ],
  pagination: {
    currentPage: 1,
    limit: 10,
    totalCount: 0,
    afterKeyForNextPage: null
  },
  filterInfo: {
    assessmentTypeList: [],
    statusList: [],
    tabs: [
      AssessmentsTab.TYPE,
      AssessmentsTab.STATUS,
      AssessmentsTab.CREATED_AT,
    ],
  },
};

export const assessments = createSlice({
  name: 'assessments',
  initialState: initialStateATM,
  reducers: {
    setActiveTabAssessments: (state, action: PayloadAction<string>) => {
      state.activeTab = action.payload;
    },
    setIsLoadingAssessments: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setTableItemsAssessments: (state, action: PayloadAction<Array<IAssessmentsTableItem>>) => {
      state.tableItems = action.payload;
    },
    setSearchValueAssessments: (state, action: PayloadAction<string>) => {
      state.filters.searchValue = action.payload;
    },
    setOrderAssessments: (state, action: PayloadAction<Array<ITabsFilter>>) => {
      state.tabFilter = action.payload;
    },
    setLimitAssessments: (state, action: PayloadAction<number>) => {
      state.pagination.limit = action.payload;
      state.pagination.currentPage = 1;
    },
    setSortByFilterAssessments: (state, action: PayloadAction<string>) => {
      state.filters.sortBy = action.payload;
    },
    setSortTypeFilterAssessments: (state, action: PayloadAction<string>) => {
      state.filters.sortType = action.payload;
    },
    setCurrentPageAssessments: (state, action: PayloadAction<number>) => {
      state.pagination.currentPage = action.payload;
    },
    setTotalCountAssessments: (state, action: PayloadAction<number>) => {
      state.pagination.totalCount = action.payload;
    },
    setEmptySearchAssessments: (state, action: PayloadAction<boolean>) => {
      state.emptySearch = action.payload;
    },
    setEmptyTableAssessments: (state, action: PayloadAction<boolean>) => {
      state.emptyTable = action.payload;
    },
    setEmptyFilterAssessments: (state, action: PayloadAction<boolean>) => {
      state.emptyFilter = action.payload;
    },
    setAssessmentTypeFilterAssessments: (state, action: PayloadAction<Array<any>>) => {
      state.filters.assessmentType = action.payload;
    },
    setStatusFilterAssessments: (state, action: PayloadAction<Array<any>>) => {
      state.filters.status = action.payload;
    },
    setDateOfCreationFilterAssessments: (state, action: PayloadAction<IAssessmentsDateOfCreationItem>) => {
      state.filters.dateOfCreation = action.payload;
    },
    setAssessmentTypeListFilterAssessments: (state, action: PayloadAction<Array<any>>) => {
      state.filterInfo.assessmentTypeList = action.payload;
    },
    setStatusListFilterAssessments: (state, action: PayloadAction<Array<any>>) => {
      state.filterInfo.statusList = action.payload;
    },
    setAfterKeyForNextPageAssessments: (state, action: PayloadAction<any>) => {
      state.pagination.afterKeyForNextPage = action.payload;
    },
  },
});

export const {
  setActiveTabAssessments,
  setEmptyTableAssessments,
  setEmptySearchAssessments,
  setTotalCountAssessments,
  setCurrentPageAssessments,
  setAssessmentTypeFilterAssessments,
  setStatusFilterAssessments,
  setSearchValueAssessments,
  setOrderAssessments,
  setLimitAssessments,
  setSortTypeFilterAssessments,
  setSortByFilterAssessments,
  setTableItemsAssessments,
  setDateOfCreationFilterAssessments,
  setEmptyFilterAssessments,
  setAssessmentTypeListFilterAssessments,
  setStatusListFilterAssessments,
  setIsLoadingAssessments,
  setAfterKeyForNextPageAssessments,
} = assessments.actions;

export default assessments.reducer;
