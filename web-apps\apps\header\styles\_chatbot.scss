@use './mixins' as *;
@use './config' as *;

.position-workflow-table {
    position: relative;
}

.workflow-creation-bot {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    visibility: hidden;
    opacity: 0;
    transition: visibility 0s linear 0.3s, opacity 0.3s ease;

    &.show {
        visibility: visible;
        opacity: 1;
        transition-delay: 0s;
    }

    .modal-backdrop {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: -1;
    }

    .chatbot-content {
        background: $white;
        border: 1px solid $grayTone2;
        border-radius: 5px;
        width: 700px;
        max-height: 80vh;
        overflow-y: auto;
        position: relative;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);

        // Custom scrollbar styling to match sidebar navigation
        scrollbar-width: auto; // Firefox - shows the default scrollbar

        &::-webkit-scrollbar {
            width: 8px; // typical scrollbar width
        }

        &::-webkit-scrollbar-thumb {
            background-color: #099c73;
            border-radius: 4px;
        }

        &::-webkit-scrollbar-track {
            background: transparent; // optional
        }

        @include media(sm) {
            width: 330px;
            max-height: 90vh;
        }
    }

    .react-chatbot-kit-chat-container {
        width: 100%;
        display: block;

        // Custom scrollbar styling to match sidebar navigation
        scrollbar-width: auto; // Firefox - shows the default scrollbar

        &::-webkit-scrollbar {
            width: 8px; // typical scrollbar width
        }

        &::-webkit-scrollbar-thumb {
            background-color: #099c73;
            border-radius: 4px;
        }

        &::-webkit-scrollbar-track {
            background: transparent; // optional
        }

        .react-chatbot-kit-chat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;

            .react-chatbot-kit-chat-header-close-chat-button {
                width: 20px;
            }
        }

        .react-chatbot-kit-user-avatar-container {
            background-color: #856f7c;
        }

        .react-chatbot-kit-chat-bot-avatar-container {
            background-color: transparent !important;
            border: none !important;

            .react-chatbot-kit-chat-bot-avatar,
            .react-chatbot-kit-chat-bot-avatar-letter {
                display: none !important;
            }
        }

        .react-chatbot-kit-chat-bot-message {
            margin-left: 20px;
            width: auto;
            max-width: 500px;

            @include media(sm) {
                width: 230px;
            }
        }

        .react-chatbot-kit-chat-bot-message-loading-dots {
            animation: blink 1s infinite;
            color: #099C73;
            width: 10px;
            height: 10px;
        }

        .react-chatbot-kit-chat-input {
            background-color: $white;
        }

        .dynamic-options {
            width: 70%;
            margin-left: 20px;
        }

        // Apply scrollbar styling to any scrollable elements within chatbot
        .react-chatbot-kit-chat-message-container,
        .react-chatbot-kit-chat-inner-container,
        div[style*="overflow"] {
            scrollbar-width: auto; // Firefox - shows the default scrollbar

            &::-webkit-scrollbar {
                width: 8px; // typical scrollbar width
            }

            &::-webkit-scrollbar-thumb {
                background-color: #099c73;
                border-radius: 4px;
            }

            &::-webkit-scrollbar-track {
                background: transparent; // optional
            }
        }
    }

    // Global scrollbar styling for all scrollable elements within the modal
    * {
        scrollbar-width: auto; // Firefox - shows the default scrollbar

        &::-webkit-scrollbar {
            width: 8px; // typical scrollbar width
        }

        &::-webkit-scrollbar-thumb {
            background-color: #099c73;
            border-radius: 4px;
        }

        &::-webkit-scrollbar-track {
            background: transparent; // optional
        }
    }
}

.chatbot-avatar-container {
    position: fixed;
    bottom: 40px;
    right: 40px;
    z-index: 10000; /* Above modal */
    display: block !important;
    visibility: visible !important;
}

.chatbot-avatar {
    width: 50px;
    height: 50px;
    background: transparent;
    cursor: pointer;
    display: flex !important;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease;
    visibility: visible !important;

    &:hover {
        transform: scale(1.05);
    }

    &.widget-button {
        width: 50px;
        height: 50px;
        background: transparent;
    }

    img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        display: block !important;
        visibility: visible !important;
    }
}

/* Ensure consistent avatar styling in modal - FORCE OVERRIDE */
.react-chatbot-kit-chat-container .react-chatbot-kit-chat-bot-avatar-container {
    background: transparent !important;
    border: none !important;
    width: auto !important;
    height: auto !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Hide ALL default react-chatbot-kit avatars */
.react-chatbot-kit-chat-container .react-chatbot-kit-chat-bot-avatar-container .react-chatbot-kit-chat-bot-avatar,
.react-chatbot-kit-chat-container .react-chatbot-kit-chat-bot-avatar-container .react-chatbot-kit-chat-bot-avatar-letter,
.react-chatbot-kit-chat-container .react-chatbot-kit-chat-bot-avatar-container > div:not(.chatbot-avatar) {
    display: none !important;
    visibility: hidden !important;
}

/* Force our custom avatar to show */
.react-chatbot-kit-chat-container .react-chatbot-kit-chat-bot-avatar-container .chatbot-avatar {
    width: 50px !important;
    height: 50px !important;
    border-radius: 50% !important;
    background: $white !important;
    border: 2px solid $mainGreen !important;
    padding: 8px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 !important;
    visibility: visible !important;
    position: relative !important;
    z-index: 10 !important;
}

.react-chatbot-kit-chat-container .react-chatbot-kit-chat-bot-avatar-container .chatbot-avatar img {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain !important;
    display: block !important;
    border-radius: 0 !important;
    visibility: visible !important;
}

.chatbot-options {
    margin-left: 20px;
    display: flex;
    flex-direction: column;
    row-gap: 10px;

    &__item {
        width: 84%;
        padding: 12px 16px;
        background-color: $white;
        border: 1px solid $mainGreen;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 500;
        color: $mainGreen;
        cursor: pointer;
        text-align: left;
        transition: background-color 0.2s ease;
        margin-left: 39px;

        &:hover {
            background-color: $mainGreen;
            color: $white;
        }
    }

    &__subitem {
        width: 78%;
        padding: 12px 16px;
        background-color: $white;
        border: 1px solid $mainGreen;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 500;
        color: $mainGreen;
        cursor: pointer;
        text-align: left;
        transition: background-color 0.2s ease;
        margin-left: 60px;

        &:hover {
            background-color: $mainGreen;
            color: $white;
        }
    }
}