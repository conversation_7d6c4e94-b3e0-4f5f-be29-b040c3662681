import axios from 'axios';
import { getConfig } from '@ucrecruits/globalstyle/src/ucrecruits-globalstyle';
import { getEnv } from '@urecruits/api';

const { API_RECRUITMENT } = getEnv();

export const fetchJobSuggestions = async (message, sessionId) => {
    try {
        const response = await axios.post(
            `${API_RECRUITMENT}/api/job/suggestions`,
            JSON.stringify({ userInput: message, sessionId }),
            getConfig()
        );
        return response.data;
    } catch (error) {
        console.error("Error fetching job details:", error);
        throw new Error("Failed to fetch job details.");
    }
};

export const formatSalary = (amount) => {
    if (!amount || isNaN(amount)) return '-';
    const num = Math.round(Number(amount));
    if (num >= 1000) {
        const inK = num / 1000;
        return `${inK % 1 === 0 ? inK.toFixed(0) : inK.toFixed(1)}K`;
    }
    return num.toString();
};

export const extractJobDetails = (jobDetails,chatJobData) => {
  // Extract about company details
  let updatedAboutCompany = {
    publicSearch: true,
    ...(jobDetails.linkedIn && { linkedIn: jobDetails.linkedIn }),
    ...(jobDetails.facebook && { facebook: jobDetails.facebook }),
    ...(jobDetails.instagram && { instagram: jobDetails.instagram }),
    ...(jobDetails.twitter && { twitter: jobDetails.twitter }),
    ...(jobDetails.aboutCompany && { aboutCompany: jobDetails.aboutCompany })
  };

  // Extract benefits - handle nested benefitsList structure
  let updatedBenefits = !!jobDetails.benefits?.length ? jobDetails.benefits?.map(i => ({ id: Date.now(), value: i })) : chatJobData.benefits?.benefitsList || [];

  // Extract job details
  const updatedJobDetails = {
    // Use original job title if available, otherwise use the title from API response
    ...(chatJobData.originalJobTitle ? { jobTitle: chatJobData.originalJobTitle } : (jobDetails.title && { jobTitle: jobDetails.title })),
    ...(!!jobDetails.salaryMonthMin && { salaryRangeMonth: [jobDetails.salaryMonthMin, jobDetails.salaryMonthMax ?? chatJobData.jobDetails.salaryRangeMonth?.[1] ?? 0] }),
    ...(!!jobDetails.salaryMonthMax && { salaryRangeMonth: [jobDetails.salaryMonthMin ?? chatJobData.jobDetails.salaryRangeMonth?.[0] ?? 0, jobDetails.salaryMonthMax] }),
    ...(!!jobDetails.salaryYearMin && { salaryRangeYear: [jobDetails.salaryYearMin, jobDetails.salaryYearMax ?? chatJobData.jobDetails.salaryRangeYear?.[1] ?? 0] }),
    ...(!!jobDetails.salaryYearMax && { salaryRangeYear: [jobDetails.salaryYearMin ?? chatJobData.jobDetails.salaryRangeYear?.[0] ?? 0, jobDetails.salaryYearMax] }),
    ...(!!jobDetails.salaryHourMin && { salaryRangeHour: [jobDetails.salaryHourMin, jobDetails.salaryHourMax ?? chatJobData.jobDetails.salaryRangeHour?.[1] ?? 0] }),
    ...(!!jobDetails.salaryHourMax && { salaryRangeHour: [jobDetails.salaryHourMin ?? chatJobData.jobDetails.salaryRangeHour?.[0] ?? 0, jobDetails.salaryHourMax] }),
    ...(jobDetails.description && { jobDescription: jobDetails.description }),
    ...(jobDetails.shortDescription && { shortJobDescription: jobDetails.shortDescription }),
    ...(jobDetails.employer && { employer: jobDetails.employer }),
    ...(jobDetails.remoteLocation && { remoteLocation: jobDetails.remoteLocation }),
    ...(jobDetails.position && { position: jobDetails.position }),
    ...(jobDetails.industry && { industryType: jobDetails.industry }),
  };

  // Extract requirements
  let updatedRequirements = {
    ...(!!jobDetails.skills?.length && { skills: jobDetails.skills }),
    ...(!!jobDetails.experienceMin && { experienceYears: [jobDetails.experienceMin, jobDetails.experienceMax ?? chatJobData.requirements.experienceYears?.[1] ?? 1] }),
    ...(!!jobDetails.experienceMax && { experienceYears: [jobDetails.experienceMin ?? chatJobData.requirements.experienceYears?.[0] ?? 1, jobDetails.experienceMax] }),
    ...(jobDetails.education && { education: { value: jobDetails.education, label: jobDetails.education } })
  };

  return {
    updatedJobDetails,
    updatedRequirements,
    updatedAboutCompany,
    updatedBenefits
  };
};