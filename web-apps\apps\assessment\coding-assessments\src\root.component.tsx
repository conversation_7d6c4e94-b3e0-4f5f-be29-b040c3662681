import { CodingAssessments } from "../components/CodingAssessments";
import {
  LocationProvider,
  createHistory
} from "@reach/router";
import {
  BrowserRouter,
  Routes,
  Route,
  Navigate
} from 'react-router-dom';
import "../styles/main.scss";
import LiveTask from "../components/tasks/LiveTask";
import HomeTask from "../components/tasks/HomeTask";
import { Provider } from "react-redux";
import store from "../config/store";
import { permissionRoutesType, UnAuthorizePageComponent, useHasPermission, NotFoundPageComponent, useSubscription, UpgradeSubscriptionComponent, NoActiveSubscriptionComponent } from "@ucrecruits/globalstyle/src/ucrecruits-globalstyle";

const routes: permissionRoutesType[] = [
  {
    path: "/coding-assessments",
    element: <CodingAssessments />,
    module: "assessment",
    permissions: ["view","add"],
    planPackages:['assessment','fullcycle']
  },
  {
    path: "/coding-assessments/live-task",
    element: <LiveTask />,
    module: "assessment",
    permissions: "add",
    planPackages: ['assessment', 'fullcycle']
  },
  {
    path: "/coding-assessments/live-task/:id",
    element: <LiveTask />,
    module: "assessment",
    permissions: "edit",
    planPackages: ['assessment', 'fullcycle']
  },
  {
    path: "/coding-assessments/take-home-task",
    element: <HomeTask />,
    module: "assessment",
    permissions: "add",
    planPackages: ['assessment', 'fullcycle']
  },
  {
    path: "/coding-assessments/take-home-task/:id",
    element: <HomeTask />,
    module: "assessment",
    permissions: "edit",
    planPackages: ['assessment', 'fullcycle']
  }
]
export default function Root(props) {
  const history = createHistory(window);
  const { onAuthorizedRoutes, companyId } = useHasPermission()
  const { isLoadingSubscription, checkCompanySubscription, hasCompanySubscriptionElement, checkCurrentPackageType } = companyId && useSubscription();
  
  const isCompanySubscription = companyId && checkCompanySubscription();
  return (
    <>
      <LocationProvider history={history}>
        <Provider store={store}>
          <BrowserRouter>
            <Routes>
              {companyId && routes.map(route => {
                const isAuthorized = route.planPackages
                  ? checkCurrentPackageType(route.planPackages) &&
                  onAuthorizedRoutes(route.module, route.permissions, route?.option || "")
                  : onAuthorizedRoutes(route.module, route.permissions, route?.option || "");

                const routeElement = isAuthorized ? route.element : route.planPackages ? <UpgradeSubscriptionComponent/> : <UnAuthorizePageComponent />;

                return <Route path={route.path} key={route.path} element={hasCompanySubscriptionElement(routeElement)} />;
              })}
                <Route path='*' element={companyId ?
                  (isLoadingSubscription ?
                     <h1>Loading...</h1> 
                     : (isCompanySubscription 
                      ? (onAuthorizedRoutes(routes[0].module, routes[0].permissions) 
                        ? <Navigate to={routes[0].path}/> 
                        : <NotFoundPageComponent/>) 
                      : <NoActiveSubscriptionComponent/>)) 
                  : <NotFoundPageComponent/> 
                } />
            </Routes>
          </BrowserRouter>
        </Provider>
      </LocationProvider>
    </>
  );
}