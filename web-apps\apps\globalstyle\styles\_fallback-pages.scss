@use './config' as *;
@use './mixins' as *;
.fallback {
  &__page {
    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(100vh - 108px);

    &__inner {
      background: $white;
      border: 1px solid $grayTone2;
      border-radius: 12px;
      padding: 56px 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      max-width: 940px;
      width: 100%;
      margin: 24px;

      @include media(md) {
        margin: 24px 16px;
        padding: 32px 24px;
      }
    }

    &__content {
      max-width: 392px;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    &__img {
      max-width: 340px;
      width: 50vw;
      margin-bottom: 32px;
    }

    &__headline {
      font-size: 28px;
      font-family: 'Poppins', sans-serif;
      color: $black;
      font-weight: 500;
      margin-bottom: 24px;
      text-align: center;

      @include media(md){
        font-size: 20px;
      }
    }

    &__description {
      text-align: center;
      color: $grayTone7;
      font-size: 16px;
      margin-bottom: 10px;

      @include media(xs) {
        margin-bottom: 32px;
      }
    }

    &__button {}
  }
}