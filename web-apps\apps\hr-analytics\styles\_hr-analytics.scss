@use "config" as *;
@use "mixins" as *;

.jobs-report {
  &__popup-item {
    width: 100%;
    margin-bottom: 25px;
    position: relative;

    &:last-child {
      margin-bottom: 0;
    }

    &--small {
      width: calc(50% - 14px);

      @include media(xs) {
        width: 100%;
      }
    }

    &--middle {
      width: calc(70% - 14px);

      @include media(xs) {
        width: 100%;
      }
    }

    &--no-margin {
      margin-bottom: 0;
    }

    &--extra-small {
      width: calc(30% - 14px);

      @include media(xs) {
        width: 100%;
      }
    }

    &__error-message {
      @include error-message;
      display: flex;
    }

    &__input {
      @include input;
    }


    &__label {
      @include label;
    }
  }
}

.hr-analytics{
  &__table-screen-top {
    display: flex;
    justify-content: space-between;
    align-items: start;
      margin-bottom: 28px;
      flex-direction: column;

  
    @include media(xs) {
      flex-direction: column;
      align-items: flex-start;
      margin-bottom: 16px;
    }
  
    &__breadcrumbs {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
  
      &__item {
        padding: 0 12px 0 6px;
        position: relative;
        display: flex;
        align-items: center;
  
        &:first-child {
          padding-left: 0;
        }
  
        &:last-child {
          &:after {
            display: none;
          }
        }
  
        &:after {
          content: "/";
          position: absolute;
          right: 0;
          font-size: 14px;
          font-weight: 500;
          color: $black;
        }
      }
  
      &__link {
        font-size: 14px;
        font-weight: 500;
        color: $black;
  
        &:hover {
          text-decoration: underline;
          color: $mainGreen;
        }
  
        &.disabled {
          color: $grayTone5;
          cursor: not-allowed;
  
          &:hover {
            text-decoration: none;
          }
        }
      }
    }
  
    &__title {
      @include media(sm) {
        font-size: 20px;
      }
  
      @include media(xs) {
        margin-bottom: 10px;
      }
    }
  
    &__wrapper {
      display: flex;
      align-items: center;
    }
  
    &__buttons-group {
      margin-left: 40px;
      display: flex;
      align-items: center;
  
      @include media(md) {
        display: none;
      }
  
      &__item {
        padding: 12px 20px;
        border-radius: 4px;
        font-size: 14px;
        line-height: 1;
        border: 1px solid #DFE2E6;
        background: $white;
        transition: .3s ease-in, .3s ease-in-out;
        margin-right: 24px;
        color: $grayTone7;
  
        &:last-child {
          margin-right: 0;
        }
  
        &.active {
          color: $greenBlue2;
          background: #ACD8D1;
          border: 1px solid #ACD8D1;
        }
      }
    }
  
    &__button {
      @include media(xs) {
        margin: 0 auto;
        width: 100%;
      }
    }
  }
  
}
.popup-right-side {
  position: fixed;
  background: rgba(0, 0, 0, .5411764705882353);
  width: 100%;
  height: 100%;
  z-index: 1000;
  left: 0;
  top: 0;
  display: flex;
  justify-content: flex-end;
  overflow: hidden;

  &__inner {
    background: $white;
    max-width: 464px;

    width: 100%;
    height: 100%;

    @include media(xs) {
      max-width: unset;
      overflow-y: scroll;

      &::-webkit-scrollbar {
        display: none;
      }
    }
  }

  &__top {
    position: sticky;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: .3s ease-in, .3s ease-in-out;
    padding: 32px 32px 31px;
    border-bottom: 1px solid rgba(255, 255, 255, 0);

    @include media(xs) {
      padding: 16px 16px 15px;
      position: fixed;
      background: $white;
      width: 100%;
      z-index: 21;
    }

    &--active {
      border-bottom: 1px solid $grayTone2;
      box-shadow: 0 4px 9px rgb(7 7 12 / 8%);
    }

    &__close-button {
      width: 24px;
      height: 24px;
      cursor: pointer;
    }

    &__title {
      color: $grayTone7;
      font-weight: 600;
      line-height: 1;
    }
  }

  &__form {
    width: 100%;
    display: flex;
    padding: 4px 11px 32px 30px;
    overflow: hidden;
    height: calc(100vh - 89px);
    flex-direction: column;

    @include media(xs) {
      padding: 60px 14px 16px;
      overflow: unset;
      height: auto;
    }

    &__inner {
      justify-content: space-between;
      padding: 0 21px 0 2px;
      flex-wrap: wrap;
      display: flex;
      overflow-y: auto;
      scrollbar-width: thin;

      &::-webkit-scrollbar {
        width: 3px;

        @include media(xs) {
          display: none;
        }
      }

      @include media(xs) {
        scrollbar-color: unset;
        scrollbar-width: unset;
        padding: 0 2px;
      }

      &::-webkit-scrollbar-track {
        background: $grayTone2;
      }

      &::-webkit-scrollbar-thumb {
        background: $greenBlue1;
        width: 3px;
        border-radius: 2px;
        transition: .3s;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: $greenBlue2;
      }
    }
  }

  &__buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 7px 0 16px 0;
    width: 100%;
    position: relative;

    @include media(xs) {
      margin: 19px 0 27px 0;
    }

    &__cancel {
      margin-right: 20px;
    }
  }


}

.jobs-report__react-select-options {
  width: 100%;
  position: relative;

  label {
    background: $white;
    border: 1px solid $grayTone3;
    box-sizing: border-box;
    border-radius: 2px;
    width: 16px;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  input {
    display: none;
  }

  input:checked+label,
  label.active {
    background: $white;
    border-color: $mainGreen;
  }

  input:checked~label span,
  label.active span {
    background: url("../image/icon/done_ic.svg") no-repeat;
    width: 10px;
    min-width: 10px;
    height: 7px;
    background-size: contain;
    display: block;

    @include media(xs) {
      width: 13px;
      height: 10px;
    }
  }

  p {
    padding-left: 23px;
    position: absolute;
    width: calc(100% - 23px);
    margin-top: 1px;
  }
}

.helper-icon {
  position: relative;
  display: flex;

  img {
    cursor: pointer;
    width: 18px;
    height: 18px;
    margin-left: 8px;

    &:hover+.helper-icon__info {
      display: block;
    }
  }

  &__info {
    position: absolute;
    display: none;
    bottom: 100%;

    &--text {
      position: absolute;
      width: 332px;
      background: $white;
      border-radius: 6px;
      padding: 8px 16px;
      box-shadow: 0 0 8px rgba(13, 16, 37, 0.15);
      font-size: 14px;
      color: $grayTone7;
      transform: translate(-20px, -14px);
      bottom: 100%;
      z-index: 1;

      @include media(lg) {
        width: 250px;
        left: -35px;
      }
    }

    &--square {
      position: absolute;
      display: block;
      width: 24px;
      height: 24px;
      background: $white;
      box-shadow: 0 0 8px rgba(13, 16, 37, 0.15);
      transform: rotate(45deg) translate(-14px, -26px);
    }
  }
}

.calendar-popup {
  position: fixed;
  background: rgba(0, 0, 0, .5411764705882353);
  width: 100%;
  height: 100%;
  z-index: 1000;
  left: 0;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  &__past {
    background: $grayTone1;
    cursor: default;
  }

  &__lane,
  &__lane-label {
    font-size: 14px;

  }

  &__lane {}

  &__lane-label {
    padding: 20px 6px 20px 0 !important;

    &>div {
      text-align: center !important;
    }
  }

  &__now {
    background-color: white !important;
    color: $mainGreen !important;
    font-weight: 500 !important;
  }

  &__inner {
    background: $white;
    width: 80%;
    height: 80%;
    border-radius: 12px;
    padding: 25px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    @include media(xs) {
      max-width: unset;
      overflow-y: scroll;
      height: 100%;
      width: 100%;
      border-radius: 0;

      &::-webkit-scrollbar {
        display: none;
      }
    }
  }
}

.choose-date {
  &__inner {}

  &__text {
    font-size: 14px;
    line-height: 1;
    color: $grayTone7;
    margin-bottom: 6px;

    input {
      color: $grayTone4;
      margin-left: 4px;
      pointer-events: none;
    }
  }

  &__button {
    margin-top: 6px;
  }
}