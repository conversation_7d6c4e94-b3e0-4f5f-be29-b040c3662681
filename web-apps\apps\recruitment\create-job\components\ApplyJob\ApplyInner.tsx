import { memo, useLayoutEffect, useState } from "react";
import { ApplyScreenEnum } from "../../store/apply-redux-enums";
import { useTypedSelector } from "../../hook/useTypedSelector";
import ApplicationInner from "./ApplicationInner";
import axios from "axios";
import { store } from "../../store";
import {
    changeSuccessfullyPopup,
    setApplicationForm,
    setCurrentApplicationScreen,
    setCurrentScreen,
    setUserInfo,
} from "../../store/apply-action-creators";
import { useNavigate, useParams } from "react-router-dom";
import Summary from "./Summary";
import HeaderApply from "./Header";
import { decodeToken, getEnv } from "@urecruits/api";
import SuccessfullyPopup from "./Popups/SuccessfullyPopup";
import ReturnToJobPopup from "./Popups/ReturnToJobPopup";
import { AuthGuard, getConfig } from "@ucrecruits/globalstyle/src/ucrecruits-globalstyle";
import SmallLoader from "@ucrecruits/globalstyle/components/Table/SmallLoader";

const ApplyInner = () => {
  const screen = useTypedSelector((state) => state.apply);
  const errorArray = useTypedSelector((state) => state.apply.errorArray);
  const jobId = useParams().value;
  const navigate = useNavigate();
  const [jobTitle, setJobTitle] = useState(" ");
  const [isApplied, setIsApplied] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { API_RECRUITMENT } = getEnv();

  // Extract and handle URL parameters
  const extractUrlParameters = () => {
    try {
      const currentUrl = window.location.href;
      const urlObj = new URL(currentUrl);
      const params = new URLSearchParams(urlObj.search);

      // Extract all parameters
      const allParams = {};
      params.forEach((value, key) => {
        allParams[key] = value;
      });

      // Check if we have parameters
      if (params.size > 0) {
        // Extract specific tracking parameters
        const trackingData = {
          applicant_guid: params.get('applicant_guid'),
          source: params.get('source'),
          utm_source: params.get('utm_source'),
          utm_medium: params.get('utm_medium'),
          utm_campaign: params.get('utm_campaign'),
          _jtochash: params.get('_jtochash'),
          _jtocprof: params.get('_jtocprof'),
          jobId: jobId,
          allParams: allParams,
          originalUrl: currentUrl,
          timestamp: new Date().toISOString()
        };

        // Store in localStorage for persistence across authentication
        localStorage.setItem('jobApplyParams', JSON.stringify(trackingData));

        // Log to console using the logging service
        console.group('🎯 Job Apply Parameters - Component Load');
        console.log('📋 Summary:', {
          jobId: trackingData.jobId,
          applicant_guid: trackingData.applicant_guid,
          source: trackingData.source,
          timestamp: trackingData.timestamp
        });

        if (trackingData.applicant_guid) {
          console.log('🆔 Applicant GUID:', trackingData.applicant_guid);
        }

        if (trackingData.source) {
          console.log('📍 Source:', trackingData.source);
        }

        if (trackingData.utm_source || trackingData.utm_medium || trackingData.utm_campaign) {
          console.log('📊 UTM Parameters:', {
            utm_source: trackingData.utm_source,
            utm_medium: trackingData.utm_medium,
            utm_campaign: trackingData.utm_campaign
          });
        }

        if (trackingData._jtochash || trackingData._jtocprof) {
          console.log('🎯 JobTarget Parameters:', {
            _jtochash: trackingData._jtochash,
            _jtocprof: trackingData._jtocprof
          });
        }

        console.log('🔗 Original URL:', trackingData.originalUrl);
        console.log('📦 All Parameters:', trackingData.allParams);
        console.groupEnd();

        return trackingData;
      }

      // Check if we have stored parameters from previous session
      const storedParams = localStorage.getItem('jobApplyParams');
      if (storedParams) {
        const parsedParams = JSON.parse(storedParams);
        console.group('🎯 Job Apply Parameters - Retrieved from Storage');
        console.log('📋 Summary:', {
          jobId: parsedParams.jobId,
          applicant_guid: parsedParams.applicant_guid,
          source: parsedParams.source,
          timestamp: parsedParams.timestamp
        });

        if (parsedParams.applicant_guid) {
          console.log('🆔 Applicant GUID:', parsedParams.applicant_guid);
        }

        if (parsedParams.source) {
          console.log('📍 Source:', parsedParams.source);
        }

        if (parsedParams.utm_source || parsedParams.utm_medium || parsedParams.utm_campaign) {
          console.log('📊 UTM Parameters:', {
            utm_source: parsedParams.utm_source,
            utm_medium: parsedParams.utm_medium,
            utm_campaign: parsedParams.utm_campaign
          });
        }

        if (parsedParams._jtochash || parsedParams._jtocprof) {
          console.log('🎯 JobTarget Parameters:', {
            _jtochash: parsedParams._jtochash,
            _jtocprof: parsedParams._jtocprof
          });
        }

        console.log('🔗 Original URL:', parsedParams.originalUrl);
        console.log('📦 All Parameters:', parsedParams.allParams);
        console.groupEnd();
        return parsedParams;
      }

      return null;
    } catch (error) {
      console.error('Error extracting URL parameters:', error);
      return null;
    }
  };
 
  const getData=async()=>{
    setIsLoading(true)
    try {
      const user=await decodeToken();
      
      await  axios(`${API_RECRUITMENT}/api/job/public-job/${jobId}?currentUserId=${user?.["https://urecruits.com/userId"]||0}`, getConfig()).then((res) => {
        if (res.data) {
const jobData=res?.data?.subscribes?.length ? res?.data?.subscribes?.find((res)=>res.jobId === jobId):{};
          const hasAppliedJob=jobData ? jobData?.applyJob : false;
          if(hasAppliedJob){
            store.dispatch(changeSuccessfullyPopup(true));
            setIsApplied(true)
          }else{
            setIsApplied(false)
          }
          const form = Array.isArray(res.data?.applicationForm) ? res.data?.applicationForm.filter((x) => x.activeTab) : [];
          setJobTitle(res.data.title);
          if(form){
            store.dispatch(setApplicationForm(form));
         
          }
          if(form?.length){
            store.dispatch(setCurrentApplicationScreen(form[0].name));
           
          }else{
            store.dispatch(setCurrentScreen(ApplyScreenEnum.SUMMARY))
          }
        } else {
          //if no page found
          navigate("/");
        }
      });
     await axios(`${API_RECRUITMENT}/api/candidate`, getConfig()).then((res) => {
        store.dispatch(
          setUserInfo({
            firstname: res.data.user.firstname,
            middlename: res.data.user.middlename,
            lastname: res.data.user.lastname,
            phone: res.data.user.phone,
            avatar: res.data.user.avatar,
            email: res.data.user.email,
            industries: res.data.industries.map((item) => item.label),
            position: res.data.position?.label,
            experience: res.data.experience,
            location: res.data?.location?.city
              ? `${res.data?.location?.city}, ${res.data?.location?.state}`
              : "",
            ctc: res.data.currentCtc,
          })
        );
      });
    } catch (error) {
      console.log(error,"error");
      
    }
    finally{

      setIsLoading(false)
    }
 
  }

  useLayoutEffect(() => {
    // Extract URL parameters first
    const extractedParams = extractUrlParameters();

    // Clean up URL parameters from address bar after extraction
    if (extractedParams && window.history.replaceState) {
      const urlObj = new URL(window.location.href);
      const cleanUrl = `${urlObj.origin}${urlObj.pathname}`;
      window.history.replaceState({}, document.title, cleanUrl);
    }

    getData();
  }, []);

  return (
    <AuthGuard module='candidate'>
    <section className="job">
      <HeaderApply title={jobTitle} />
      {errorArray.length > 0 && (
        <div className={"apply_error"}>
          <svg
            width="18"
            height="19"
            viewBox="0 0 18 19"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
              d="M9.00195 17C13.1441 17 16.502 13.6421 16.502 9.5C16.502 5.35786 13.1441 2 9.00195 2C4.85982 2 1.50195 5.35786 1.50195 9.5C1.50195 13.6421 4.85982 17 9.00195 17Z"
              stroke="#FE4672"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M9 6.5V9.5"
              stroke="#FE4672"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M9 12.5H9.00833"
              stroke="#FE4672"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <p>{errorArray.length} unfilled questions</p>
        </div>
      )}
      <div className="job__steps">
        {isLoading ?<SmallLoader/>:
        <>
        {screen.currentScreen === ApplyScreenEnum.APPLICATION && (
          <ApplicationInner />
        )}
        {screen.currentScreen === ApplyScreenEnum.SUMMARY && <Summary />}</>}
      </div>
      {screen.successfullyAppliedPopup && (
        <SuccessfullyPopup jobId={jobId} jobTitle={jobTitle}  isApplied={isApplied}/>
      )}
      {screen.returnToJobPopup && <ReturnToJobPopup />}
    </section>
    </AuthGuard>
  );
};
export default memo(ApplyInner);
