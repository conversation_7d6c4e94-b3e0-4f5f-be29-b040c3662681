@use "config" as *;
@use "mixins" as *;
@use "sass:color";
.header {
  width: 100%;
  height: 52px;

  > div {
    width: 100%;
  }

  &__inner {
    background: $white;
    box-shadow: 0 4px 9px rgba(7, 7, 12, 0.08);
    min-height: 52px;
    width: 100%;
    display: flex;
    align-items: center;
    position: fixed;
    top: 0;
    z-index: 4;
    @include media(lg) {
      padding: 0 16px;
    }

    @include media(xs) {
      position: fixed;
      left: 0;
      top: 0;
      z-index: 18;
    }

    &.active{
      box-shadow: none;
    }
  }

  &__logo {
    display: block;
    min-width: 68px;
    @include media(lg) {
      display: none;
    }

    &__icon {
      padding: 0 16px;
    }
  }

  &__left {
    display: none;
    @include media(lg) {
      display: flex;
      align-items: center;
    }

    &__link {
      color: $mainGreen;
      font-weight: 800;
      font-size: 14px;
      text-transform: uppercase;
      margin-left: 20px;
    }
  }

  &__burger {
    width: 16px;
    height: 16px;
    position: relative;
    cursor: pointer;
    display: none;
    @include media(lg) {
      display: block;
    }

    span {
      position: absolute;
      border-radius: 2px;
      transition: 0.3s cubic-bezier(0.8, 0.5, 0.2, 1.4);
      width: 100%;
      height: 2px;
      transition-duration: 500ms;
      background: $grayTone5;

      &:nth-child(1) {
        top: 0;
        right: 0;
        width: 100%;
      }

      &:nth-child(2) {
        top: 7px;
        right: 0;
        opacity: 1;
        width: 100%;
      }

      &:nth-child(3) {
        bottom: 0;
        right: 0;
        width: 100%;
      }
    }
  }

  &__burger.active {
    span {
      &:nth-child(1) {
        transform: rotate(45deg);
        top: 7px;
      }

      &:nth-child(2) {
        opacity: 0;
      }

      &:nth-child(3) {
        transform: rotate(-45deg);
        top: 7px;
      }
    }
  }

  &__nav {
    padding: 0 32px;
    @include media(lg) {
      display: none;
    }

    &__list {
      display: flex;
    }

    &__item {
      position: relative;
      padding: 15px 0;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: 0.3s ease-in, 0.3s ease-in-out;
      margin-right: 32px;

      &:last-child {
        margin-right: 0;
      }

      &:hover {
        .header__nav__name {
          color: $mainGreen;
        }

        .header__nav__item--arrow {
          path {
            fill: $mainGreen;
          }
        }

        .header__sublist {
          display: block;
        }
      }

      &--arrow {
        margin-left: 5px;
        width: 10px;
        min-width: 10px;
        height: 7px;

        path {
          transition: 0.3s ease-in, 0.3s ease-in-out;
        }
      }
    }

    &__name {
      font-size: 14px;
      font-weight: 800;
      color: $grayTone4;
      text-transform: uppercase;
      white-space: nowrap;
      transition: 0.3s ease-in, 0.3s ease-in-out;
    }
  }

  &__sublist {
    display: none;
    position: absolute;
    top: 100%;
    width: 100%;
    padding-top: 0px;
    z-index: 10;

    &__inner {
      padding: 0;
      background: $white;
      box-shadow: 0 6px 12px rgb(7 7 12 / 8%);
      z-index: 10;
      min-width: 174px;
      width: 100%;
      border-radius: 6px;
      border: 1px solid #d3dce6;
      overflow: hidden;
    }

    &__link {
      color: $black;
      font-size: 14px;
      padding: 10px 15px 10px 20px;      ;
      width: 100%;
      display: block;
      background: $white;
      transition: 0.3s ease-in, 0.3s ease-in-out;

      &:hover {
        // background: $grayTone1;
        background: color.adjust($mainGreen, $alpha: -0.70);
      }
    }
  }

  &__action {
    margin-left: auto;
    display: flex;
    align-items: center;
    padding-right: 32px;
    @include media(lg) {
      padding-right: 0;
    }
  }

  &__notification {
    margin-right: 16px;
    cursor: pointer;
    position: relative;

    &__count {
      background-color: red;
      color: white;
      border-radius: 50%;
      padding: 1px 6px;
      position: absolute;
      top: -10px;
      right: -5px;
      font-size: 12px;
    }

    &__body {
      height: 300px;
      width: 400px;
      position: absolute;
      top: 80%;
      right: 90px;
      padding: 20px 0;
      background: $white;
      box-shadow: 0 6px 12px rgba(7, 7, 12, 0.08);
      border-radius: 4px;
      border: 1px solid $grayTone2;
      transform: translate(24px, 19px);
      z-index: 10;
      @include media(lg) {
        transform: translate(8px, 19px);
      }
      @include media(md) {
        right: 20px;
        width: 325px;
      }
      &__list {
        height: 215px;
        overflow-y: auto;
      }

      &__head {
        color: $black;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px;

        &.link {
          color: $mainGreen;
          font-size: 12px;
          // align-self: flex-end;
          padding: 0 5px;
          &:hover {
            text-decoration: underline;
          }
        }

        &.title {
          font-weight: 800;
        }
      }
    }

    &__item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: "Inter", "Avenir LT Std", sans-serif;
      padding: 10px;
      border-bottom: 1px solid $grayTone3;
      font-size: 12px;

      &.new {
        background-color: $grayTone1;
        &:hover {
          background-color: $white;
        }
      }

      &__content {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 5px;
        color: $black;
      }
      &__info{
        display: flex;
        justify-content: space-between;
        gap: 10px;
      }

      &__image {
        width: 30px;
        height: 30px;
        border-radius: 50%;
      }
      &__name {
        font-size: 14px;
        color: $black;
        padding-right: 12px;
        line-height: normal;
        &:hover {
          text-decoration: underline;
          color: $mainGreen;
        }
      }

      &__description{
        line-height: normal;
        color: $grayTone5;
        font-size: 12px;
        margin-bottom: 8px;
      }
      &__time{
        line-height: normal;
        color: $grayTone5;
        font-size: 12px;
      }


      &__icon {
        width: 20px;
        min-width: 20px;
        height: 20px;
        object-fit: contain;
      }

      &__actions {
        width: 5px;
        height: 5px;
        position: relative;
        border: 1px solid $grayTone5;
        padding: 10px;
        border-radius: 50%;
        margin: 5px;
        cursor: pointer;
        &.read {
          border-color: $mainGreen;
        }

        .dot {
          position: absolute;
          top: 5px;
          right: -5px !important;
          width: 10px;
          height: 10px;
          background-color: $mainGreen;
          border-radius: 50%;
          cursor: pointer;
        }
      }
    }
  }

  &__user {
    position: relative;

    &__head {
      display: flex;
      align-items: center;
      cursor: pointer;
      position: initial;
    }

    &__avatar {
      margin-right: 12px;
      width: 28px;
      min-width: 28px;
      height: 28px;
      object-fit: contain;
      border-radius: 50%;
    }

    &__arrow {
      width: 10px;
      min-width: 9px;
      height: 6px;
    }
  }

  &__setting {
    position: absolute;
    width: 200px;
    right: 0;
    top: 100%;
    padding: 0;
    background: $white;
    box-shadow: 0 6px 12px rgba(7, 7, 12, 0.08);
    border-radius: 4px;
    border: 1px solid $grayTone2;
    transform: translate(24px, 19px);
    z-index: 10;
    @include media(lg) {
      transform: translate(8px, 19px);
    }

    &__link {
      cursor: pointer;
      padding: 16px 20px;
      display: flex;
      justify-content: space-between;
      transition: 0.3s ease-in, 0.3s ease-in-out;

      &:hover {
        background: $grayTone1;
      }
    }

    &__name {
      font-size: 14px;
      color: $black;
      padding-right: 12px;
    }

    &__icon {
      width: 20px;
      min-width: 20px;
      height: 20px;
      object-fit: contain;
    }
  }
}
