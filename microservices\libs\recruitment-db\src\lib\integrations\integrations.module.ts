import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Integrations } from './integrations.model';
import { IntegrationsService } from './integrations.service';
import { GmailAuthService } from './gmail/auth.service';
import { GcalAuthService } from './gcal/auth.service';
import { OutlookAuthService } from './outlook/auth.service';
import { McalAuthService } from './mcal/auth.service';
import { MulterModule } from "@nestjs/platform-express";
import { IntegrationAuthModule } from "../integrationAuth/integrationAuth.module";
import { IntegrationAuthService } from '../integrationAuth/integrationAuth.service';
import { HellosignService } from "./hellosign/hellosign.service";
import { HellosignIntegrationModule } from "../hellosign/hellosign-integration.module";
import { UniversalService } from "./universal/universal.service";
import { HttpModule } from "@nestjs/axios";
import {UniversalModule} from "../universal/universal.module";

@Module({
  imports: [
    SequelizeModule.forFeature([Integrations]),
    MulterModule.register({
      dest: "./uploads",
    }),
    IntegrationAuthModule,
    HellosignIntegrationModule,
    HttpModule,
  ],
  providers: [
    IntegrationsService,
    GmailAuthService,
    GcalAuthService,
    OutlookAuthService,
    McalAuthService,
    IntegrationAuthService,
    HellosignService,
  ],
  exports: [
    SequelizeModule,
    IntegrationsService,
    HellosignService,
  ],
})
export class IntegrationsModule {}
