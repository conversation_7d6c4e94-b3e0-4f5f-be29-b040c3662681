import { DomainAssessment, DomainFilterDto, DomainQuestions, DomainQuestionsService } from "@microservices/db";
import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query,Logger } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { AuthUser, Permissions, PermissionsGuard } from "@microservices/auth";
import { AuthGuard } from "@nestjs/passport";
import {HttpService} from "@nestjs/axios";
import {getProgress} from "apps/temporal/src/app/workflow/temporal/workflows";
import {connectToTemporal} from "apps/temporal/src/app/workflow/workflow.provider";


//   companyId: user["https://urecruits.com/companyId"],
//   tenantId: user["https://urecruits.com/tenantId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   userId: user["https://urecruits.com/userId"],

@ApiTags("Domain")
@Controller('domain-questions')
@ApiBearerAuth("access-token")
export class DomainQuestionsController {
  constructor(
    private readonly questionsService: DomainQuestionsService,
    private readonly httpService: HttpService) {}


  @ApiOperation({ summary: 'Get all domain assessment with pagination' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200})
  @Get("all")
  @Permissions("recruiter",'assessment:view')
  findAll(@AuthUser() user: any,@Query() query:any) {
    return this.questionsService.findAllAssessment(user["https://urecruits.com/companyId"],query);
  }

  @ApiOperation({ summary: 'Create Domain Assessment' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 201 })
  @Post()
  @Permissions("recruiter",'assessment:edit')
  create(@Body() createQuestionsDto: DomainAssessment, @AuthUser() user: any) {
    createQuestionsDto.companyId = user["https://urecruits.com/companyId"];
    return this.questionsService.create(createQuestionsDto, +user["https://urecruits.com/userId"]);
  }

  @ApiOperation({ summary: 'Delete domain assessment' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200 })
  @Delete(':id')
  @Permissions("recruiter",'assessment:edit')
  remove(@Param('id') id: string, @AuthUser() user: any) {
    return this.questionsService.remove(+id, +user["https://urecruits.com/companyId"], +user["https://urecruits.com.userId"]);
  }

  @ApiOperation({ summary: 'Update domain assessment' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200 })
  @Patch(':id')
  @Permissions("recruiter",'assessment:edit')
  update(@Param('id') id: string, @Body() updateQuestionsDto: DomainAssessment, @AuthUser() user: any) {
    return this.questionsService.update(+id, updateQuestionsDto, +user["https://urecruits.com/companyId"], +user["https://urecruits.com.userId"]);
  }

  @ApiOperation({ summary: 'Update instruction in domain assessment' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200 })
  @Patch('instruction/:id')
  @Permissions("recruiter")
  updateInstruction(@Param('id') id: string, @Body() updateQuestionsDto, @AuthUser() user: any) {
    return this.questionsService.updateInstruction(+id, updateQuestionsDto, +user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Get domain assessment by id' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200, type: DomainAssessment })
  @Get(':id')
  @Permissions("recruiter",'assessment:view')
  findOne(@Param('id') id: string, @AuthUser() user: any) {
    return this.questionsService.findOneById(+id, +user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Get domain assessment by id for candidate' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200, type: DomainAssessment })
  @Get('candidate/:id')
  @Permissions("candidate")
  async findById(@Param('id') id: string, @Query() query:any, @AuthUser() user: any) {
    const candidateId=user["https://urecruits.com/userId"]
    const jobId = query?.jobId;
    let balanceDuration;
      const url = `${process.env.RECRUITMENT_API_URI}/api/temporal-workflow?jobId=${jobId}&id=${candidateId}`;
      let response
      try {
        const res = await this.httpService.get(url).toPromise();
        response = res?.data;
      } catch (error) {
        console.error("Error fetching data:", error);
      }

      const client = await connectToTemporal();
      if (client && response) {
        const handle = await client.getHandle(response?.workflowid);
        balanceDuration = await handle.query(getProgress);
      } else {
        Logger.log(
          "Getting issue to connect the client in domain result"
        );
      }
    const data = await this.questionsService.findById(+id);

    const result = {
      ...data?.dataValues,
      balanceDuration
      }
    return result;
  }

  @ApiOperation({ summary: 'Get all domain assessment with pagination' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200, type: DomainQuestions })
  @Get()
  @Permissions("recruiter",'assessment:view')
  findALl(@Query() query:any, @AuthUser() user: any) {
    return this.questionsService.findAllWithFilter(query, +user["https://urecruits.com/companyId"]);
  }


  @ApiOperation({ summary: 'Get assessment duration' })
  @ApiResponse({ status: 200, type: DomainQuestions })
  @Get('duration/:id')
  getAssessmentDuration(@Param('id') assessmentId:string){
      return this.questionsService.getAssessmentDuration(+assessmentId);
  }

  @ApiOperation({ summary: 'Create dummy domain assessment' })
  @ApiResponse({ status: 200 })
  @Post('dummy')
  createDummyAssessment(@Body() dto:any) {
    if(dto.companyId){
      return this.questionsService.createDummyAssessment(dto.companyId);
    }
  }
}
