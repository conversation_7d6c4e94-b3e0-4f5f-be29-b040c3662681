import { ApiProperty } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class TemplateDto {
  @ApiProperty({ example: "1", description: "Template ID" })
  templateId: string;

  @ApiProperty({ example: "1", description: "Template name" })
  templateName: string;

  @ApiProperty({ example: "1", description: "Template Description" })
  @IsOptional()
  templateDescription: string;

  @ApiProperty({ example: "1", description: "Template Body" })
  templateBody: string;

  @ApiProperty({ example: "1", description: "Template Description" })
  signers: any;
}
