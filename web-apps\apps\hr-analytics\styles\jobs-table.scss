@use "config" as *;
@use "mixins" as *;
.jobs-table {
  &__async-wrap{
    width: 100%;
  }
  &__column {
    &__small {
      width: 200px;
    }

    &__default {
      width: 250px;
    }

    &__middle {
      width: 280px;
    }
  }

  &__status-button {
    margin: 12px 6px 0 6px;
    cursor: pointer;
    padding: 12px 20px;
    transition: .3s ease-in, .3s ease-in-out;
  }

  &__status {
    padding: 9px 12px !important;
    margin-left: -6px;
    line-height: 1.3;
    margin-bottom: 3px;
    @include media(md) {
      margin-left: 0;
    }
  }

  &__locations {
    display: flex;
    flex-direction: column;

    &__remote {
      margin-top: 6px;
    }
  }

  &__text {


    &__capitalize {
      text-transform: capitalize;
    }

    &__salary {
      display: flex;
      align-items: center;

      span {
        margin-left: 4px;
      }
    }

    &__no-overflow {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  &__cw{
    display: flex;
    flex-direction: column;
    width: 100%;

    &__top-wrap{
      display: flex;
      align-items: center;
      width: 100%;
    }
    &__img{
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
    &__desc{
      width: calc(100% - 24px);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      text-align: left;
      font-size: 14px;
      line-height: 1;
      color:$grayTone6;
    }
    &__date{
      font-size: 12px;
      line-height: 16px;
      color: $grayTone5;
      margin-top: 4px;
      margin-left: 24px;
    }
  }
}