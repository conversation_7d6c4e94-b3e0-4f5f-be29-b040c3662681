@use "config" as *;
@use "mixins" as *;
.switcher {
  display: flex;
  align-items: center;
  margin: 0 16px 0 0;

  @include media(md) {
    margin: 0 32px 16px 0;
  }

  &:last-child {
    margin-right: 0;
  }

  &__body {
    width: 56px;
    height: 20px;
    border: 1px solid $grayTone2;
    margin-right: 8px;
    position: relative;
    border-radius: 36px;

    &:after {
      content: "";
      position: absolute;
      width: 24px;
      height: 24px;

      top: 0;
      background: $grayTone4;
      border-radius: 50%;
      transform: translate(-30px, -3px);
      right: 0;
      transition: .3s ease-in, .3s ease-in-out;
    }


    &.active {
      &:after {
        background: $mainGreen;
        transform: translate(2px, -3px);
      }
    }
  }


}