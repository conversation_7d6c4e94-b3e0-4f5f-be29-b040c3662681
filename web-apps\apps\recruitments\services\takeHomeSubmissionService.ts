import { getEnv } from "@urecruits/api";
import fetchData, { patchData, postData } from "../hook/http";
import { ITakeHomeSubmission, ISubmissionReviewData } from "../types/redux/review-and-score";

const { API_RECRUITMENT } = getEnv();

export interface CreateTakeHomeSubmissionDto {
  jobId: number;
  candidateId: number;
  assessmentId: number;
  assignmentId?: number;
  duration?: string;
  language?: string;
  submittedAt?: Date;
}

export class TakeHomeSubmissionService {
  /**
   * Create a new take-home submission
   */
  static async createSubmission(submissionData: CreateTakeHomeSubmissionDto): Promise<ITakeHomeSubmission | null> {
    try {
      const response = await postData(`${API_RECRUITMENT}/api/take-home-submissions`, submissionData);
      return response?.data || null;
    } catch (error) {
      console.error('Error creating take-home submission:', error);
      throw error;
    }
  }

  /**
   * Get take-home submission by candidate and job
   */
  static async getSubmissionByCandidate(candidateId: string, jobId: string): Promise<ITakeHomeSubmission | null> {
    try {
      const response = await fetchData(`${API_RECRUITMENT}/api/take-home-submissions/candidate/${candidateId}/job/${jobId}`);
      return response?.data || null;
    } catch (error) {
      console.error('Error fetching submission by candidate:', error);
      return null;
    }
  }

  /**
   * Get submission details for review
   */
  static async getSubmissionForReview(submissionId: string): Promise<ITakeHomeSubmission | null> {
    try {
      const response = await fetchData(`${API_RECRUITMENT}/api/take-home-submissions/${submissionId}/review-details`);
      return response?.data || null;
    } catch (error) {
      console.error('Error fetching submission for review:', error);
      return null;
    }
  }

  /**
   * Update submission feedback and score
   */
  static async updateSubmissionFeedback(
    submissionId: string,
    reviewData: Partial<ISubmissionReviewData>
  ): Promise<boolean> {
    try {
      const response = await patchData(`${API_RECRUITMENT}/api/take-home-submissions/${submissionId}/feedback`, reviewData);
      return response?.success || false;
    } catch (error) {
      console.error('Error updating submission feedback:', error);
      return false;
    }
  }

  /**
   * Get all take-home submissions for review list
   */
  static async getSubmissionsForReview(params: {
    limit: number;
    offset: number;
    search?: string;
    assessmentType?: string;
  }): Promise<{ data: ITakeHomeSubmission[]; total: number }> {
    try {
      const queryParams = new URLSearchParams({
        limit: params.limit.toString(),
        offset: params.offset.toString(),
        ...(params.search && { search: params.search }),
        ...(params.assessmentType && { assessmentType: params.assessmentType })
      });

      const response = await fetchData(`${API_RECRUITMENT}/api/take-home-submissions?${queryParams}`);
      return {
        data: response?.data || [],
        total: response?.total || 0
      };
    } catch (error) {
      console.error('Error fetching submissions for review:', error);
      return { data: [], total: 0 };
    }
  }

  /**
   * Check if a submission exists for candidate and job
   */
  static async hasSubmission(candidateId: string, jobId: string): Promise<boolean> {
    try {
      const submission = await this.getSubmissionByCandidate(candidateId, jobId);
      return submission !== null;
    } catch (error) {
      console.error('Error checking submission existence:', error);
      return false;
    }
  }

  /**
   * Get submission statistics
   */
  static async getSubmissionStats(submissionId: string): Promise<{
    totalQuestions: number;
    completedQuestions: number;
    totalTestCases: number;
    passedTestCases: number;
    overallSuccessRate: number;
  } | null> {
    try {
      const submission = await this.getSubmissionForReview(submissionId);
      if (!submission) return null;

      return {
        totalQuestions: submission.testCaseSummary.totalQuestions,
        completedQuestions: submission.questionSubmissions.length,
        totalTestCases: submission.testCaseSummary.totalTestCases,
        passedTestCases: submission.testCaseSummary.totalPassedTestCases,
        overallSuccessRate: submission.testCaseSummary.overallSuccessRate
      };
    } catch (error) {
      console.error('Error fetching submission stats:', error);
      return null;
    }
  }
}
