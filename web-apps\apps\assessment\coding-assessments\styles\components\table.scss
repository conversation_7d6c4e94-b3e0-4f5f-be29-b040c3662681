@use "../config" as *;
@use "../mixins" as *;

.table-ui {
    height: 100%;
    width: 100%;
    position: relative;

  &__header {
    height: 52px;
    padding: 0 67px 0 17px;
    background: white;
    border-right: 1px solid $grayTone2;
    border-left: 1px solid $grayTone2;
    cursor: pointer;
    z-index: 8;
  }

  &__header-name {
    font-family: "Inter", "Avenir LT Std", sans-serif;
    font-size: 14px;
    font-weight: 400;
    color: $grayTone7;
    padding-left: 15px;
    vertical-align: middle;
    &__img {
      width: 6px;
    }
  }

  &__header-button {
    display: flex;
    align-items: center;
  }

  &__header-shadow {
    transition: background 2s ease-out;
    &:after {
      content: "";
      position: absolute;
      top: 0;
      bottom: 0;
      left: 100%;
      width: 13px;
      pointer-events: none;
      background: linear-gradient(90deg, rgba(88, 88, 88, 0.1) 0%, rgba(255, 255, 255, 0.1) 100%);
    }
  }

  &__row {
    display: table-row;
    height: 60px;
    border-right: 1px solid $grayTone2;
    border-left: 1px solid $grayTone2;
    &:last-child {
      padding-bottom: 131px;
    }
  }

  &__m-row {
    width: 100%;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    line-height: 3;
    button {
      width: 16px;
    }
  }

  &__row-m-content {
    margin-left: 20px;
  }

  &__row--white {
    background-color: $white;
  }

  &__row--gray {
    background-color: $grayTone1;
  }

  &__row-shadow {
    &:after {
      content: "";
      position: absolute;
      top: 0;
      bottom: 0;
      left: 100%;
      width: 13px;
      pointer-events: none;
      background: linear-gradient(90deg, rgba(88, 88, 88, 0.1) 0%, rgba(255, 255, 255, 0.1) 100%);
    }
  }

  &__row-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 175px;
    @include media(xs) {
      white-space: nowrap;
      text-overflow: unset;
      max-width: 100%;
    }
  }

  &__row-status-container {
    position: relative;
    height: 96px;
    width: 200px;
  }
  &__row-status-active {
    top: 0;
    left: 0;
    line-height: 30px;
    padding-left: 7px;
    vertical-align: baseline;
    width: 66px;
    height: 32px;
    margin-right: 60px;
    background: rgba(2, 156, 165, 0.1);
    border: 1px solid $greenBlue1;
    border-radius: 3px;
    color: $greenBlue1;
    @include media(xs) {
      line-height: 35px;
      padding-left: 5px;
      margin-bottom: 10px;
    }
  }

  &__row-status-draft {
    top: 0;
    left: 0;
    line-height: 30px;
    padding-left: 7px;
    vertical-align: baseline;
    width: 66px;
    height: 32px;
    margin-right: 60px;
    background: rgba(254, 70, 114, 0.1);
    border: 1px solid $red;
    border-radius: 3px;
    color: $red;
    @include media(xs) {
      line-height: 35px;
      padding-left: 5px;
      margin-bottom: 10px;
    }
  }

  &__row-tabindex {
    font-weight: 900;
    color: $grayTone7;
  }

  &__row--text {
    font-size: 14px;
    vertical-align: middle;
    padding: 16px 0 16px 16px;
  }

  &__row--green-text {
    color: $mainGreen;
  }

  &__row-icon {
    vertical-align: sub;
    min-width: 16px;
    max-width: 16px;
    cursor: pointer;
    transition: opacity .3s;
    margin-right: calc(45% - 5px);
    &:hover {
      opacity: .5;
    }
  }

  &__row-icon-pen {
    min-width: 20px;
    max-width: 20px;
    cursor: pointer;
    transition: opacity .3s;
    margin-right: calc(30% - 5px);
    &:hover {
      opacity: .5;
    }
  }

  &__row-m-icon-pen {
    min-width: 20px;
    max-width: 20px;
    cursor: pointer;
    transition: opacity .3s;
    &:hover {
      opacity: .5;
    }
  }

  &__row-icon-delete {
    min-width: 16px;
    cursor: pointer;
    transition: opacity .3s;
    &:hover {
      opacity: .5;
    }
  }

  &__results-article {
    padding-left: 20px;
    font-size: 12px;
    @include media(md) {
      padding-left: 0;
    }
  }

  &__results-article-delete{
    padding-left: 20px;
    font-size: 12px;
    @include media(xs) {
      padding-left: 0;
    }
  }

  &__icon-wrapper {
    vertical-align: middle;
    white-space: nowrap;
    min-width: 100px;
  }

  &__icon-m-wrapper {
    vertical-align: middle;
    white-space: nowrap;
    width: 100%;
    min-width: 100px;
  }

  &__pagination {
    display: flex;
    justify-self: center;
  }

  &__pagination-wrapper {
    font-size: 12px;
    background: #fff;
    height: 60px;
    border: 1px solid $grayTone2;
    padding: 4px 34px;
    position: fixed;
    border-radius: 0 0 12px 12px;
    justify-content: space-between;
    align-items: center;
    left: 100px;
    z-index: 1;
    right: 22px;
    width: calc(100% - 116px) !important;
    display: flex !important;
    bottom: calc(14% - 89px);
    @include media(xs) {
      left: 16px;
      width: calc(100% - 36px) !important;
      flex-wrap: wrap;
      justify-content: center;
      flex-direction: column;
    }
    @include media(sm) {
      padding: 4px 5px;
    }
  }

  &__pagination-wrapper-delete {
    font-size: 12px;
    align-items: center;
    background: #fff;
    height: 63px;
    border: 1px solid $grayTone2;
    padding: 0 24px;
    position: fixed;
    justify-content: space-between;
    left: 100px;
    z-index: 8;
    right: 22px;
    width: calc(100% - 116px) !important;
    display: flex !important;
    bottom: calc(14% - 31px);
    @include media(xs) {
      display: flex !important;
      left: 16px;
      width: calc(100% - 36px) !important;
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }

  &__pagination-wrapper-dropdown {
    padding-left: 20px;
  }

  &__pagination-wrapper-select {
    color: $mainGreen;
    cursor: pointer;
  }

  &__pagination-wrapper-content {
    border-bottom: none;
    display: flex;
    padding: 0;
    @include media(xs) {
      padding: 5px 30px 0 30px !important;
      border-bottom: none !important;
    }
  }
  &__pagination-wrapper-content-delete {
    border-bottom: none;
    display: flex;
    padding: 0;
    @include media(xs) {
      padding: 0 0 0 30px !important;
      border-bottom: none !important;
    }
  }

  &__pagination-container {
    border-bottom: none;
    padding: 0;
  }
  &__pagination-wrapper-dropdown-left {
    display: flex;
    align-items: center;
    margin-right: 12px;
    padding: 0;
  }

  &__pagination-wrapper-dropdown-right {
    display: flex;
    align-items: center;
    margin-left: 12px;
    padding: 0;
  }

  &__pagination-item {
    border-radius: 6px;
    text-align: center;
    margin: 0 5px 0;
    width: 24px;
    height: 24px;
    border: 1px solid $grayTone2;
    font-size: 12px;
  }

  &__pagination-breack {
    display: block;
    height: 10px;
    width: 10px;
    border: 1px solid $grayTone2;
  }

  &__pagination-link {
    display: inline-block;
    line-height: 23px;
    width: 20px;
    height: 24px;
    border-radius: 6px;
    text-align: center;
  }

  &__pagination-item--active {
    background: $mainGreen;
    color: #fff;
  }

  &__pagination-item--next {
    margin-left: 9px;
    line-height: 1.73;
  }

  &__pagination-item--previous {
    margin-right: 9px;
    line-height: 1.73;
  }
  &__pagination-item-disabled {
    filter: invert(1%) sepia(1%) saturate(0%) hue-rotate(311deg) brightness(140%) contrast(115%);
    a {
      img {
        cursor: auto !important;
        pointer-events: all !important;
      }
    }
  }
}

.css-1ygcj2i-MuiTableCell-root {
  font-family: "Inter", "Avenir LT Std", sans-serif !important;
  color: $grayTone7;
  font-weight: 900 !important;
}


//td tr:first-child + div {
//  box-shadow: none
//}
