@use "config" as *;
@use "mixins" as *;
.PhoneInput {
  display: flex;
  align-items: center;
  border: 1px solid #DFE2E6;
  box-sizing: border-box;
  border-radius: 4px;
  width: 100%;
  line-height: 1;
  padding: 13px 16px;
  font-size: 14px;
  color: #2A2C33;
}

.PhoneInputInput {
  flex: 1;
  min-width: 0;
  font-size: 14px;
  padding-left: 16px;
  color: #2A2C33;
}

.PhoneInputCountryIcon {
  width: calc(1em * 1.5);
  height: 1em;
}

.PhoneInputCountryIcon--square {
  width: 1em;
}

.PhoneInputCountryIconImg {
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 2px;
  box-shadow: 0 0 0 1px rgba(45, 44, 44, 0.17), inset 0 0 0 1px rgba(45, 44, 44, 0.17);
}

.PhoneInputInternationalIconPhone {
  opacity: 0.8;
}

.PhoneInputInternationalIconGlobe {
  opacity: 0.65;
}


.PhoneInputCountry {
  position: relative;
  align-self: stretch;
  display: flex;
  align-items: center;
  padding-right: 7px;
  &:after{
    content: "";
    position: absolute;
    top: -14px;
    right: 0;
    height: 44px;
    width: 1px;
    background: #DFE2E6;
  }
}

.PhoneInputCountrySelect {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 1;
  border: 0;
  opacity: 0;
  cursor: pointer;
}

.PhoneInputCountrySelect[disabled] {
  cursor: default;
}

.PhoneInputCountrySelectArrow {
  width: 20px;
  min-width: 20px;
  height: 14px;
  background: url("../image/icon/form-arrow_ic.svg") no-repeat;
  background-size: contain;
  background-position-x: center;
}

.PhoneInputCountrySelect:focus + .PhoneInputCountryIcon + .PhoneInputCountrySelectArrow {
  opacity: 1;
}

.PhoneInputCountrySelect:focus + .PhoneInputCountryIcon .PhoneInputInternationalIconGlobe {
  opacity: 1;
}
