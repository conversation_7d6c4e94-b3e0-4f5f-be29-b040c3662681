@use "config" as *;
@use "mixins" as *;
@mixin card {
  background: $white;
  padding: 24px 16px 24px 24px;
  border-radius: 12px;
  border: 1px solid $grayTone2;
  width: 100%;
}

@mixin link {
  font-size: 12px;
  color: $mainGreen;
  font-family: 'Poppins', sans-serif;
  line-height: 1;
  font-weight: 300;
  text-decoration: underline;
}

@mixin headline {
  color: $black;
  font-size: 20px;
  line-height: 1;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
}

@mixin status {
  border: 1px solid transparent;
  border-radius: 3px;
  color: $black;
  padding: 6px 8px;
  width: fit-content;
  line-height: 1;
  font-size: 14px;
  display: flex;
  align-items: center;
}

@mixin overflowY {
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

.jobs-report {
  &__inner {
    display: flex;
    flex-direction: column;

   &.screening {
      .jobs-report__top {
        display: flex;
        flex-wrap: wrap;
        width: 100%;

        &__left {
          width: calc(75% - 28px);
          margin-right: 28px;
          @include media(lg) {
            width: 100%;
          }
          @media only screen and (max-width: 1200px) {
            margin: 0;
          }
        }

        &__right {
          width: 25%;
          height: 480px;
          max-height: fit-content;
          @media only screen and (max-width: 1200px) {
            width: 100%;
            margin-bottom: 28px;
            height: 480px;
            max-height: fit-content;
          }
        }
      }

      .jobs-report__bottom {
        width: 100%;
        margin-top: 28px;
        display: flex;
        @include media(lg) {
          flex-wrap: wrap;
        }
      }
    }
  }

  &__headline {
    margin-bottom: 28px;
  }
}

.schedule {
  @include card;
  margin: 0 0 24px 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;


  &__head {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &__headline {
      @include headline;
    }

    &__link {
      @include link;
    }
  }


  &__inner {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-grow: 1;
  }

  &__empty {
    padding: 20px 16px;
    max-width: 220px;
  }
}

.greeting {
  @include card;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  margin-bottom: 28px;

  &__left {
    padding: 24px 0 24px 24px;
    width: calc(100% - 310px);
    @include media(md) {
      width: calc(100% - 149px);
    }
  }

  &__tagline {
    margin-bottom: 24px;
    color: $grayTone6;
    font-size: 14px;
    line-height: 1;
    font-weight: 400;
    @include media(sm) {
      font-size: 12px;
      margin-bottom: 8px;
    }
  }

  &__headline {
    font-family: 'Poppins', sans-serif;
    font-size: 28px;
    line-height: 1;
    font-weight: 500;
    color: $black;
    @include media(sm) {
      font-size: 16px
    }
  }


  &__right {
    &__img {
      width: 203px;
      object-fit: contain;
      margin-right: 107px;
      @include media(md) {
        margin-right: 24px;
        width: 125px;
      }
      @include media(xs) {
        margin-right: 8px;
      }
    }
  }
}

.jobs-report-table {
  background: $white;
  padding: 24px 24px 12px 24px;
  border-radius: 12px;
  border: 1px solid $grayTone2;
  width: 100%;

  &__header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;

    &__title {
      color: $black;
      font-size: 20px;
      line-height: 1;
      font-family: 'Poppins', sans-serif;
      padding-right: 16px;
      width: calc(100% - 68px);
      font-weight: 600;
    }

    &__link {
      display: block;
      font-size: 12px;
      color: $mainGreen;
      text-decoration: underline;
    }
  }

  &__select {
    margin-bottom: 18px;
    max-width: 240px;
    width: 100%;
    animation: 1.5s ease-in smooth;
  }

  &__table {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    overflow-x: auto;
    animation: 1.5s ease-in smooth;

    &::-webkit-scrollbar {
      height: 3px;
      cursor: pointer;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #888;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  }

  &__thead {
    display: flex;
    width: 100%;

    &__td {
      flex: 2 1 0;
      font-size: 14px;
      color: $grayTone7;
      font-weight: 900;
      min-width: 220px;
      padding: 6px 0;

      &.small {
        flex: 1 1 0;
      }

      &.large {
        min-width: 280px;
        flex: 3 1 0;
      }
    }
  }

  &__tbody {
    width: 100%;

    &__tr {
      width: 100%;
      min-width: fit-content;
      display: flex;
      align-items: center;
      border-bottom: 1px solid $grayTone2;
      padding: 14px 0;

      &:last-child {
        border-color: transparent;
        padding-bottom: 12px;
      }
    }

    &__td {
      flex: 2 1 0;
      font-size: 14px;
      color: $grayTone7;
      min-width: 220px;
      display: flex;
      align-items: center;

      &.small {
        flex: 1 1 0;
      }

      &.large {
        min-width: 280px;
        flex: 3 1 0;
      }

      &.gray {
        color: $grayTone5;
        font-weight: 400;
      }

      &.green {
        color: $mainGreen;
      }
    }

    &__icon {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      margin-right: 6px;
    }
  }

  &__link {
    color: $black;
    display: flex;
    align-items: center;
    transition: .3s ease-in, .3s ease-in-out;

    &:hover {
      text-decoration: underline;
      color: $mainGreen;
    }
  }

  &__status {
    border: 1px solid transparent;
    border-radius: 3px;
    color: $black;
    padding: 6px 8px;
    width: fit-content;
    line-height: 1;

    &.publish {
      background: rgba(2, 156, 165, 0.1);
      border-color: #029CA5;
      color: $greenBlue2;
    }
  }

  &__info {
    display: flex;
    margin-right: 32px;

    &__item {
      margin-right: 24px;
      display: flex;
      align-items: center;

      &:last-child {
        margin-right: 0;
      }

      img {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }

      span {
        min-width: 25px;
        font-weight: 900;
        color: $grayTone6;
        line-height: 1;
        display: block;
      }
    }
  }

  &__users {
    position: relative;
    display: flex;

    &__item {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      object-fit: contain;
      z-index: 1;


      &:nth-child(2) {
        transform: translateX(-10px);
        z-index: 2;
      }

      &:nth-child(3) {
        transform: translateX(-20px);
        z-index: 3;
      }

      img {
        width: 28px;
        height: 28px;
        object-fit: cover;
        border-radius: 50%;
      }
    }

    &__count {
      position: absolute;
      background: $mainGreen;
      z-index: 3;
      right: 0;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      border: 1px solid $white;
      transform: translateX(-3px);
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: content-box;

      span {
        font-size: 12px;
        color: $white;
        line-height: 1;
      }
    }
  }
}

.jobs-report-list {
  @include card;
  width: (calc(33.33% - 18.6px));

  @include media(lg) {
    width: (calc(50% - 14px));
  }

  @include media(md) {
    width: 100%;
  }

  &.left {
    margin: 0 28px 0 0;
    @include media(lg) {
      margin: 0 14px 28px 0;
    }
    @include media(md) {
      margin: 0 0 28px 0;
    }
  }

  &.middle {
    @include media(lg) {
      margin: 0 0 28px 14px;
    }
    @include media(md) {
      margin: 0 0 28px 0;
    }
  }

  &.right {
    margin: 0 0 0 28px;

    @include media(lg) {
      margin: 0 14px 0 0;
    }

    @include media(md) {
      margin: 0;
    }
  }

  &__head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    &__headline {
      @include headline;
    }

    &__link {
      @include link;
    }
  }

  &__list {
    animation: 1.5s ease-in smooth;
  }

  &__select {
    margin-bottom: 18px;
    max-width: 240px;
    width: 100%;
    animation: 1.5s ease-in smooth;
  }

  &__item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #E0E6ED;

    &:first-child {
      padding-top: 0;
    }

    &:last-child {
      border-bottom: unset;
    }

    &:hover {
      .jobs-report-list__item__arrow svg path {
        stroke: $mainGreen;
      }

      .jobs-report-list__item__username,
      .jobs-report-list__item__salary,
      .jobs-report-list__item__time {
        color: $mainGreen;
      }
    }

    &__inner {
      display: flex;
      flex-direction: column;
      width: calc(100% - 4px);
    }

    &__top {
      display: flex;
      align-items: center;
      margin-bottom: 22px;
    }

    &__avatar {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      object-fit: cover;
      margin-right: 12px;
    }

    &__username {
      color: $grayTone7;
      font-size: 14px;
      line-height: 1;
      font-weight: 500;
      margin-right: 12px;
      transition: .3s ease-in, .3s ease-in-out;
    }

    &__status {
      @include status;

      &.approved {
        background: rgba(2, 156, 165, 0.1);
        border-color: #029CA5;
        color: $greenBlue2;
      }
    }

    &__bottom {
      display: flex;
      align-items: center;
    }

    &__salary {
      padding-right: 16px;
      line-height: 1;
      color: $grayTone5;
      font-size: 14px;
      position: relative;
      display: flex;
      align-items: center;
      transition: .3s ease-in, .3s ease-in-out;

      &:after {
        content: "";
        position: absolute;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: $mainGreen;
        right: 0;
      }
    }

    &__time {
      line-height: 1;
      color: $grayTone5;
      font-size: 14px;
      padding-left: 8px;
      transition: .3s ease-in, .3s ease-in-out;
    }

    &__arrow {
      svg {
        path {
          transition: .3s ease-in, .3s ease-in-out;
        }
      }
    }
  }

}


@keyframes smooth {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}