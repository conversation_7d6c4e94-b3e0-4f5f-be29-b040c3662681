import {
  <PERSON><PERSON><PERSON>To,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { Candidate } from '../candidates/candidates.model'

interface ProjectAttrs {
  projectName?: string;
  dateStart?: string;
  dateEnd?: string;
  awardDate?: string;
  notes?: string;
  candidateId: number;
}

@Table({ tableName: 'projects' })
export class Project extends Model<Project, ProjectAttrs> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: 'Pet project',
    description: 'Project Name',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  projectName: string;

  @ApiProperty({
    example: '2021-12-28T22:00:00.000Z',
    description: 'Period Date start',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  dateStart: string;

  @ApiProperty({
    example: '2021-12-28T22:00:00.000Z',
    description: 'Period Date end',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  dateEnd: string;

  @ApiProperty({
    example: '2021-12-28T22:00:00.000Z',
    description: 'Award date',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  awardDate: string;

  @ApiProperty({
    example: 'Notes',
    description: 'Notes',
  })
  @Column({ type: DataType.TEXT, allowNull: true })
  notes: string;

  @ApiProperty({ example: '1', description: 'Candidate ID' })
  @ForeignKey(() => Candidate)
  @Column({ type: DataType.INTEGER, allowNull: false })
  candidateId: number;

  @BelongsTo(() => Candidate)
  candidate: Candidate;
}
