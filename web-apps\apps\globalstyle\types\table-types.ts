export interface ITheadItem {
	title: string
	handler?: (value: string) => void | null,
	dbName: string
}

export interface ICommonTheadItem {
	title: string;
	handler?: (field: string) => void;
	field?: string;
}

export interface ISearch {
	searchValue: string;
	placeholder: string;
	setSearchValue(value: string): void;
	emptySearch?: Omit<ITableEmpty, 'type'>;
}

export interface ICheckBox {
	status?: boolean
	addClass?: string
	setStatus(value: boolean): void
}
export interface ITableEmpty {
	type: 'emptyTable' | 'emptySearch' | 'emptyFilter';
	handler?: (pagination?: any) => void;
	desc?: string;
	title?: string;
	buttonText?: string;
}

export interface INoResultsSearch {
	desc?: string;
	title?: string;
	resetButtonText?: string;
	resetFilters?: () => void
}

export interface IPagination {
	filters?: any;
	limit: number;
	currentPage: number;
	totalCount: number;
	setTotalCount?: (count: number) => void;
	setCurrentPage(value: number, limit?: number, filters?: any): void;
	setLimit(value: number): void;
	showOption?: Array<ISelectOption>;
}

export interface IColumns {
	headerName: string;
	field: string;
	pinnable?: boolean;
	isMobileTitle?: boolean;
	renderCell?: (cellValue, rowData) => any;
	mobileRenderCell?: (cellValue, rowData) => any;
	setActions?: any;
	sort?: boolean;
	visibility?: boolean;
	className?: string;
}

export interface IDisableRow {
	text: string;
	getDisableStatus: (rowData) => boolean;
}

export interface ITableFilter {
	filterData: any;
	filter: any;
	resetFilters: (pagination: any) => void;
	pagination?: any;
	filterItems: (state: any) => JSX.Element;
	submitFunc?: (page: number, limit?: number, filters?: any) => void;
	emptyFilter?: Omit<ITableEmpty, 'type'>;
	isFilterSubmit: boolean;
	setIsFilterSubmit: (value: boolean) => void;
	isEmptyFilter?: boolean;
	setIsEmptyFilter?: (value: boolean) => void;
}
export interface ITableView {
	test?: any;
	tableViewType?: 'table' | 'list';
	refetchApi?: { refetch: boolean, setRefetch: any };
	columns: Array<IColumns>;
	emptyTable?: Omit<ITableEmpty, 'type'>;
	store?: any;
	storeName?: string;
	useTypedSelector?: any;
	disableRow?: IDisableRow;
	searchField?: ISearch;
	pagination?: IPagination;
	rearrangeColumns?: { setColumnOrder: any }
	tableListCard?: (IMobileTable) => any;
	filters?: ITableFilter;
	sort?: (field) => void;
	rowsData: { api: string; rowData?: never } | { rowData: any[]; api?: never };
	isLoading?: boolean;
}
export interface IMobileTable {
	columns: Array<IColumns>;
	rowData: any;
	disableRow?: IDisableRow;
	actions?: any;
}

export interface IMobileTableItem {
	columns: Array<IColumns>;
	row: any;
	disableRow?: IDisableRow;
	actions?: IMobileTable['actions'];
}
export interface ISelectOption {
	value: number
	label: string
}

export interface ITabsFilter {
	displayName: string,
	id: number,
	active: boolean,
	dbName: string
}
export interface IColReorder {
	headerName: string,
	id: number,
	visibility: boolean,
	pinnable?: boolean
}

