export default (array) => {
	return array.map(item => {
		switch (item.id) {
			case 75:
				return {...item, monaco: 'c'}
			case 48:
				return {...item, monaco: 'c'}
			case 49:
				return {...item, monaco: 'c'}
			case 50:
				return {...item, monaco: 'c'}
			case 76:
				return {...item, monaco: 'cpp'}
			case 52:
				return {...item, monaco: 'cpp'}
			case 53:
				return {...item, monaco: 'cpp'}
			case 54:
				return {...item, monaco: 'cpp'}
			case 86:
				return {...item, monaco: 'clojure'}
			case 51:
				return {...item, monaco: 'csharp'}
			case 57:
				return {...item, monaco: "elixir"}
			case 87:
				return {...item, monaco: "fsharp"}
			case 60:
				return {...item, monaco: "go"}
			case 62:
				return {...item, monaco: "java"}
			case 63:
				return {...item, monaco: "javascript"}
			case 78:
				return {...item, monaco: "kotlin"}
			case 64:
				return {...item, monaco: "lua"}
			case 79:
				return {...item, monaco: "objective-c"}
			case 67:
				return {...item, monaco: "pascal"}
			case 85:
				return {...item, monaco: "perl"}
			case 68:
				return {...item, monaco: "php"}
			case 70:
				return {...item, monaco: "python"}
			case 71:
				return {...item, monaco: "python"}
			case 80:
				return {...item, monaco: "r"}
			case 72:
				return {...item, monaco: "ruby"}
			case 81:
				return {...item, monaco: "scala"}
			case 82:
				return {...item, monaco: "sql"}
			case 83:
				return {...item, monaco: "swift"}
			case 74:
				return {...item, monaco: "typescript"}
			case 84:
				return {...item, monaco: "vb"}
			default: {
				return null
			}
		}
	}).filter(x => x)
}