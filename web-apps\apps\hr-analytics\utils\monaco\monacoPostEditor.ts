import fetchData, {deleteData, postData} from "../../hook/fetchData";
import {getEnv} from "@urecruits/api";

const {API_ASSESSMENT} = getEnv()
export const monacoRun = async (value: string, language_id: number) => {
	const source_code = window.btoa(value)
	const token = await postData(`${API_ASSESSMENT}/api/coding-area/run`, {language_id, source_code})

	const timeout = () => {
		return new Promise((resolve) => {
			setTimeout(async () => {
				const res = await fetchData(`${API_ASSESSMENT}/api/coding-area/${token.token}`)
				if (res?.status?.description === 'Processing' || res?.status?.description === 'In Queue') {
					resolve(timeout())
				} else {
					resolve(res);
				}
			}, 1000);
		});


	}
	const result = await timeout()
	await deleteData(`${API_ASSESSMENT}/api/coding-area/${token.token}`,{})
	return result
}