@use "config" as *;
@use "mixins" as *;
@mixin card {
  background: $white;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid $grayTone2;
  width: 100%;
  margin: 24px 0px;
}


.report {

  .report-screen-top__title {
    font-size: 36px;
    font-weight: 700;
    line-height: 36px;
    color: #2A2C33;
    margin-bottom: 32px
  }
  .report-wrapper{
    @include card;
    .report-inner-header {
      font-size: 20px;
    font-weight: 900;
    line-height: 20px;
    color: #2A2C33;
    margin-bottom: 16px;
    }

    .report-inner-card {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;


      @include media(xl) {
        grid-template-columns: repeat(3, 1fr);
      }

      @include media(md) {
        grid-template-columns: repeat(2, 1fr);
      }
    
      @include media(sm) {
        grid-template-columns: repeat(1, 1fr);
      }

    }
  }


  @mixin overflowY {
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #888;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  }
}

@keyframes smooth {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}