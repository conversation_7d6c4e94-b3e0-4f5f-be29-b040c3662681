@use "config" as *;
@use "mixins" as *;
.candidate-job-scoreboard{
  padding: 28px 32px;
  @include media(xs) {
    padding: 24px 16px;
  }

  &__head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28px;

    @include media(sm) {
      display: block;
    }

    &__left {
      display: flex;
      @include media(md) {
        flex-direction: column;
      }
    }

    &__headline {
      line-height: 1;
      margin-right: 40px;
    }

    &__right {
      display: flex;
    }
  }

  .candidate-table {
    &__column {
      &__small {
        width: 200px;
      }

      &__default {
        width: 250px;
      }

      &__middle {
        width: 280px;
      }
    }

    &__status {
        padding: 4px 8px;
        border-radius: 3px;      
        @include media(md){
          font-size: 14px;
          width: min-content;
        }
    }

    &__title{
      display: flex;
      gap: 8px;
      align-items: center;
    }

    &__link {
      font-size: 12px;
      color: $mainGreen;
      font-weight: 900;

      &:hover {
        text-decoration: underline;
      }
    }

    &__text {
      color: $grayTone7;
      font-size: 14px;
      font-weight: 500;


      &.gray {
        color: $grayTone5;
      }

      &.green {
        color: $mainGreen;
      }

      &.with-dotted {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        display: block;
      }
    }

    &__company {
      width: 100%;
      display: flex;
      align-items: center;

      &__avatar {
        width: 28px;
        height: 28px;
        object-fit: cover;
        border-radius: 50%;
        margin-right: 6px;
      }

      &__text {
        font-size: 14px;
        color: $grayTone7;
        line-height: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        display: flex;
        align-self: center;
      }
    }

    &__location {
      width: 100%;

      &__value {
        font-size: 14px;
        color: $grayTone7;
        line-height: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        display: block;
      }

      &__remove {
        color: $grayTone5;
        font-size: 14px;
        font-weight: 500;
      }
    }

    &__actions {
      display: flex;
      align-items: center;

      &__item {
        font-size: small;
        margin-right: 28px;
        cursor: pointer;
        display: flex;

        &.save {
          transition: .3s ease-in, .3s ease-in-out;

          &:hover {
            path {
              stroke: $mainGreen;
            }
          }
        }

        &.active {
          path {
            stroke: $mainGreen;
          }
        }

        &:last-child {
          margin-right: 0;
        }

        &.disabled {
          pointer-events: none;

          svg {
            path, circle {
              stroke: $grayTone5;
            }
          }
        }
      }
    }
  }
}