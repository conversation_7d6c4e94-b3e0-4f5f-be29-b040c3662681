import {
  Column,
  DataType,
  Model,
  Table,
  <PERSON><PERSON><PERSON>,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { Company } from '../companies/companies.model';
import { User } from '../users/users.model'
import { Education } from '../educations/educations.model'
import { Experience } from '../experiences/experiences.model'
import { Location } from "../locations/location.model";
import { RecruiterPositions } from "../recruiter-positions/recruiter-positions.model";

interface RecruiterAttrs {
  positionId?: number;
  officeEmail?: string;
  officePhone?: string;
  employeeId?: string;
  officePhoneExtn?: string;
  department?: string;
  locationId?: number;
  reportingId?: number;
  userId: number;
  companyId: number;
  birthday?: Date;
  dateJoining?: Date;
  gender?: string;
  degree?: string;
  maritalStatus?: string;
  emergencyPerson?: string;
  relationEmployee?: string;
  emergencyMobile?: string;
  personalEmail?: string;
  languages?: string;
  linkedin?: string;
  twitter?: string;
  facebook?: string;
  instagram?: string;
  currentStreet?: string;
  currentHouseNumber?: string;
  currentCity?: string;
  currentCountry?: string;
  currentState?: string;
  currentZip?: string;
  permanentStreet?: string;
  permanentHouseNumber?: string;
  permanentCity?: string;
  permanentCountry?: string;
  permanentState?: string;
  permanentZip?: string;
  passport?: string;
  visaType?: string;
  issueCountry?: string;
  issuedOn?: string;
  expiration?: string;
  nationality?: string;
  visaStatus?: string;
  profileStatus?: string;
  inviteLink?: string;
}

@Table({ tableName: 'recruiters', createdAt: false, updatedAt: false })
export class Recruiter extends Model<
  Recruiter,
  RecruiterAttrs
  > {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: '1', description: 'Position Id' })
  @ForeignKey(() => RecruiterPositions)
  @Column({ type: DataType.INTEGER, allowNull: true })
  positionId: number;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Office Email',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  officeEmail: string;

  @ApiProperty({
    example: 'test',
    description: 'Employee id',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  employeeId: string;

  @ApiProperty({ example: '5455654645', description: 'Office Phone Number' })
  @Column({ type: DataType.STRING, allowNull: true })
  officePhone: string;

  @ApiProperty({
    example: '5455654645',
    description: 'Office Phone Number Extn',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  officePhoneExtn: string;

  @ApiProperty({ example: 'Sales Department', description: 'Department' })
  @Column({ type: DataType.STRING, allowNull: true })
  department: string;

  @ApiProperty({ example: "1", description: "Location id" })
  @ForeignKey(() => Location)
  @Column({ type: DataType.INTEGER, allowNull: true })
  locationId: number;

  @ApiProperty({ example: '1', description: 'Company ID' })
  @ForeignKey(() => Company)
  @Column({ type: DataType.INTEGER, allowNull: false })
  companyId: number;

  @ApiProperty({ example: '1', description: 'User ID' })
  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: false })
  userId: number;

  @ApiProperty({ example: '1', description: 'Recruiter ID' })
  @ForeignKey(() => Recruiter)
  @Column({ type: DataType.INTEGER, allowNull: true })
  reportingId: number;

  @ApiProperty({ example: '01.01.1990', description: 'Birthday data' })
  @Column({ type: DataType.DATE, allowNull: true, defaultValue: DataType.NOW })
  birthday: Date;

  @ApiProperty({ example: '01.01.1990', description: 'Date of joining' })
  @Column({ type: DataType.DATE, allowNull: true, defaultValue: DataType.NOW })
  dateJoining: Date;

  @ApiProperty({ example: 'Male', description: 'Gender' })
  @Column({ type: DataType.STRING, allowNull: true })
  gender: string;

  @ApiProperty({ example: 'master', description: 'Education degree' })
  @Column({ type: DataType.STRING, allowNull: true })
  degree: string;

  @ApiProperty({ example: 'Single', description: 'Marital Status' })
  @Column({ type: DataType.STRING, allowNull: true })
  maritalStatus: string;

  @ApiProperty({
    example: 'Rob Anderson',
    description: 'Emergency Contact Person',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  emergencyPerson: string;

  @ApiProperty({ example: 'Positive', description: 'Relation with Employee' })
  @Column({ type: DataType.STRING, allowNull: true })
  relationEmployee: string;

  @ApiProperty({
    example: '213214124',
    description: 'Emergency Contact Mobile',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  emergencyMobile: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Personal Email',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  personalEmail: string;

  @ApiProperty({
    example:
      '[{"value":"English","label":"English"},{"value":"Spanish","label":"Spanish"}]',
    description: 'Languages',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  languages: string;

  @ApiProperty({
    example: 'https://www.linkedin.com/',
    description: 'Linkedin',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  linkedin: string;

  @ApiProperty({ example: 'https://twitter.com/', description: 'Twitter' })
  @Column({ type: DataType.STRING, allowNull: true })
  twitter: string;

  @ApiProperty({
    example: 'https://www.facebook.com/',
    description: 'Facebook',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  facebook: string;

  @ApiProperty({
    example: 'https://www.instagram.com/',
    description: 'Instagram',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  instagram: string;

  @ApiProperty({
    example: 'New Street',
    description: 'Current Address: Street',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  currentStreet: string;

  @ApiProperty({
    example: '95',
    description: 'Current Address: House name or number',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  currentHouseNumber: string;

  @ApiProperty({ example: 'New York', description: 'Current Address: City' })
  @Column({ type: DataType.STRING, allowNull: true })
  currentCity: string;

  @ApiProperty({ example: 'USA', description: 'Current Address: Country' })
  @Column({ type: DataType.STRING, allowNull: true })
  currentCountry: string;

  @ApiProperty({ example: 'New York', description: 'Current Address: State' })
  @Column({ type: DataType.STRING, allowNull: true })
  currentState: string;

  @ApiProperty({
    example: '10030',
    description: 'Current Address: Zip/Postcode',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  currentZip: string;

  @ApiProperty({
    example: 'New Street',
    description: 'Current Address: Street',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  permanentStreet: string;

  @ApiProperty({
    example: '95',
    description: 'Current Address: House name or number',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  permanentHouseNumber: string;

  @ApiProperty({ example: 'New York', description: 'Current Address: City' })
  @Column({ type: DataType.STRING, allowNull: true })
  permanentCity: string;

  @ApiProperty({ example: 'USA', description: 'Current Address: Country' })
  @Column({ type: DataType.STRING, allowNull: true })
  permanentCountry: string;

  @ApiProperty({ example: 'New York', description: 'Current Address: State' })
  @Column({ type: DataType.STRING, allowNull: true })
  permanentState: string;

  @ApiProperty({
    example: '10030',
    description: 'Current Address: Zip/Postcode',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  permanentZip: string;

  @ApiProperty({ example: '2131232', description: 'Passport №' })
  @Column({ type: DataType.STRING, allowNull: true })
  passport: string;

  @ApiProperty({ example: 'U-1', description: 'Visa Type' })
  @Column({ type: DataType.STRING, allowNull: true })
  visaType: string;

  @ApiProperty({ example: 'USA', description: 'Issue Country' })
  @Column({ type: DataType.STRING, allowNull: true })
  issueCountry: string;

  @ApiProperty({ example: 'January 11, 2022', description: 'Issued On' })
  @Column({ type: DataType.STRING, allowNull: true })
  issuedOn: string;

  @ApiProperty({ example: 'January 11, 2022', description: 'Expiration' })
  @Column({ type: DataType.STRING, allowNull: true })
  expiration: string;

  @ApiProperty({ example: 'American', description: 'Nationality' })
  @Column({ type: DataType.STRING, allowNull: true })
  nationality: string;

  @ApiProperty({ example: 'Open', description: 'Status' })
  @Column({ type: DataType.STRING, allowNull: true })
  visaStatus: string;

  @ApiProperty({ example: 'Open', description: 'Status' })
  @Column({ type: DataType.STRING, allowNull: true, defaultValue: "Pending" })
  profileStatus: string;

  @ApiProperty({ example: 'invite123', description: 'Invite Link' })
  @Column({ type: DataType.STRING, allowNull: true })
  inviteLink: string;

  @BelongsTo(() => User)
  user: User;

  @BelongsTo(() => RecruiterPositions)
  position: RecruiterPositions;

  @BelongsTo(() => Recruiter)
  reporting: Recruiter;

  @HasMany(() => Recruiter)
  teams: Recruiter[];

  @BelongsTo(() => Company)
  company: Company;

  @HasMany(() => Education)
  educationDetails: Education[];

  @HasMany(() => Experience)
  experienceDetails: Experience[];

  @BelongsTo(() => Location)
  location: Location;
}
