import {IAsync<PERSON>he<PERSON><PERSON><PERSON>List, ITabsFilter} from "../global/global";

export interface IJobDetailView{
	title:string,
	selectedItems:Array<number>
	emptySearch: boolean,
		emptyTable: boolean,
	filters:{
		searchValue:string,
		sortBy:string,
		sortType:string,
		location:Array<IAsyncCheckBoxList>,
		education:Array<string>
		skills:Array<string>,
		experience:Array<number>
		salary_year:Array<number>,
		percentage:Array<number>
		status:Array<string>
		applyJob:boolean
	},
	tableItems:Array<{
		firstname:string,
		middlename:string,
		lastname:string,
		avatar:string,
		id:number,
		percent:number,
		location:string,
		experience:string,
		salary:string,
		phone:string,
		email:string,
		status:string,
		scores:string
	}>,
	fixedTab: {
		id: number,
		displayName: string,
	},
	tabFilter:Array<ITabsFilter>,
	pagination:{
		currentPage: number,
		limit: number,
		totalCount: number
	},
	filterInfo:{
		tabs:Array<string>
	}
}

export interface IJobDetailPieView{
	totalJobs: number,
  	jobsData: { key : string, doc_count : number }[],
  	duration : string
}

export interface IJobDetailAreaView{
	jobStatus: string,
  	jobsData: { key : number, doc_count : number, key_as_string : string }[],
  	duration : string
}

export type IJobAreaArray =  { key : number, doc_count : number, key_as_string : string }[]