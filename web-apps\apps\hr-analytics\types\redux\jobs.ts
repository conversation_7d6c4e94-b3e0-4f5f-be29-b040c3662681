import { ITabsFilter } from "../global/global";

export interface IJobsTable {
	topButtons: string
	emptySearch: boolean,
	emptyTable: boolean,
	filters: {
		searchValue: string,
		sortBy: string,
		sortType: string,
		date: {
			from: string,
			to: string
		},
		status: Array<string>
	},
	changeWorkflow: {
		open: boolean,
		title: string,
		id: number,
		jobId: number
	},
	workflowList: any
	tableItems: Array<IJobsTableItem>,
	fixedTab: {
		id: number,
		displayName: string,
	},
	tabFilter: Array<ITabsFilter>,
	pagination: {
		currentPage: number,
		limit: number,
		totalCount: number
	},
	filterInfo: {
		tabs: Array<string>
	},
	deletePopupInfo: {
		open: boolean,
		name: string,
		id: number
	},
	approvePopup: {
		open: boolean,
		name: string,
		id: number
	},
	rejectPopup: {
		open: boolean,
		name: string,
		id: number
	},
}

export interface IJobsTableItem {
	id: number
	jobTitle: string,
	jobLocation: string,
	salary: string,
	postedOn: string,
	postedBy: string,
	status: string,
	positionWorkflow: string,
	remoteLocations: boolean,
	summary: boolean
	workflow: boolean
	sso_link?: {
		marketplace?: string
	}
}