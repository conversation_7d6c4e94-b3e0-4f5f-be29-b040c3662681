import React, { useEffect, useState } from 'react';
import { createClientMessage } from 'react-chatbot-kit';
import { store } from '../../../store';
import { changeAbout, changeBENEFITS, changeJobDetails, changeRequirements } from '../../../store/action-creators';
import { useSelector } from 'react-redux';
import { fetchJobSuggestions, extractJobDetails } from './actionUtils';
import { setChatAboutCompany, setChatBenefits, setChatJobDetails, setChatRequirements, setChatSavedAnswers, setIsLoading } from '../../../store/reducers/chatJobReducer';

const ActionProvider = ({ createChatBotMessage, setState, children, state }) => {
  const { jobDetails: jobDetailsState } = useSelector((state: any) => state.screen);
  const chatJobData = useSelector((state: any) => state.chatbotJobsDetails);
  const [saveDetails, setSaveDetails] = useState(false);

  useEffect(() => {
    if (saveDetails) {
      const { jobDetails, requirements, aboutCompany, benefits } = chatJobData;
      store.dispatch(changeJobDetails(jobDetails));
      store.dispatch(changeRequirements(requirements));
      store.dispatch(changeAbout(aboutCompany));
      store.dispatch(changeBENEFITS(benefits));
    }
  }, [saveDetails, chatJobData]);

  useEffect(() => {
    if (state.messages.length > 0) {
      sessionStorage.setItem('chatbot_messages', JSON.stringify(state.messages));
    }
  }, [state?.messages]);

  useEffect(() => {
    const defaultFields = {
      jobType: { value: "Full-Time Employees", label: "Full-Time Employees" },
      preferableShift: { value: "General Shift", label: "General Shift" },
      functionalArea: { value: "Information Technology (IT)", label: "Information Technology (IT)" },
      noticePeriod: { value: "1 Month", label: "1 Month" },
      opening: { value: "1", label: "1" },
    };
    store.dispatch(changeJobDetails({ ...jobDetailsState, ...defaultFields }));
    store.dispatch(setChatJobDetails(defaultFields));
  }, []);

  const formatMessage = (text) => {
    if (!text) return null;
    
    // Handle markdown-style links first
    const markdownLinkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
    if (text.match(markdownLinkRegex)) {
      return (
        <span>
          {text.split(markdownLinkRegex).map((part, i) => {
            if (i % 3 === 1) { // This is the link text
              const url = text.split(markdownLinkRegex)[i + 1];
              return (
                <a
                  key={i}
                  href={url}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{ color: '#0066cc', textDecoration: 'underline' }}
                >
                  {part}
                </a>
              );
            }
            if (i % 3 === 2) return null;
            return formatRichText(part);
          })}
        </span>
      );
    }
    
    // Handle plain URLs
    const urlRegex = /(https?:\/\/[^\s]+(?![^)]*\)))/g;
    if (!text.match(urlRegex)) {
      return formatRichText(text);
    }

    return (
      <span>
        {text.split(urlRegex).map((part, i) => 
          part.match(urlRegex) ? (
            <a
              key={i}
              href={part}
              target="_blank"
              rel="noopener noreferrer"
              style={{ color: '#0066cc', textDecoration: 'underline', wordBreak: 'break-all' }}
            >
              {part}
            </a>
          ) : formatRichText(part)
        )}
      </span>
    );
  };

  const formatRichText = (text) => {
    if (!text) return null;
    
    return text.split(/\n\n+/).map((paragraph, pIndex) => {
      if (!paragraph.trim()) return <br key={`br-${pIndex}`} />;
      
      return (
        <span key={`para-${pIndex}`}>
          {paragraph.split(/\n/).map((line, lIndex) => {
            if (!line.trim()) return <br key={`line-${pIndex}-${lIndex}`} />;
            
            const parts = line.split(/(\*\*|__)(.*?)\1/);
            return (
              <span key={`line-${pIndex}-${lIndex}`}>
                {parts.map((part, partIndex) => {
                  if (part === '**' || part === '__') return null;
                  if (parts[partIndex - 1] === '**' || parts[partIndex - 1] === '__') {
                    return <strong key={`bold-${pIndex}-${lIndex}-${partIndex}`}>{part}</strong>;
                  }
                  return part;
                })}
                {lIndex < paragraph.split(/\n/).length - 1 && <br />}
              </span>
            );
          })}
          {pIndex < text.split(/\n\n+/).length - 1 && <><br /><br /></>}
        </span>
      );
    });
  };

  const handleChatResponse = async (messageStr, showOnlyResponse = false, loadingMessage = "...") => {
    const loadingMsg = createChatBotMessage(loadingMessage, { loading: true, delay: 1000 });
    setState(prev => ({ ...prev, messages: [...prev.messages, loadingMsg] }));

    try {
      const data = await fetchJobSuggestions(messageStr, state.sessionId);
      const jobDetails = data.formattedJobDetails?.[0];
      const hasValidJobDetails = data.formattedJobDetails?.length > 0 && jobDetails;

      if (data.chatResponse || showOnlyResponse) {
        const formattedResponse = formatMessage(data.chatResponse);
        const message = createChatBotMessage(formattedResponse, { delay: 1000 });
        
        if (hasValidJobDetails) {
          const { updatedJobDetails, updatedRequirements, updatedAboutCompany, updatedBenefits } = extractJobDetails(jobDetails, chatJobData);
          store.dispatch(setChatJobDetails(updatedJobDetails));
          store.dispatch(setChatRequirements(updatedRequirements));
          store.dispatch(setChatAboutCompany(updatedAboutCompany));
          store.dispatch(setChatBenefits(updatedBenefits));
          setSaveDetails(true);
        }

        setState(prev => ({ ...prev, messages: [...prev.messages.slice(0, -1), message] }));
        return;
      }
    } catch (error) {
      console.error("Error in conversation:", error);
      setState(prev => ({
        ...prev,
        messages: [...prev.messages.slice(0, -1), createChatBotMessage("Sorry, I encountered an error. Please try again.")]
      }));
    }
  };

  const actions = {
    greet: () => {
      const message = createChatBotMessage("Hello there! Please enter the job title you want to create?", {});
      setState(state => ({ ...state, messages: [...state.messages, message] }));
    },
    promptJobTitle: () => {
      const message = createChatBotMessage("What is the title of the job you want to create?", {});
      setState(state => ({ ...state, messages: [...state.messages, message] }));
    },
    sendClientMessage: (messageStr, widgetName, payload) => {
      const message = createClientMessage(messageStr, { widget: widgetName, payload, delay: 1000 });
      setState(prev => ({ ...prev, messages: [...prev.messages, message] }));
    },
    sendChatMessage: (messageStr, widgetName, payload) => {
      const message = createChatBotMessage(messageStr, { widget: widgetName, payload, delay: 1000 });
      setState(prev => ({ ...prev, messages: [...prev.messages, message] }));
    },
    resetState: () => {
      setState(state => ({ ...state, messages: [], jobDetails: {} }));
      store.dispatch(setChatSavedAnswers({}));
    },
    chatConversation: async (messageStr) => {
      if (messageStr) {
        store.dispatch(setIsLoading(true));
        await handleChatResponse(messageStr, true);
        store.dispatch(setIsLoading(false));
      }
    }
  };

  return (
    <div>
      {React.Children.map(children, child => React.cloneElement(child, { actions }))}
    </div>
  );
};

export default ActionProvider;