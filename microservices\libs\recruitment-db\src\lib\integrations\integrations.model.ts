import { Column, DataType, Model, Table } from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';

@Table({ tableName: 'integrations' })
export class Integrations extends Model<Integrations> {
  @ApiProperty({ example: 1, description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
    id: number;

  @ApiProperty({ example: 'gmail', description: 'gmail, gcal or outlook' })
  @Column({ type: DataType.STRING })
    provider: string;

  @ApiProperty({ example: 1, description: 'User ID' })
  @Column({ type: DataType.INTEGER })
  userId: number;

  @ApiProperty({ example: '1//0cccfbP2IuMtWCgYIARAAGAwSNw', description: 'Provider connection data in JSON format or String' })
  @Column({ type: DataType.STRING })
    data: string;
}
