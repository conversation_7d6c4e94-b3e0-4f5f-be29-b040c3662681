import ChatBotImg from '../../image/icon/UR_Agent.svg';

const PwBotAvatar = (props) => {
  return (
    <div
      className={`chatbot-avatar ${props?.className || ''}`}
      style={{
        width: '50px',
        height: '50px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'transparent',
        cursor: 'pointer',
        transition: 'transform 0.2s ease'
      }}

    >
      <img
        src={ChatBotImg}
        alt='uR Agent'
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'contain',
          display: 'block'
        }}
        {...props}
      />
    </div>
  )
};

export default PwBotAvatar;