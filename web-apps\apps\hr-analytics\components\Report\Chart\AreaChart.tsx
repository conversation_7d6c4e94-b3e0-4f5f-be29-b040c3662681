import { format } from 'date-fns';
import { default as ReactSelect } from "react-select";
import { memo } from "react";
import { selectSmallStyle } from "../../../styles/selectSmallStyle";
import {
  AreaChart as RechartsAreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
} from "recharts";

const getTickFormatter = (duration) => {
  switch (duration) {
    case 'week':
      return (tick) => format(new Date(tick), 'EEE'); // Day of the week (Sun, Mon, etc.)
    case 'month':
      return (tick) => format(new Date(tick), 'd'); // Day and month (e.g., 1 Jan)
    case 'year':
      return (tick) => format(new Date(tick), 'MMM'); // Month and year (e.g., Jan 2024)
    default:
      return (tick) => format(new Date(tick), 'P'); // Default: full date (e.g., 01/01/2024)
  }
};


const options = [
  { value: "week", label: "This Week" },
  { value: "month", label: "This Month" },
  { value: "year", label: "This Year" },
];

const AreaChart = ({
  data,
  title,
  dataKey,
  areaChartOperations,
  changeAreaHandler,
}) => {
  return (
    <div className="area-chart">
      <div className="area-chart__head">
        {!!title ? <p className="area-chart__head__headline">{title}</p> : ""}
        {/* <div className="table-screen-top">
          <div className="table-screen-top__wrapper"> */}
        <div className="table-screen-top__buttons-group">
          {areaChartOperations.map((buttonDetails) => {
            return (
              <button
                key={buttonDetails.buttonText}
                className={`table-screen-top__buttons-group__item ${buttonDetails.isActive} `}
                onClick={() => {
                  buttonDetails.onChange(buttonDetails.buttonValue);
                }}
              >
                {buttonDetails.buttonText}
              </button>
            );
          })}
        </div>
        {/* </div>
        </div> */}
        <div className="area-chart__head__select">
          <ReactSelect
            options={options}
            value={options.find((ele) => ele.value === data.duration)}
            onChange={(value: any) => {
              changeAreaHandler(value.value);
            }}
            isSearchable={false}
            hideSelectedOptions={false}
            styles={selectSmallStyle}
            id="publishJobsSelect"
            instanceId="publishJobsSelect"
          />
        </div>
      </div>
      <div
        className={`area-chart__body ${
          areaChartOperations.duration === "month" ? "month" : ""
        }`}
      >
        <div className="area-chart__body__inner">
          <ResponsiveContainer width="100%" height="100%">
            <RechartsAreaChart
              width={412}
              height={198}
              data={data.jobsData}
              margin={{
                top: 10,
                right: 30,
                left: 0,
                bottom: 0,
              }}
            >
              <CartesianGrid strokeDasharray="4 4" />
              <XAxis
                dataKey="key_as_string"
                tickFormatter={getTickFormatter(data.duration)}
                style={{
                  fontSize: "12px",
                  textTransform: "uppercase",
                  color: "#999EA5",
                  lineHeight: "1.4",
                }}
              />
              <YAxis
                style={{
                  fontSize: "12px",
                  color: "#999EA5",
                }}
              />
              <Area
                type="monotone"
                dataKey={dataKey}
                stroke="#099C73"
                fill="#099C7340"
              />
            </RechartsAreaChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default memo(AreaChart);
