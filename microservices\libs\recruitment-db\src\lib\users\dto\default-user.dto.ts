import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class DefaultUserDto {
  @ApiPropertyOptional({ example: "https://domain.com/avatar.jpg", description: "Avatar" })
  @IsOptional()
  avatar?: string;

  @ApiProperty({ example: "<EMAIL>", description: "Email address" })
  email: string;

  @ApiProperty({ example: "+123123456", description: "Phone" })
  phone: string;

  @ApiProperty({ example: "Rob", description: "First Name" })
  firstname: string;

  @ApiPropertyOptional({ example: "<PERSON>", description: "First Name" })
  @IsOptional()
  middlename?: string;

  @ApiProperty({ example: "Stark", description: "Last Name" })
  lastname: string;

  @ApiPropertyOptional({ example: "auth0|61a1102ba9ee1000738655a0", description: "Auth id" })
  @IsOptional()
  authId?: string;

  @ApiPropertyOptional({ example: "master", description: "Education degree" })
  @IsOptional()
  degree?: string;

  @ApiPropertyOptional({ example: "1", description: "Company ID" })
  @IsOptional()
  companyId?: number;
}
