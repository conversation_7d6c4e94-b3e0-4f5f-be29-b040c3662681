import { CodeExecutorService, SubmissionReq, BatchSumissions } from '@microservices/code-executor';
import {
  CodingAreaService,
  LanguagesService,
  LiveCodingService,
  LiveDraftsService,
  TakeHomeDraftsService,
  TakeHomeTaskService,
  CodeExecutionService
} from '@microservices/db';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Body, Controller, Get, Param, Post, Query, UseGuards, HttpException, HttpStatus, BadRequestException } from '@nestjs/common';
import { ArchivateSqliteService } from 'tools/archiveWithSqlite.service';
import { MultiFileSub } from 'tools/multi-file.interface';
import { SqliteGenerator } from 'tools/sqlite.service';
import { AuthUser, Permissions, PermissionsGuard } from '@microservices/auth';
import { AuthGuard } from '@nestjs/passport';
import { OpenAIExecutorService } from '@microservices/code-executor';

@Controller()
export class AppController {
  constructor(
    private codingAreaService: CodingAreaService,
    private codeExecutionService: CodeExecutorService,
    private archiveSQLiteService: ArchivateSqliteService,
    private sqlGenerator: SqliteGenerator,
    private liveCodingService: LiveCodingService,
    private takeHomeTaskService: TakeHomeTaskService,
    private takeHomeDraftsService: TakeHomeDraftsService,
    private liveDraftsService: LiveDraftsService,
    private languagesService: LanguagesService,
    private executionService: CodeExecutionService,
    private openAIExecutorService: OpenAIExecutorService,
  ) { }

  @Get('healthcheck')
  async healthcheck() {
    return { status: 'healthy' };
  }

  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('coding-playback/:id')
  async getCodingPlayback(@Param('id') assignmentId: number) {
    return this.codingAreaService.findAllByAssignmentId(assignmentId);
  }

  @ApiOperation({ summary: 'Get single submission' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('coding-area/:id')
  async getSingleSubmission(@Param() params): Promise<any> {
    return await this.codeExecutionService.getSubmission(params.id)
      .catch((err) => {
        throw err;
      });
  }

  @ApiOperation({ summary: 'Analyze code using GPT-4' })
  @ApiResponse({ status: 201, description: 'Code analyzed successfully with GPT-4' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post('coding-area/run')
  async postSingleSubmission(@Body() submissionData: any) {
    // Check if the submission is an array instead of a single object
    if (Array.isArray(submissionData)) {
      if (submissionData.length === 0) {
        return {
          token: `openai-${Date.now()}`,
          status: { id: 11, description: "Error" },
          stderr: "Empty submission array",
          message: "No code submissions found in the request.",
          created_at: new Date().toISOString(),
          finished_at: new Date().toISOString(),
          test_cases: [],
          overall_result: "Failed"
        };
      }
      // Take the first submission from the array
      submissionData = submissionData[0];
    }

    // Now we have a single submission object
    const submission: SubmissionReq = submissionData;

    // Ensure language_id is defined to prevent errors
    if (submission.language_id === undefined) {
      submission.language_id = 61; // Default to JavaScript if missing
    }

    const data = await this.openAIExecutorService.analyzeCode(submission).catch((err) => {
      return {
        token: `openai-${Date.now()}`,
        status: { id: 11, description: "Error" },
        stderr: err.message || "Unknown error occurred",
        message: "Failed to analyze code due to an internal error.",
        language_id: submission.language_id,
        created_at: new Date().toISOString(),
        finished_at: new Date().toISOString(),
        test_cases: [],
        overall_result: "Failed"
      };
    });
    return data;
  }

  @ApiOperation({ summary: 'Post single submission' })
  @ApiResponse({ status: 201 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post('coding-execution/create')
  async createCodingExecution(@Body() submission) {
    return await this.executionService.createCodeExecution(submission).catch((err) => {
      throw err;
    });
  }

  @ApiOperation({ summary: 'Post multiple submission' })
  @ApiResponse({ status: 201 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post('coding-area/run-multiple-sqlite')
  async postMultipleSubmissionSQlite(@Body() data: MultiFileSub) {
    await this.sqlGenerator.generateDb('db.sqlite', data.sql);
    const zipedData = await this.archiveSQLiteService.generateZip(data);
    return await this.codeExecutionService.postSharedMultiple(zipedData as string).catch((err) => {
      throw err;
    });
  }

  @ApiOperation({ summary: 'Get languages' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('languages')
  async getLanguages(): Promise<any> {
    try {
      const languages = await this.languagesService?.findLanguages();
      return languages;
    } catch (error) {
      throw new HttpException('Failed to fetch languages', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @ApiOperation({ summary: 'Get sqlite submission' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('coding-assessment/search')
  @Permissions('assessment:view')
  async searchCodingAssessment(@Query() query, @AuthUser() user) {
    const companyId = user["https://urecruits.com/companyId"];
    if (!query.type) {
      const takeHomeTask = await this.takeHomeTaskService.search(query, companyId);
      const takeHomeDraft = await this.takeHomeDraftsService.search(query, companyId);
      const liveCoding = await this.liveCodingService.search(query, companyId);
      const liveDraft = await this.liveDraftsService.search(query, companyId);
      if (query.status === "ACTIVE") {
        return [...liveCoding, ...takeHomeTask];
      } else if (query.status === "DRAFT") {
        return [...liveDraft, ...takeHomeDraft];
      } else {
        return [...liveCoding, ...liveDraft, ...takeHomeTask, ...takeHomeDraft];
      }
    } else if (query.type === 'live-task') {
      const liveCoding = await this.liveCodingService.search(query, companyId);
      const liveDraft = await this.liveDraftsService.search(query, companyId);
      if (query.status === "ACTIVE") {
        return [...liveCoding];
      } else if (query.status === "DRAFT") {
        return [...liveDraft];
      }
      return [...liveCoding, ...liveDraft];
    } else {

      const takeHomeTask = await this.takeHomeTaskService.search(query, companyId);
      const takeHomeDraft = await this.takeHomeDraftsService.search(query, companyId);
      if (query.status === "ACTIVE") {
        return [...takeHomeTask];
      } else if (query.status === "DRAFT") {
        return [...takeHomeDraft];
      }
      return [...takeHomeTask, ...takeHomeDraft];
    }
  }

  @ApiOperation({ summary: 'Get sqlite submission' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post('coding-assessment/delete')
  @Permissions('assessment:edit')
  deleteScopeAssessments(@Body() data, @AuthUser() user): any {
    if (data.type === 'live-task') {
      if (data.status === 'ACTIVE') return this.liveCodingService.remove(data.id, user["https://urecruits.com/companyId"], user["https://urecruits.com.userId"]);
      else return this.liveDraftsService.remove(data.id, user["https://urecruits.com.userId"]);
    } else if (data.status === 'ACTIVE') {
      return this.takeHomeTaskService.remove(data.id, user["https://urecruits.com/companyId"], user["https://urecruits.com.userId"]);
    } else {
      return this.takeHomeDraftsService.remove(data.id);
    }

    return 'OK';
  }

  @ApiOperation({ summary: 'Analyze batch of code submissions using GPT-4' })
  @ApiResponse({ status: 201, description: 'Batch of code analyzed successfully with GPT-4' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post('coding-area/run/batch')
  async postBatchSubmissions(@Body() submissions: BatchSumissions) {
    try {
      // Filter out duplicate submissions by comparing source_code
      const uniqueSubmissions = this.filterDuplicateSubmissions(submissions);

      // Create an array to hold promises for all the OpenAI analyses
      const analysisPromises = uniqueSubmissions.map(submission => {
        // Ensure language_id is defined to prevent errors
        if (submission.language_id === undefined) {
          submission.language_id = 61; // Default to JavaScript if missing
        }

        return this.openAIExecutorService.analyzeCode(submission);
      });

      // Wait for all analyses to complete
      const results = await Promise.all(analysisPromises);

      // Return the results in the same format as the original endpoint
      return {
        submissionResults: results
      };
    } catch (err) {
      throw err;
    }
  }

  /**
   * Filters out duplicate submissions from a batch based on source_code
   */
  private filterDuplicateSubmissions(submissions: BatchSumissions): BatchSumissions {
    const seen = new Set<string>();
    return submissions.filter(submission => {
      // If language_id is missing, default to 0 to prevent undefined errors
      if (submission.language_id === undefined) {
        submission.language_id = 0;
      }

      const key = `${submission.source_code}`;
      if (seen.has(key)) {
        return false; // Skip duplicate
      } else {
        seen.add(key);
        return true;
      }
    });
  }

  @ApiOperation({ summary: 'Get batch submission' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('coding-area/batch/get')
  async getBatchSubmissions(@Query() query): Promise<any> {

    if (!query?.tokens || query?.tokens.length === 0) {
      throw new BadRequestException("There should be at least one submission in a batch");
    }
    try {
      const data = await this.codeExecutionService.getBatchSubmissions(query.tokens)
      return data
    } catch (err) {
      throw err
    }
  }
}
