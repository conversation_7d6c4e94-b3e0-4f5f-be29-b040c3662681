import { useState } from "react";
import axios from "axios";
import { getEnv } from "@urecruits/api";
import { Button, getConfig } from "@ucrecruits/globalstyle/src/ucrecruits-globalstyle";
import { store } from "../../../store";
import { changeJobBoards } from "../../../store/action-creators";

const { API_RECRUITMENT } = getEnv();
const successImage = require("../../../image/icon/success_image.svg");

const JobTargetPopup = ({ isOpen, onClose, onSuccess }: { isOpen: boolean; onClose: () => void; onSuccess?: () => void }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  if (!isOpen) return null;

  const handleConnect = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await axios.post(
        `${API_RECRUITMENT}/api/jobtarget/integrate`,
        {},
        getConfig()
      );
      if (response.status === 200 || response.status === 201) {
        console.log("JobTarget integration initiated:", response.data);
        setSuccess(true);
        if (onSuccess) onSuccess();
      }
    } catch (err) {
      console.error("Error initiating JobTarget integration:", err);
      setError("Failed to connect to JobTarget. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="popup">
      {!success ? (
        <div className="popup__step">
          <div className="popup__head">
            <p className="popup__head__headline">Connect to JobTarget</p>
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="popup__head__close"
              onClick={onClose}
            >
              <path
                d="M18 6L12 12M6 18L12 12M12 12L6 6L18 18"
                stroke="#C1C5CB"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <div className="popup__body">
            <p className="popup__body__text">
              No JobTarget integration found. Please connect your account to proceed.
            </p>
            {error && <p className="error-message">{error}</p>}
          </div>
          <div className="popup__bottom end">
            <button
              className="popup__bottom__cancel button--empty"
              onClick={onClose}
            >
              Cancel
            </button>
            <Button
              isLoading={isLoading}
              label="Connect to JobTarget"
              className={`popup__bottom__publish button--filled ${isLoading ? "button--filled-disable" : ""}`}
              onClick={handleConnect}
            />
          </div>
        </div>
      ) : (
        <div className="popup__step">
          <div className="popup__head">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="popup__head__close"
              onClick={onClose}
            >
              <path
                d="M18 6L12 12M6 18L12 12M12 12L6 6L18 18"
                stroke="#C1C5CB"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <div className="popup__body">
            <img src={successImage} alt="" className="popup__body__image" />
            <p className="popup__body__headline">
              Successfully Connected to JobTarget
            </p>
          </div>
          <div className="popup__bottom center">
            <Button
              label="Got it!"
              className="popup__bottom__publish button--filled"
              onClick={onClose}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default JobTargetPopup;