import React from 'react';

interface ChatHeaderProps {
  setIsChatbotOpen: (open: boolean) => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({ setIsChatbotOpen }) => {
  return (
    <div className="react-chatbot-kit-chat-header">
      <div className="react-chatbot-kit-chat-header-text">uR Agent</div>
      <button 
        className="react-chatbot-kit-chat-header-close-chat-button"
        onClick={() => setIsChatbotOpen(false)}
      >
        ✕
      </button>
    </div>
  );
};

export default ChatHeader;
