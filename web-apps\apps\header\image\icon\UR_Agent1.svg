<svg width="73" height="86" viewBox="0 0 73 86" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_552_124)">
<mask id="mask0_552_124" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="73" height="73">
<path d="M0 0H73V72.5846H0V0Z" fill="white"/>
</mask>
<g mask="url(#mask0_552_124)">
<mask id="mask1_552_124" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="73" height="73">
<path d="M36.5 0C16.3424 0 0 16.2494 0 36.2922C0 56.335 16.3424 72.5846 36.5 72.5846C56.6576 72.5846 73 56.335 73 36.2922C73 16.2494 56.6576 0 36.5 0Z" fill="white"/>
</mask>
<g mask="url(#mask1_552_124)">
<path d="M0 0H73V72.5846H0V0Z" fill="#1C1C1C"/>
</g>
</g>
<mask id="mask2_552_124" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="73" height="73">
<path d="M0 0H72.9949V72.5795H0V0Z" fill="white"/>
</mask>
<g mask="url(#mask2_552_124)">
<mask id="mask3_552_124" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="73" height="73">
<path d="M36.4983 0C16.3408 0 0 16.2477 0 36.2906C0 56.3334 16.3408 72.5795 36.4983 72.5795C56.6559 72.5795 72.9949 56.3334 72.9949 36.2906C72.9949 16.2477 56.6559 0 36.4983 0Z" fill="white"/>
</mask>
<g mask="url(#mask3_552_124)">
<path d="M36.4983 0C16.3408 0 0 16.2477 0 36.2906C0 56.3334 16.3408 72.5795 36.4983 72.5795C56.6559 72.5795 72.9949 56.3334 72.9949 36.2906C72.9949 16.2477 56.6559 0 36.4983 0Z" stroke="#01646E" stroke-width="12"/>
</g>
</g>
<mask id="mask4_552_124" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="10" y="31" width="24" height="25">
<path d="M10.5464 31.0801H33.7735V55.692H10.5464V31.0801Z" fill="white"/>
</mask>
<g mask="url(#mask4_552_124)">
<mask id="mask5_552_124" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="10" y="31" width="24" height="25">
<path d="M33.6834 31.3613V43.8726C33.6834 47.0159 32.5542 49.7649 30.3549 52.0103C28.0966 54.2541 25.3877 55.3769 22.1149 55.3769C18.9552 55.3769 16.2463 54.2541 13.988 51.9533C11.6756 49.7095 10.5464 47.0159 10.5464 43.8726V31.3613H16.81V43.8726C16.81 45.2756 17.318 46.5107 18.3341 47.5764C19.3502 48.6421 20.5908 49.1473 22.1149 49.1473C23.5816 49.1473 24.8796 48.6421 25.8957 47.5764C26.9118 46.5107 27.4198 45.3327 27.4198 43.8726V31.3613H33.6834Z" fill="white"/>
</mask>
<g mask="url(#mask5_552_124)">
<path d="M10.5464 31.3613V55.3769H33.6834V31.3613H10.5464Z" fill="url(#paint0_linear_552_124)"/>
</g>
</g>
<mask id="mask6_552_124" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="37" y="17" width="26" height="38">
<path d="M37.5487 17.7285H62.4638V54.0208H37.5487V17.7285Z" fill="white"/>
</mask>
<g mask="url(#mask6_552_124)">
<mask id="mask7_552_124" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="37" y="17" width="26" height="37">
<path d="M52.9279 39.3261C54.8454 38.4283 56.4269 37.0269 57.6117 35.2313C58.7966 33.3785 59.3604 31.3595 59.3604 29.1712C59.3604 26.0279 58.2312 23.3343 56.0876 21.1459C53.8309 18.902 51.1777 17.7793 48.0737 17.7793H37.8032V52.794C37.8032 52.794 39.8353 50.3807 40.9071 48.4726C41.6414 47.239 42.6 46.0609 44.0668 45.4987C44.0111 45.3309 44.0111 45.0506 44.1241 44.9382H43.8423C43.8423 44.9382 44.2372 40.3935 44.293 38.9335C44.3487 37.4751 44.293 34.221 44.293 34.221L42.4869 34.1085C42.4869 34.1085 43.1638 33.4355 42.995 32.4807C42.4869 31.5274 42.092 30.742 42.092 30.742C42.092 30.742 41.6414 29.8441 42.3181 28.7214C42.995 27.6003 43.6161 26.3651 43.6161 26.3651C43.6161 26.3651 43.9537 25.9725 44.801 25.7476C45.5909 25.5798 46.099 25.13 46.1547 24.9622C46.2121 24.8497 46.0433 24.457 45.8171 24.2892C45.5909 24.1213 45.309 23.111 45.8728 22.6613C46.4382 22.1561 47.7919 22.381 47.905 22.9986C47.9607 23.4467 47.905 23.6716 47.905 23.6716H48.018C48.018 23.6716 48.0737 24.3446 48.018 24.5141C47.9607 24.6248 47.8476 24.6819 47.8476 24.7943C47.8476 24.9622 47.7919 25.2994 48.018 25.2994C48.2999 25.2994 48.7506 25.3549 48.7506 25.3549C48.7506 25.3549 49.316 25.3549 49.4848 25.86C49.6536 26.3651 50.4435 28.7214 50.5009 28.8338C50.6139 29.0017 50.7828 30.5741 50.5009 31.415C50.5009 31.7522 50.4435 32.0325 50.3321 32.2004C50.3321 32.2004 50.3878 32.7055 49.9928 32.7625C49.824 32.8734 49.429 33.3785 49.429 33.6034C49.429 33.8282 49.7666 33.7712 49.429 33.9407C49.0898 34.1085 48.921 33.8836 48.921 34.4458C48.921 35.0064 48.1868 38.4853 48.0737 40.7292C47.9607 42.9746 47.905 44.8257 47.905 44.8257C47.905 44.8257 48.526 45.2184 48.921 45.6682C50.7828 46.509 52.3626 48.1368 53.9997 50.5485C55.1846 52.2318 55.9188 53.2992 56.3695 53.9151H62.52L52.9279 39.3261Z" fill="white"/>
</mask>
<g mask="url(#mask7_552_124)">
<path d="M37.8032 17.7793V53.9151H62.4643V17.7793H37.8032Z" fill="url(#paint1_linear_552_124)"/>
</g>
</g>
<mask id="mask8_552_124" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="45" y="36" width="2" height="10">
<path d="M45.101 36.084H46.7791V45.2613H45.101V36.084Z" fill="white"/>
</mask>
<g mask="url(#mask8_552_124)">
<mask id="mask9_552_124" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="45" y="36" width="2" height="10">
<path d="M46.325 44.4334C46.4938 43.7035 46.5512 37.4185 46.5512 36.9704C46.5512 36.5206 46.3807 36.185 46.325 36.9133C46.2677 37.6434 45.2533 44.6567 45.309 44.7692C45.3647 44.8262 45.5909 44.9386 45.817 45.1064C45.9858 45.1064 46.0989 45.1064 46.2677 45.1064C46.2119 44.994 46.2677 44.7692 46.325 44.4334Z" fill="white"/>
</mask>
<g mask="url(#mask9_552_124)">
<path d="M45.2523 36.1855V45.107H46.5502V36.1855H45.2523Z" fill="url(#paint2_linear_552_124)"/>
</g>
</g>
<mask id="mask10_552_124" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="43" y="28" width="2" height="4">
<path d="M43.0035 28.9922H44.2622V31.4951H43.0035V28.9922Z" fill="white"/>
</mask>
<g mask="url(#mask10_552_124)">
<mask id="mask11_552_124" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="43" y="29" width="2" height="3">
<path d="M43.9535 29.1137C43.8404 28.8334 43.2767 29.7312 43.1636 30.1794C43.5585 30.5721 43.6716 31.3575 43.7847 31.2467C43.729 30.7969 43.9535 30.7969 44.124 30.2364C44.2354 29.6742 43.9535 29.1137 43.9535 29.1137Z" fill="white"/>
</mask>
<g mask="url(#mask11_552_124)">
<path d="M43.1636 28.834V31.3581H44.2354V28.834H43.1636Z" fill="url(#paint3_linear_552_124)"/>
</g>
</g>
<mask id="mask12_552_124" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="48" y="28" width="3" height="5">
<path d="M48.8761 28.9922H50.1348V32.3294H48.8761V28.9922Z" fill="white"/>
</mask>
<g mask="url(#mask12_552_124)">
<mask id="mask13_552_124" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="49" y="29" width="1" height="3">
<path d="M49.711 30.0128C49.7668 29.845 49.5406 29.5077 49.3718 29.3398C49.3718 29.5647 49.2587 29.7326 49.2587 29.7326C49.203 29.9004 49.203 30.7982 49.2587 31.191C49.3161 31.5853 49.4849 31.4729 49.598 31.9781C49.598 31.5283 49.6537 30.1823 49.711 30.0128Z" fill="white"/>
</mask>
<g mask="url(#mask13_552_124)">
<path d="M49.2031 29.3398V31.9781H49.7669V29.3398H49.2031Z" fill="url(#paint4_linear_552_124)"/>
</g>
</g>
<path d="M12.3579 74.9916C11.7548 75.7996 11.037 76.227 10.2078 76.2758C9.37687 76.3227 8.4903 75.9986 7.54629 75.3048C6.59249 74.604 6.01564 73.8524 5.8157 73.0495C5.61904 72.246 5.82226 71.4393 6.42536 70.6313L9.0606 67.0918L10.9747 68.4935L8.35916 72.0066C8.11006 72.3423 8.00024 72.6715 8.03142 72.9969C8.06581 73.3262 8.25427 73.6182 8.59512 73.8704C8.93605 74.1181 9.27037 74.2093 9.59811 74.1444C9.92917 74.079 10.2176 73.8839 10.4634 73.5578L13.079 70.0447L14.9931 71.4521L12.3579 74.9916Z" fill="#018687" fill-opacity="0.502"/>
<path d="M21.6175 77.2402C21.4208 77.6445 21.1324 77.957 20.7522 78.1791C20.3752 78.4005 19.9409 78.4916 19.4477 78.4525L19.5854 81.94L17.173 80.7925L17.1664 77.8011L16.3667 77.4231L15.1933 79.8478L13.0497 78.8241L16.2552 72.1758L20.3458 74.1249C20.821 74.3515 21.1848 74.6377 21.4339 74.9849C21.6863 75.334 21.824 75.705 21.847 76.0997C21.8731 76.4905 21.7961 76.8718 21.6175 77.2402ZM19.3821 76.2884C19.469 76.1112 19.4821 75.9367 19.4214 75.7672C19.3592 75.5946 19.2444 75.4675 19.074 75.3892L17.6646 74.7179L17.0747 75.9431L18.484 76.6144C18.6545 76.6927 18.8249 76.7023 18.9953 76.6407C19.1691 76.5753 19.2985 76.4578 19.3821 76.2884Z" fill="#018687" fill-opacity="0.502"/>
<path d="M31.0466 84.8535L30.9221 83.7785L28.3655 83.3222L27.8739 84.2868L25.4746 83.8632L29.4472 77.084L32.1807 77.5666L33.5245 85.297L31.0466 84.8535ZM29.1456 81.7774L30.7254 82.0572L30.4305 79.3527L30.3846 79.3392L29.1456 81.7774Z" fill="#018687" fill-opacity="0.502"/>
<path d="M39.3201 77.895C40.0313 77.8867 40.6721 77.9861 41.2408 78.1947C41.8127 78.4001 42.2667 78.7082 42.6043 79.1202C42.9452 79.5341 43.1189 80.0392 43.1287 80.6393L40.854 80.6714C40.8491 80.3685 40.7098 80.1227 40.4345 79.9353C40.1591 79.7447 39.8265 79.6516 39.4381 79.6548C38.8776 79.6644 38.4564 79.821 38.1729 80.124C37.8927 80.4237 37.755 80.846 37.7599 81.3883L37.773 82.0731C37.7763 82.6173 37.9238 83.0345 38.2122 83.3246C38.504 83.6159 38.9301 83.7578 39.4905 83.7482C39.8789 83.7449 40.2083 83.6506 40.4804 83.4677C40.7508 83.2854 40.8835 83.0505 40.8802 82.7636L39.2021 82.7835L39.1824 81.2843L43.1353 81.2323L43.1877 85.3449L41.9553 85.3648L41.7128 84.6345C41.0441 85.2179 40.1625 85.5144 39.071 85.5272C37.8418 85.5439 36.9159 85.2377 36.2915 84.6017C35.6655 83.9683 35.3443 83.0178 35.3279 81.7535C35.3148 80.5071 35.6524 79.5553 36.344 78.8988C37.0388 78.2435 38.0304 77.9079 39.3201 77.895Z" fill="#018687" fill-opacity="0.502"/>
<path d="M44.4475 77.768L50.7341 76.5293L51.0815 78.2564L47.1287 79.0387L47.3253 80.036L50.7078 79.3647L51.0422 81.027L47.6596 81.6983L47.8759 82.806L51.9009 82.0108L52.2483 83.745L45.8897 84.9964L44.4475 77.768Z" fill="#018687" fill-opacity="0.502"/>
<path d="M59.6204 81.0973L55.3922 78.9203L56.7229 82.2448L54.6907 83.0528L51.9441 76.2024L53.8779 75.4335L58.1192 77.6624L56.7688 74.286L58.8009 73.4844L61.5476 80.3349L59.6204 81.0973Z" fill="#018687" fill-opacity="0.502"/>
<path d="M64.7215 72.0953L67.6652 76.7361L65.6462 78.0075L62.7027 73.3667L60.6443 74.6573L59.6283 73.0663L65.7512 69.2207L66.7668 70.8117L64.7215 72.0953Z" fill="#018687" fill-opacity="0.502"/>
<path d="M12.8344 75.4662C12.2313 76.2749 11.5135 76.7017 10.6842 76.7504C9.85334 76.7979 8.96671 76.4732 8.02277 75.7794C7.06897 75.0786 6.49208 74.3277 6.29214 73.5242C6.09548 72.7206 6.2987 71.9139 6.90178 71.1059L9.53701 67.5664L11.4511 68.9681L8.83563 72.4812C8.58647 72.8169 8.47672 73.1461 8.50784 73.4722C8.54223 73.8008 8.73075 74.0928 9.0716 74.345C9.41246 74.5927 9.74679 74.6845 10.0746 74.619C10.4056 74.5536 10.6941 74.3585 10.9399 74.0324L13.5554 70.5193L15.4696 71.9274L12.8344 75.4662Z" fill="#018687" fill-opacity="0.302"/>
<path d="M22.094 77.7128C21.8973 78.1171 21.6089 78.4303 21.2286 78.6518C20.8517 78.8732 20.4175 78.9643 19.9241 78.9252L20.0618 82.4126L17.6495 81.2651L17.6429 78.2737L16.8431 77.8957L15.6698 80.3204L13.5261 79.2968L16.7317 72.6484L20.8222 74.5976C21.2975 74.8241 21.6613 75.111 21.9104 75.4576C22.1628 75.8067 22.3005 76.1783 22.3234 76.5723C22.3496 76.9638 22.2726 77.3451 22.094 77.7128ZM19.8586 76.7617C19.9454 76.5839 19.9586 76.4093 19.8979 76.2399C19.8356 76.0673 19.7209 75.9402 19.5505 75.8619L18.1411 75.1906L17.5511 76.4158L18.9605 77.0871C19.1309 77.1654 19.3014 77.1756 19.4718 77.1134C19.6456 77.0486 19.775 76.9311 19.8586 76.7617Z" fill="#018687" fill-opacity="0.302"/>
<path d="M31.5225 85.3281L31.3979 84.2525L28.8413 83.7962L28.3497 84.7608L25.9504 84.3372L29.923 77.5586L32.6565 78.0406L34.0004 85.771L31.5225 85.3281ZM29.6214 82.2514L31.2013 82.5318L30.9062 79.8267L30.8604 79.8138L29.6214 82.2514Z" fill="#018687" fill-opacity="0.302"/>
<path d="M39.7966 78.3657C40.5079 78.3574 41.1486 78.4568 41.7173 78.6654C42.2893 78.8708 42.7432 79.1789 43.0808 79.5915C43.4217 80.0049 43.5954 80.5099 43.6053 81.11L41.3305 81.1427C41.3256 80.8392 41.1863 80.5934 40.911 80.406C40.6357 80.2154 40.303 80.1223 39.9146 80.1255C39.3541 80.1358 38.9329 80.2917 38.6494 80.5953C38.3692 80.895 38.2315 81.3167 38.2364 81.8596L38.2496 82.5438C38.2528 83.088 38.4003 83.5052 38.6888 83.7953C38.9805 84.0873 39.4065 84.2285 39.967 84.2188C40.3554 84.2156 40.6849 84.1213 40.9569 83.939C41.2273 83.7561 41.3601 83.5219 41.3568 83.235L39.6786 83.2542L39.659 81.755L43.6118 81.703L43.6642 85.8156L42.4318 85.8355L42.1893 85.1052C41.5207 85.6885 40.639 85.9851 39.5475 85.9985C38.3184 86.0146 37.3925 85.7085 36.768 85.0731C36.142 84.439 35.8208 83.4891 35.8044 82.2248C35.7913 80.9778 36.1289 80.026 36.8205 79.3695C37.5153 78.7148 38.5069 78.3792 39.7966 78.3657Z" fill="#018687" fill-opacity="0.302"/>
<path d="M44.924 78.2426L51.2105 77.0039L51.5579 78.7316L47.605 79.5133L47.8017 80.5107L51.1843 79.8393L51.5186 81.5016L48.136 82.1729L48.3524 83.2813L52.3773 82.4861L52.7248 84.2196L46.3661 85.4711L44.924 78.2426Z" fill="#018687" fill-opacity="0.302"/>
<path d="M60.0969 81.5739L55.8688 79.3969L57.1995 82.7214L55.1673 83.5294L52.4207 76.6789L54.3545 75.9101L58.5958 78.139L57.2454 74.7625L59.2775 73.9609L62.0242 80.8114L60.0969 81.5739Z" fill="#018687" fill-opacity="0.302"/>
<path d="M65.1987 72.5679L68.1417 77.2087L66.1227 78.4795L63.1793 73.8387L61.121 75.1293L60.1049 73.539L66.2277 69.6934L67.2433 71.2837L65.1987 72.5679Z" fill="#018687" fill-opacity="0.302"/>
<path d="M11.8814 74.5189C11.2783 75.327 10.5604 75.7538 9.73118 75.8032C8.90033 75.85 8.0137 75.5259 7.06976 74.8315C6.11594 74.1313 5.53908 73.3798 5.33914 72.5762C5.14248 71.7734 5.3457 70.9666 5.94879 70.158L8.584 66.6191L10.4982 68.0202L7.88262 71.5333C7.63352 71.869 7.5237 72.1982 7.55482 72.5243C7.58927 72.8535 7.77773 73.1449 8.11859 73.3977C8.45945 73.6455 8.79378 73.7366 9.12157 73.6718C9.45257 73.6063 9.74105 73.4106 9.98688 73.0845L12.6025 69.5714L14.5166 70.9795L11.8814 74.5189Z" fill="#1C1C1C"/>
<path d="M21.1409 76.7675C20.9442 77.1712 20.6558 77.4844 20.2756 77.7058C19.8987 77.9279 19.4644 78.019 18.9711 77.9798L19.1087 81.4667L16.6964 80.3198L16.6898 77.3278L15.8901 76.9498L14.7167 79.3745L12.5731 78.3514L15.7787 71.7031L19.8692 73.6516C20.3444 73.8782 20.7083 74.165 20.9574 74.5122C21.2097 74.8607 21.3474 75.2323 21.3703 75.627C21.3966 76.0179 21.3195 76.3991 21.1409 76.7675ZM18.9055 75.8157C18.9924 75.6379 19.0055 75.464 18.9449 75.2946C18.8826 75.1213 18.7679 74.9942 18.5974 74.9166L17.1881 74.2446L16.598 75.4704L18.0075 76.1418C18.1779 76.2201 18.3483 76.2297 18.5188 76.1674C18.6925 76.1026 18.822 75.9852 18.9055 75.8157Z" fill="#1C1C1C"/>
<path d="M30.5701 84.3789L30.4455 83.3033L27.8889 82.8469L27.3973 83.8116L24.998 83.388L28.9706 76.6094L31.7041 77.092L33.048 84.8224L30.5701 84.3789ZM28.669 81.3021L30.2489 81.5826L29.9539 78.8775L29.908 78.8646L28.669 81.3021Z" fill="#1C1C1C"/>
<path d="M38.8435 77.4185C39.5548 77.4101 40.1955 77.5096 40.7642 77.7182C41.3362 77.9235 41.7901 78.2316 42.1277 78.6436C42.4686 79.0576 42.6423 79.5627 42.6521 80.1621L40.3774 80.1948C40.3726 79.8919 40.2332 79.6455 39.9579 79.4581C39.6826 79.2675 39.3499 79.175 38.9615 79.1782C38.401 79.1879 37.9798 79.3445 37.6963 79.6474C37.4161 79.9471 37.2784 80.3694 37.2833 80.9117L37.2964 81.5965C37.2997 82.1408 37.4472 82.5579 37.7357 82.848C38.0273 83.1394 38.4534 83.2812 39.0139 83.2716C39.4024 83.2684 39.7318 83.1734 40.0038 82.9911C40.2742 82.8089 40.4069 82.574 40.4037 82.2871L38.7255 82.307L38.7058 80.8078L42.6587 80.7551L42.7111 84.8684L41.4788 84.8876L41.2362 84.1579C40.5675 84.7413 39.6858 85.0378 38.5944 85.0506C37.3653 85.0673 36.4393 84.7605 35.8149 84.1252C35.1889 83.4911 34.8677 82.5412 34.8513 81.2769C34.8382 80.0305 35.1758 79.0788 35.8674 78.4222C36.5623 77.7669 37.5537 77.4313 38.8435 77.4185Z" fill="#1C1C1C"/>
<path d="M43.9709 77.2947L50.2575 76.0566L50.6049 77.7837L46.6521 78.566L46.8487 79.5634L50.2313 78.8921L50.5656 80.5537L47.1831 81.225L47.3994 82.3334L51.4243 81.5382L51.7718 83.2717L45.4131 84.5231L43.9709 77.2947Z" fill="#1C1C1C"/>
<path d="M59.1445 80.6246L54.9164 78.4477L56.2471 81.7715L54.2149 82.5802L51.4683 75.7297L53.4021 74.9602L57.6434 77.1898L56.293 73.8133L58.3251 73.0117L61.0718 79.8622L59.1445 80.6246Z" fill="#1C1C1C"/>
<path d="M64.2458 71.6207L67.1895 76.2615L65.1705 77.5322L62.2269 72.8914L60.1685 74.1821L59.1525 72.5917L65.2749 68.7461L66.2911 70.3365L64.2458 71.6207Z" fill="#1C1C1C"/>
</g>
<defs>
<linearGradient id="paint0_linear_552_124" x1="22.1124" y1="31.3606" x2="22.1124" y2="55.3762" gradientUnits="userSpaceOnUse">
<stop stop-color="#017B7F"/>
<stop offset="0.015625" stop-color="#017A7E"/>
<stop offset="0.03125" stop-color="#017A7E"/>
<stop offset="0.046875" stop-color="#01797D"/>
<stop offset="0.0625" stop-color="#01787D"/>
<stop offset="0.078125" stop-color="#01777C"/>
<stop offset="0.09375" stop-color="#01777B"/>
<stop offset="0.109375" stop-color="#01767B"/>
<stop offset="0.125" stop-color="#01757A"/>
<stop offset="0.140625" stop-color="#01747A"/>
<stop offset="0.15625" stop-color="#017379"/>
<stop offset="0.171875" stop-color="#017379"/>
<stop offset="0.1875" stop-color="#017278"/>
<stop offset="0.203125" stop-color="#017177"/>
<stop offset="0.21875" stop-color="#017077"/>
<stop offset="0.234375" stop-color="#017076"/>
<stop offset="0.25" stop-color="#016F76"/>
<stop offset="0.265625" stop-color="#016E75"/>
<stop offset="0.28125" stop-color="#016D75"/>
<stop offset="0.296875" stop-color="#016D74"/>
<stop offset="0.3125" stop-color="#016C74"/>
<stop offset="0.328125" stop-color="#016B73"/>
<stop offset="0.34375" stop-color="#016A72"/>
<stop offset="0.359375" stop-color="#016A72"/>
<stop offset="0.375" stop-color="#016971"/>
<stop offset="0.390625" stop-color="#016871"/>
<stop offset="0.40625" stop-color="#016770"/>
<stop offset="0.421875" stop-color="#016770"/>
<stop offset="0.4375" stop-color="#01666F"/>
<stop offset="0.453125" stop-color="#01656E"/>
<stop offset="0.46875" stop-color="#01646E"/>
<stop offset="0.484375" stop-color="#01636D"/>
<stop offset="0.5" stop-color="#01636D"/>
<stop offset="0.515625" stop-color="#01626C"/>
<stop offset="0.53125" stop-color="#01616C"/>
<stop offset="0.546875" stop-color="#01606B"/>
<stop offset="0.5625" stop-color="#01606A"/>
<stop offset="0.578125" stop-color="#015F6A"/>
<stop offset="0.59375" stop-color="#015E69"/>
<stop offset="0.609375" stop-color="#015D69"/>
<stop offset="0.625" stop-color="#015D68"/>
<stop offset="0.640625" stop-color="#015C68"/>
<stop offset="0.65625" stop-color="#015B67"/>
<stop offset="0.671875" stop-color="#015A66"/>
<stop offset="0.6875" stop-color="#015A66"/>
<stop offset="0.703125" stop-color="#015965"/>
<stop offset="0.71875" stop-color="#015865"/>
<stop offset="0.734375" stop-color="#015764"/>
<stop offset="0.75" stop-color="#015764"/>
<stop offset="0.765625" stop-color="#015663"/>
<stop offset="0.78125" stop-color="#015563"/>
<stop offset="0.796875" stop-color="#015462"/>
<stop offset="0.8125" stop-color="#015361"/>
<stop offset="0.828125" stop-color="#015361"/>
<stop offset="0.84375" stop-color="#015260"/>
<stop offset="0.859375" stop-color="#015160"/>
<stop offset="0.875" stop-color="#01505F"/>
<stop offset="0.890625" stop-color="#01505F"/>
<stop offset="0.90625" stop-color="#014F5E"/>
<stop offset="0.921875" stop-color="#014E5D"/>
<stop offset="0.9375" stop-color="#014D5D"/>
<stop offset="0.953125" stop-color="#014D5C"/>
<stop offset="0.96875" stop-color="#014C5C"/>
<stop offset="0.984375" stop-color="#014B5B"/>
<stop offset="1" stop-color="#014A5B"/>
</linearGradient>
<linearGradient id="paint1_linear_552_124" x1="50.2071" y1="17.7799" x2="50.2071" y2="53.9157" gradientUnits="userSpaceOnUse">
<stop stop-color="#018687"/>
<stop offset="0.125" stop-color="#018687"/>
<stop offset="0.1875" stop-color="#018687"/>
<stop offset="0.21875" stop-color="#018687"/>
<stop offset="0.234375" stop-color="#018587"/>
<stop offset="0.242188" stop-color="#018586"/>
<stop offset="0.25" stop-color="#018486"/>
<stop offset="0.257812" stop-color="#018485"/>
<stop offset="0.265625" stop-color="#018385"/>
<stop offset="0.273438" stop-color="#018284"/>
<stop offset="0.28125" stop-color="#018284"/>
<stop offset="0.289062" stop-color="#018184"/>
<stop offset="0.296875" stop-color="#018183"/>
<stop offset="0.304688" stop-color="#018083"/>
<stop offset="0.3125" stop-color="#018082"/>
<stop offset="0.320312" stop-color="#017F82"/>
<stop offset="0.328125" stop-color="#017E81"/>
<stop offset="0.335938" stop-color="#017E81"/>
<stop offset="0.34375" stop-color="#017D81"/>
<stop offset="0.351562" stop-color="#017D80"/>
<stop offset="0.359375" stop-color="#017C80"/>
<stop offset="0.367188" stop-color="#017C7F"/>
<stop offset="0.375" stop-color="#017B7F"/>
<stop offset="0.382812" stop-color="#017A7E"/>
<stop offset="0.390625" stop-color="#017A7E"/>
<stop offset="0.398438" stop-color="#01797E"/>
<stop offset="0.40625" stop-color="#01797D"/>
<stop offset="0.414062" stop-color="#01787D"/>
<stop offset="0.421875" stop-color="#01787C"/>
<stop offset="0.429688" stop-color="#01777C"/>
<stop offset="0.4375" stop-color="#01767B"/>
<stop offset="0.445312" stop-color="#01767B"/>
<stop offset="0.453125" stop-color="#01757B"/>
<stop offset="0.460937" stop-color="#01757A"/>
<stop offset="0.46875" stop-color="#01747A"/>
<stop offset="0.476562" stop-color="#017479"/>
<stop offset="0.484375" stop-color="#017379"/>
<stop offset="0.492187" stop-color="#017278"/>
<stop offset="0.5" stop-color="#017278"/>
<stop offset="0.507812" stop-color="#017178"/>
<stop offset="0.515625" stop-color="#017177"/>
<stop offset="0.523437" stop-color="#017077"/>
<stop offset="0.53125" stop-color="#017076"/>
<stop offset="0.539062" stop-color="#016F76"/>
<stop offset="0.546875" stop-color="#016E75"/>
<stop offset="0.554687" stop-color="#016E75"/>
<stop offset="0.5625" stop-color="#016D75"/>
<stop offset="0.570312" stop-color="#016D74"/>
<stop offset="0.578125" stop-color="#016C74"/>
<stop offset="0.585937" stop-color="#016C73"/>
<stop offset="0.59375" stop-color="#016B73"/>
<stop offset="0.601562" stop-color="#016A72"/>
<stop offset="0.609375" stop-color="#016A72"/>
<stop offset="0.617188" stop-color="#016972"/>
<stop offset="0.625" stop-color="#016971"/>
<stop offset="0.632812" stop-color="#016871"/>
<stop offset="0.640625" stop-color="#016870"/>
<stop offset="0.648438" stop-color="#016770"/>
<stop offset="0.65625" stop-color="#01666F"/>
<stop offset="0.664062" stop-color="#01666F"/>
<stop offset="0.671875" stop-color="#01656F"/>
<stop offset="0.679688" stop-color="#01656E"/>
<stop offset="0.6875" stop-color="#01646E"/>
<stop offset="0.695313" stop-color="#01646D"/>
<stop offset="0.703125" stop-color="#01636D"/>
<stop offset="0.710938" stop-color="#01626D"/>
<stop offset="0.71875" stop-color="#01626C"/>
<stop offset="0.726563" stop-color="#01616C"/>
<stop offset="0.734375" stop-color="#01616B"/>
<stop offset="0.742188" stop-color="#01606B"/>
<stop offset="0.75" stop-color="#01606A"/>
<stop offset="0.757812" stop-color="#015F6A"/>
<stop offset="0.765625" stop-color="#015E6A"/>
<stop offset="0.773438" stop-color="#015E69"/>
<stop offset="0.78125" stop-color="#015D69"/>
<stop offset="0.789062" stop-color="#015D68"/>
<stop offset="0.796875" stop-color="#015C68"/>
<stop offset="0.804688" stop-color="#015C67"/>
<stop offset="0.8125" stop-color="#015B67"/>
<stop offset="0.820312" stop-color="#015A67"/>
<stop offset="0.828125" stop-color="#015A66"/>
<stop offset="0.835938" stop-color="#015966"/>
<stop offset="0.84375" stop-color="#015965"/>
<stop offset="0.851562" stop-color="#015865"/>
<stop offset="0.859375" stop-color="#015864"/>
<stop offset="0.867188" stop-color="#015764"/>
<stop offset="0.875" stop-color="#015664"/>
<stop offset="0.882812" stop-color="#015663"/>
<stop offset="0.890625" stop-color="#015563"/>
<stop offset="0.898438" stop-color="#015562"/>
<stop offset="0.90625" stop-color="#015462"/>
<stop offset="0.914062" stop-color="#015461"/>
<stop offset="0.921875" stop-color="#015361"/>
<stop offset="0.929688" stop-color="#015261"/>
<stop offset="0.9375" stop-color="#015260"/>
<stop offset="0.945312" stop-color="#015160"/>
<stop offset="0.953125" stop-color="#01515F"/>
<stop offset="0.960938" stop-color="#01505F"/>
<stop offset="0.96875" stop-color="#01505E"/>
<stop offset="0.976562" stop-color="#014F5E"/>
<stop offset="0.984375" stop-color="#014E5E"/>
<stop offset="0.992188" stop-color="#014E5D"/>
<stop offset="1" stop-color="#014D5D"/>
</linearGradient>
<linearGradient id="paint2_linear_552_124" x1="45.9416" y1="36.1854" x2="45.9416" y2="45.107" gradientUnits="userSpaceOnUse">
<stop stop-color="#017177"/>
<stop offset="0.03125" stop-color="#017177"/>
<stop offset="0.0625" stop-color="#017077"/>
<stop offset="0.09375" stop-color="#016F76"/>
<stop offset="0.125" stop-color="#016F76"/>
<stop offset="0.15625" stop-color="#016E75"/>
<stop offset="0.1875" stop-color="#016E75"/>
<stop offset="0.21875" stop-color="#016D75"/>
<stop offset="0.25" stop-color="#016D74"/>
<stop offset="0.28125" stop-color="#016C74"/>
<stop offset="0.3125" stop-color="#016C73"/>
<stop offset="0.34375" stop-color="#016B73"/>
<stop offset="0.375" stop-color="#016A72"/>
<stop offset="0.40625" stop-color="#016A72"/>
<stop offset="0.4375" stop-color="#016972"/>
<stop offset="0.46875" stop-color="#016971"/>
<stop offset="0.5" stop-color="#016871"/>
<stop offset="0.53125" stop-color="#016870"/>
<stop offset="0.5625" stop-color="#016770"/>
<stop offset="0.59375" stop-color="#016670"/>
<stop offset="0.625" stop-color="#01666F"/>
<stop offset="0.65625" stop-color="#01656F"/>
<stop offset="0.6875" stop-color="#01656E"/>
<stop offset="0.71875" stop-color="#01646E"/>
<stop offset="0.75" stop-color="#01646D"/>
<stop offset="0.78125" stop-color="#01636D"/>
<stop offset="0.8125" stop-color="#01626D"/>
<stop offset="0.84375" stop-color="#01626C"/>
<stop offset="0.875" stop-color="#01616C"/>
<stop offset="0.90625" stop-color="#01616B"/>
<stop offset="0.9375" stop-color="#01606B"/>
<stop offset="0.96875" stop-color="#01606A"/>
<stop offset="1" stop-color="#015F6A"/>
</linearGradient>
<linearGradient id="paint3_linear_552_124" x1="43.6523" y1="28.8337" x2="43.6523" y2="31.3588" gradientUnits="userSpaceOnUse">
<stop stop-color="#018082"/>
<stop offset="0.125" stop-color="#017F82"/>
<stop offset="0.25" stop-color="#017F82"/>
<stop offset="0.375" stop-color="#017E81"/>
<stop offset="0.5" stop-color="#017E81"/>
<stop offset="0.625" stop-color="#017D80"/>
<stop offset="0.75" stop-color="#017C80"/>
<stop offset="0.875" stop-color="#017C7F"/>
<stop offset="1" stop-color="#017B7F"/>
</linearGradient>
<linearGradient id="paint4_linear_552_124" x1="49.4661" y1="29.3401" x2="49.4661" y2="31.9774" gradientUnits="userSpaceOnUse">
<stop stop-color="#017F82"/>
<stop offset="0.125" stop-color="#017E81"/>
<stop offset="0.25" stop-color="#017E81"/>
<stop offset="0.375" stop-color="#017D80"/>
<stop offset="0.5" stop-color="#017C80"/>
<stop offset="0.625" stop-color="#017C7F"/>
<stop offset="0.75" stop-color="#017B7F"/>
<stop offset="0.875" stop-color="#017A7E"/>
<stop offset="1" stop-color="#017A7E"/>
</linearGradient>
<clipPath id="clip0_552_124">
<rect width="73" height="86" fill="white"/>
</clipPath>
</defs>
</svg>
