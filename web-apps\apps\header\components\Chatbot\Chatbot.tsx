import { Chatbot } from 'react-chatbot-kit';
import config from './config';
import MessageParser from './MessageParser';
import 'react-chatbot-kit/build/main.css';
import BotAvatar from './PwBotAvatar';
import { useEffect, useState } from 'react';
import ChatHeader from './ChatHeader';
import ActionProvider from '../../store/ActionProvider';

const GlobalChatBot = () => {
  const { state } = config;
  const [isChatbotOpen, setIsChatbotOpen] = useState(false);
  const [messages, setMessages] = useState([]);

  const validateInput = (input) => {
    return input.length > 0 && input.trim().length !== 0;
  };

  useEffect(() => {
    if (isChatbotOpen) {
      const savedMessages = sessionStorage.getItem('chatbot_messages');
      if (savedMessages) {
        try {
          const parsedMessages = JSON.parse(savedMessages);
          if (Array.isArray(parsedMessages)) {
            setMessages(parsedMessages);
          }
        } catch (error) {
          console.error("Failed to parse chatbot messages:", error);
        }
      }
    }
  }, [isChatbotOpen]);

  const onSaveMessages = (newMessages) => {
    if (Array.isArray(newMessages)) {
      setMessages(newMessages);
      sessionStorage.setItem('chatbot_messages', JSON.stringify(newMessages));
    }
  };

  const updatedConfig = {
    ...config,
    customComponents: {
      ...config.customComponents,
      header: () => <ChatHeader setIsChatbotOpen={setIsChatbotOpen} />,
    },
  };

  return (
    <>
      <div
        className={`workflow-creation-bot ${isChatbotOpen ? 'show' : ''}`}
        role="dialog"
        aria-modal="true"
      >
        <div
          className="modal-backdrop"
          onClick={() => setIsChatbotOpen(false)}
          role="button"
          aria-label="Close modal"
          style={{
            backdropFilter: 'blur(8px)',
            WebkitBackdropFilter: 'blur(8px)', // Safari support
            backgroundColor: 'rgba(0, 0, 0, 0.3)' // Semi-transparent overlay
          }}
        />
        <div
          className="chatbot-content"
          onClick={(e) => e.stopPropagation()}
        >
          <Chatbot
            key={state.sessionId}
            validator={validateInput}
            config={updatedConfig}
            actionProvider={ActionProvider}
            messageParser={(props) => <MessageParser {...props} state={state} />}
            headerText="uR Agent"
            saveMessages={onSaveMessages}
            messageHistory={messages.length > 0 ? messages : undefined}
          />
        </div>
      </div>
      <div className="chatbot-avatar-container">
        <BotAvatar
          onClick={() => setIsChatbotOpen(!isChatbotOpen)}
          className="widget-button"
          data-testid="chatbot-avatar"
        />
      </div>
    </>
  );
};

export default GlobalChatBot;