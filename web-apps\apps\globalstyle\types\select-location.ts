export interface SelectLocationProp {
    fields:ISelectLocationField[],
    title?:{
        name:string|React.ReactElement,
        className:string,
    },
    className?:string,
    customFields?:[{
        element:React.ReactElement,
        className?:string,
    }]
}

export interface ISelectLocationField{
    label?:{
        name:string,
        className?:string,
        isRequired?:boolean
    },
    type: 'country'| 'state'| 'city'| 'zip'| 'custom',
    selectedValue: any,
    setSelectedValue: (any) => void,
    onChange?: (value) => void,
    value?: any,
    placeHolder?: string,
    options?: any[],
    optionLabel?: string,
    className?: string,
    customSelectStyle?: any,
    error?: {
        message: string,
        className: string
    },
    isDisabled?: boolean
}