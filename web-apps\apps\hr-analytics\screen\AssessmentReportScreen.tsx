import {
  memo,
  useCallback,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from "react";
import { Link, Outlet, useNavigate } from "react-router-dom";
import {
  AnalyticsAreaChartComponent,
  AnalyticsCircleChartComponent,
  getConfig,
  TableCardViewComp,
  useHasPermission,
} from "@ucrecruits/globalstyle/src/ucrecruits-globalstyle";
import useTableClickAndDragScroll from "../hook/useTableClickAndDragScroll";
import { store, useTypedSelector } from "../store";
import FiltersAssessmentsAnalytics from '../components/Report/Assessments/Filters/FiltersAssessmentsAnalytics';
import axios from "axios";
import { getEnv } from "@urecruits/api";
import { setAssessmentsStatusPie } from "../store/reducers/assessments/assessmentsStatusPieReducer";
import {
  setAreaAssessments,
  setAreaDuration,
  setAreaStatus,
} from "../store/reducers/assessments/assessmentDetailViewReducerArea";
import {
  defaultFiltersAssessments,
  setCurrentPageAssessments,
  setAssessmentTypeFilterAssessments,
  setEmptyFilterAssessments,
  setEmptySearchAssessments,
  setIsLoadingAssessments,
  setLimitAssessments,
  setSearchValueAssessments,
  setSortByFilterAssessments,
  setSortTypeFilterAssessments,
  setStatusFilterAssessments,
  setTableItemsAssessments,
  setTotalCountAssessments
} from "../store/reducers/assessments/assessmentsTableReducer";
import { transformDate } from "../utils/transformDate";
import { AssessmentsTab } from "../enums/assessments/assessmentsEnums";
import { IColumns } from "@ucrecruits/globalstyle/types/table-types";

const { API_HR_ANALYTICS } = getEnv();

const TabColumns = [
  {
    activeTab: "allAssessments",
    columns: [
      AssessmentsTab.NAME,
      AssessmentsTab.TYPE,
      AssessmentsTab.CREATED_AT,
      AssessmentsTab.STATUS,
    ],
  },
  {
    activeTab: "byStatus",
    columns: [
      AssessmentsTab.NAME,
      AssessmentsTab.TYPE,
      AssessmentsTab.UPDATED_AT,
      AssessmentsTab.STATUS,
    ],
  },
  {
    activeTab: "byType",
    columns: [
      AssessmentsTab.NAME,
      AssessmentsTab.TYPE,
      AssessmentsTab.LANGUAGE_ID,
      AssessmentsTab.PACKAGE_ID,
    ],
  },
];

const tableFunc = (state) => state.assessments;
const assessmentsStatusPieFunc = (state) => state.assessments_status_pie;
const assessmentsAreaFunc = (state) => state.assessments_area;
const FilterItems = (state) => <FiltersAssessmentsAnalytics activeTab={state} />;

const columns: Array<IColumns> = [
  {
    field: "name",
    headerName: AssessmentsTab.NAME,
    visibility: true,
    isMobileTitle: true,
    pinnable: true,
    renderCell: (cellValue) => <p className="assessments__name__text">{cellValue || ""}</p>,
    mobileRenderCell: (cellValue) => <p className="assessments__name__text">{cellValue || ""}</p>,
  },
  {
    field: "status",
    headerName: AssessmentsTab.STATUS,
    visibility: true,
    renderCell: (cellValue) => (
      <p
        className={`table__status-block ${cellValue === "ACTIVE"
            ? "table__status-block--green"
            : "table__status-block--red"
          } assessments__status`}
      >
        {cellValue || "N/A"}
      </p>
    ),
    mobileRenderCell: (cellValue) => (
      <p
        className={`table__status-block ${cellValue === "ACTIVE"
            ? "table__status-block--green"
            : "table__status-block--red"
          } assessments__status`}
      >
        {cellValue || "N/A"}
      </p>
    ),
  },
  {
    field: "type",
    headerName: AssessmentsTab.TYPE,
    visibility: true,
    renderCell: (cellValue) => <p>{cellValue || "N/A"}</p>,
  },
  {
    field: "languageId",
    headerName: AssessmentsTab.LANGUAGE_ID,
    visibility: false,
  },
  {
    field: "packageId",
    headerName: AssessmentsTab.PACKAGE_ID,
    visibility: false,
  },
  {
    field: "createdAt",
    headerName: AssessmentsTab.CREATED_AT,
    visibility: true,
    renderCell: (cellValue) => <p>{cellValue || "N/A"}</p>,
  },
  {
    field: "updatedAt",
    headerName: AssessmentsTab.UPDATED_AT,
    visibility: false,
    renderCell: (cellValue) => <p>{cellValue || "N/A"}</p>,
  },
];

const AssessmentReportScreen = () => {
  const navigate = useNavigate();
  const { checkUserPermission } = useHasPermission();
  const [columnsArr, setColumnsArr] = useState(columns);
  const [isEmptyFilter, setIsEmptyFilter] = useState<boolean>(false);
  const [isFilterSubmit, setIsFilterSubmit] = useState(false);

  const tableRef = useRef(null);

  const table = useTypedSelector(tableFunc);
  const pie = useTypedSelector(assessmentsStatusPieFunc);
  const area = useTypedSelector(assessmentsAreaFunc);

  const filterArray = [
    table.filters.searchValue,
    table.filters.sortBy,
    table.filters.sortType,
    table.pagination.limit,
  ];

  const filterAssessmentsByStatus = (type) => {
    setAssessmentsAreaHandler(area.duration, type);
  };

  const areaChartOperations = [
    {
      buttonText: "Take-Home",
      onChange: filterAssessmentsByStatus,
      isActive: area?.assessmentStatus === "take-home" ? "active" : "",
      buttonValue: "take-home",
    },
    {
      buttonText: "Live-Coding",
      onChange: filterAssessmentsByStatus,
      isActive: area?.assessmentStatus === "live-task" ? "active" : "",
      buttonValue: "live-task",
    },
    {
      buttonText: "Domain",
      onChange: filterAssessmentsByStatus,
      isActive: area?.assessmentStatus === "domain" ? "active" : "",
      buttonValue: "domain",
    },
    {
      buttonText: "All",
      onChange: filterAssessmentsByStatus,
      isActive: area?.assessmentStatus === "all" ? "active" : "",
      buttonValue: "all",
    },
  ];

  const changeAreaHandler = (duration) => {
    setAssessmentsAreaHandler(duration, area.assessmentStatus);
  };

  useLayoutEffect(() => {
    setAssessmentsHandler(
      table.pagination.currentPage,
      table.pagination.limit,
      table.filters
    );
    setAssessmentsStatusPieHandler();
    setAssessmentsAreaHandler("week", "all");
  }, []);

  useEffect(() => {
    if (isFilterSubmit) {
      setAssessmentsHandler(
        1,
        table.pagination.limit,
        table.filters
      );
      setIsFilterSubmit(false);
    }
  }, [isFilterSubmit, table.filters, table.pagination.limit]);

  useEffect(() => {
    const hasFilters =
      table.filters.assessmentType.length > 0 ||
      table.filters.status.length > 0 ||
      table.filters.dateOfCreation.from ||
      table.filters.dateOfCreation.to ||
      (table.filters.dateOfCreation.duration &&
        table.filters.dateOfCreation.duration !== "All time");

    setIsEmptyFilter(!hasFilters);

    setAssessmentsHandler(
      1,
      table.pagination.limit,
      table.filters
    );
  }, [
    table.filters.assessmentType,
    table.filters.status,
    table.filters.dateOfCreation.from,
    table.filters.dateOfCreation.to,
    table.filters.dateOfCreation.duration,
  ]);

  useEffect(() => {
    document.addEventListener("visibilitychange", visibilityChangeFunc);
    return () =>
      document.removeEventListener("visibilitychange", visibilityChangeFunc);
  }, [...filterArray, table.pagination.currentPage]);

  useEffect(() => {
    switch (table.activeTab) {
      case "allAssessments":
        setColumnsArr(columns.map((i) =>
          TabColumns[0].columns.includes(i.headerName as AssessmentsTab)
            ? { ...i, visibility: true }
            : { ...i, visibility: false }
        ));
        break;
      case "byStatus":
        setColumnsArr(columns.map((i) =>
          TabColumns[1].columns.includes(i.headerName as AssessmentsTab)
            ? { ...i, visibility: true }
            : { ...i, visibility: false }
        ));
        break;
      case "byType":
        setColumnsArr(columns.map((i) =>
          TabColumns[2].columns.includes(i.headerName as AssessmentsTab)
            ? { ...i, visibility: true }
            : { ...i, visibility: false }
        ));
        break;
      default:
        setColumnsArr(columns);
    }
  }, [table.activeTab]);

  const visibilityChangeFunc = () => {
    if (document.visibilityState === "visible") {
      setAssessmentsHandler(
        table.pagination.currentPage,
        table.pagination.limit,
        table.filters
      );
    }
  };

  const setSearchCallback = useCallback(
    (value) => {
      store.dispatch(setSearchValueAssessments(value));
      setAssessmentsHandler(1, table.pagination.limit, {
        ...table.filters,
        searchValue: value,
      });
    },
    [...filterArray]
  );

  const emptyFilterCallback = useCallback(() => {
    resetFunc(table.pagination.limit);
  }, []);

  const emptyTableCallback = useCallback(() => {
    if (checkUserPermission("assessment", "add")) {
      navigate("/recruitment/manage-assessments/add-new");
      localStorage.setItem("prevRoute", "/hr-analytics/reports/assessments-report");
    } else {
      navigate("/hr-analytics/reports");
    }
  }, []);

  useTableClickAndDragScroll(tableRef);

  return (
    <section className="assessments-report">
      <Outlet />
      <div className="hr-analytics__table-screen-top">
        <ul className="hr-analytics__table-screen-top__breadcrumbs">
          <li className="hr-analytics__table-screen-top__breadcrumbs__item">
            <Link
              to="/hr-analytics/reports"
              className="hr-analytics__table-screen-top__breadcrumbs__link"
            >
              Reports Center
            </Link>
          </li>
          <li className="hr-analytics__table-screen-top__breadcrumbs__item">
            <p className="hr-analytics__table-screen-top__breadcrumbs__link disabled">
              Assessments Report
            </p>
          </li>
        </ul>
        <div className="hr-analytics__table-screen-top__wrapper">
          <h2 className="hr-analytics__table-screen-top__title">Assessments Report</h2>
        </div>
      </div>

      {table.activeTab === "allAssessments" && (
        <div className="graph-wrapper">
          <div className="graph-assessment-summary-circle">
            <AnalyticsCircleChartComponent
              title={"Assessments Status"}
              pieChartData={{
                data: pie.assessmentsData || [{ key: "No data", doc_count: 1 }],
                totalCount: pie.totalAssessments || 0,
                showCount: true
              }}
              pieColors={["#029CA5", "#FE4672", "#737980"]}
              countText={"Total Assessments"}
            />
          </div>
          <div className="graph-assessment-summary-area">
            <AnalyticsAreaChartComponent
              title=""
              data={{
                data: area.assessmentsData || [],
                duration: area.duration
              }}
              areaChartOperations={areaChartOperations}
              dataKey="doc_count"
              changeAreaHandler={changeAreaHandler}
              xAxisTickDataKey="key_as_string"
            />
          </div>
        </div>
      )}

      <TableCardViewComp
        columns={columnsArr}
        rowsData={{ rowData: table.tableItems || [] }}
        isLoading={table.isLoading}
        emptyTable={{
          buttonText: checkUserPermission("assessment", "add")
            ? "Add Assessment"
            : "Go to report center",
          handler: emptyTableCallback,
          desc:
            "Assessments table is empty" +
            (checkUserPermission("assessment", "add")
              ? ", Add your first assessment!"
              : "."),
          title: "Table is empty",
        }}
        searchField={{
          placeholder: "Search assessments",
          searchValue: table.filters.searchValue || "",
          setSearchValue: setSearchCallback,
        }}
        pagination={{
          currentPage: table.pagination.currentPage,
          limit: table.pagination.limit,
          totalCount: table.pagination.totalCount,
          setCurrentPage: (page) =>
            setAssessmentsHandler(page, table.pagination.limit, table.filters),
          setLimit: setLimitHandler,
          setTotalCount(count) {
            store.dispatch(setTotalCountAssessments(count));
          },
        }}
        filters={{
          filter: table.filters,
          filterData: table.filterInfo,
          filterItems: FilterItems,
          isEmptyFilter: isEmptyFilter,
          setIsEmptyFilter: setIsEmptyFilter,
          resetFilters: emptyFilterCallback,
          isFilterSubmit: isFilterSubmit,
          setIsFilterSubmit: setIsFilterSubmit,
        }}
      />
    </section>
  );
};

export const setAssessmentsHandler = (page: number, limit: number, filters: any) => {
  store.dispatch(setCurrentPageAssessments(page));
  const getData = async () => {
    store.dispatch(setIsLoadingAssessments(true));
    try {
      const currentFilters = store.getState().assessments.filters;
      const pagination = store.getState().assessments.pagination;
      let url =
        `${API_HR_ANALYTICS}/api/analytics/assessments?` +
        `limit=${encodeURIComponent(pagination.limit || 10)}` +
        `&offset=${encodeURIComponent((page - 1) * pagination.limit || 0)}` +
        `&search=${encodeURIComponent(currentFilters.searchValue || "")}` +
        `${currentFilters.assessmentType?.length
          ? `&assessmentType=${encodeURIComponent(
            currentFilters.assessmentType?.map((i) => i.id).join(",")
          )}`
          : ""
        }` +
        `${currentFilters.status?.length
          ? `&status=${encodeURIComponent(
            currentFilters.status?.map((i) => i.id).join(",")
          )}`
          : ""
        }` +
        `&sortBy=createdAt` +
        `&sortType=desc`;

      if (currentFilters.dateOfCreation.from) {
        url += `&from=${encodeURIComponent(currentFilters.dateOfCreation.from.toISOString())}`;
      }
      if (currentFilters.dateOfCreation.to) {
        url += `&to=${encodeURIComponent(currentFilters.dateOfCreation.to.toISOString())}`;
      }

      const res = await axios(url, getConfig());
      const items = transformArrayToTableData(res.data);
      store.dispatch(setTotalCountAssessments(res.data.hits?.total?.value || 0));
      store.dispatch(setTableItemsAssessments(items));
      store.dispatch(setCurrentPageAssessments(page));
    } catch (error) {
      console.error("Error fetching assessments details:", error);
      store.dispatch(setTableItemsAssessments([]));
    } finally {
      store.dispatch(setIsLoadingAssessments(false));
    }
  };
  getData();
};

export const setAssessmentsStatusPieHandler = () => {
  const getData = async () => {
    try {
      const res = await axios(`${API_HR_ANALYTICS}/api/analytics/assessments/pie`, getConfig());

      const bucketsObj = res.data?.assessments_types?.buckets || {};
      const data = Object.entries(bucketsObj).map(([key, value]: [string, any]) => ({
        key,
        doc_count: value.doc_count,
      }));
      const count = res.data?.total_assessments?.value || 0;

      store.dispatch(
        setAssessmentsStatusPie({
          assessmentsData: data.length > 0 ? data : [{ key: "No data", doc_count: 1 }],
          totalAssessments: count,
        })
      );
    } catch (error) {
      console.error("Error getting assessments pie data", error);
      store.dispatch(
        setAssessmentsStatusPie({
          assessmentsData: [{ key: "No data", doc_count: 1 }],
          totalAssessments: 0,
        })
      );
    }
  };
  getData();
};

export const setAssessmentsAreaHandler = (duration: string, typeFilter: string) => {
  const getData = async () => {
    try {
      store.dispatch(setAreaDuration(duration));
      store.dispatch(setAreaStatus(typeFilter));
      const res = await axios(
        `${API_HR_ANALYTICS}/api/analytics/assessments/area?duration=${duration}`,
        getConfig()
      );

      const assessmentsByType = res.data.assessments_by_type?.buckets || [];
      let timeSeriesData = [];

      if (typeFilter === "all") {
        const timeMap = new Map();
        assessmentsByType.forEach(typeBucket => {
          const timeBuckets = typeBucket.assessments_over_time?.buckets || [];
          timeBuckets.forEach(timeBucket => {
            const key = timeBucket.key;
            const existing = timeMap.get(key) || 0;
            timeMap.set(key, existing + timeBucket.doc_count);
          });
        });
        timeSeriesData = Array.from(timeMap.entries()).map(([key, doc_count]) => ({
          key,
          doc_count,
          key_as_string: new Date(key).toISOString().split('T')[0]
        }));
      } else {
        const selectedTypeBucket = assessmentsByType.find(bucket => bucket.key === typeFilter);
        if (selectedTypeBucket) {
          timeSeriesData = selectedTypeBucket.assessments_over_time?.buckets?.map(bucket => ({
            key: bucket.key,
            doc_count: bucket.doc_count,
            key_as_string: bucket.key_as_string || new Date(bucket.key).toISOString().split('T')[0]
          })) || [];
        }
      }

      timeSeriesData.sort((a, b) => a.key - b.key);
      store.dispatch(setAreaAssessments(timeSeriesData));
    } catch (err) {
      console.error("Error fetching assessments area data", err);
      store.dispatch(setAreaAssessments([]));
    }
  };
  getData();
};

const setLimitHandler = (value: any) => {
  store.dispatch(setLimitAssessments(value));
};

const transformArrayToTableData = (response: any) => {
  const hits = response?.hits?.hits || [];
  const transformed = hits
    .map((itemData: any, index: number) => {
      const assessment = itemData?._source;
      if (!assessment) {
        console.warn(`Skipping invalid assessment at index ${index}:`, itemData);
        return null;
      }
      return {
        id: assessment.assessmentId || `temp-id-${index}`,
        name: assessment.name || "N/A",
        type: assessment.assessmentType || "N/A",
        status: assessment.status || "N/A",
        languageId: assessment.languageId || null,
        packageId: assessment.packageId || null,
        createdAt: transformDate(assessment.createdAt) || "N/A",
        updatedAt: transformDate(assessment.updatedAt) || "N/A",
        companyId: assessment.companyId || null,
        description: assessment.description || "",
      };
    })
    .filter(Boolean);
  return transformed;
};

const resetFunc = (pagination) => {
  store.dispatch(setSearchValueAssessments(""));
  store.dispatch(setEmptyFilterAssessments(false));
  store.dispatch(setEmptySearchAssessments(false));
  store.dispatch(setAssessmentTypeFilterAssessments([]));
  store.dispatch(setStatusFilterAssessments([]));
  store.dispatch(setSortTypeFilterAssessments("desc"));
  store.dispatch(setSortByFilterAssessments("assessmentId"));
  setAssessmentsHandler(1, pagination, defaultFiltersAssessments);
};

export default memo(AssessmentReportScreen);