import React from "react";

const searchLogo = require("./../images/search.svg");

export const SearchInput = ({ searchValue, onSearchChange, setSearchValue }) => {
  return (
    <div className="search-input">
      <button
        onClick={onSearchChange}
        className="search-input__search-button btn--green"
      >
        <img
          src={searchLogo}
          alt="search-icon"
          className="search-input__icon"
        />
      </button>
      <input
        type="search"
        placeholder="Search coding questions"
        className="search-input__input"
        onChange={(e) => setSearchValue(e.target.value)}
        value={searchValue}
        onKeyDown={(e) => e.key === "Enter" && onSearchChange()}
      />
    </div>
  );
};
