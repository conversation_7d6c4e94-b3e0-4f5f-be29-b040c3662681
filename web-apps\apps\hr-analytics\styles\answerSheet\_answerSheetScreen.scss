@use "../config" as *;
@use "../mixins" as *;
.domain-assessment{
    min-height: max-content;
    max-height: 100%;
    &__timer {
        width: 120px;
        height: 50px;
        margin: auto;
        border: 1px solid #029CA5;
        border-radius: 5px;
        margin-top: 30px;
        background: #029CA51A;
        color: #015462;
        display: flex;
        align-items: center; 
        justify-content: center; 
      }
      
    &__question{
    width : 60%;
    height: fit-content;
    margin: auto;
    margin-top: 30px;
        @media (max-width: 768px) {
            width : 80%;
        }
    }

    &__pagination{
        width : 60%;
        max-height: 20px;
        margin: auto;
    }
    &__footer {
      color: #fff;
      height: 60px;
      text-align: center;
      display: flex;
      justify-content: space-between;
      margin: 10px 60px;

      &__cancel_btn,
      &__prev_btn,
      &__next_btn {
        color: #099c73;
        padding: 10px 28px;
        font-family: "Inter", "Avenir LT Std", sans-serif;
        font-style: normal;
        font-weight: 900;
        font-size: 14px;
        border-radius: 4px;
      }
      &__next_btn {
        color: white;
      }
      &__prev_btn:disabled{
        color: rgb(202, 200, 200);
        border-color: rgb(202, 200, 200);
        cursor: not-allowed;
      }
    
      &__cancel_btn {
        &:hover {
          color: #099c73;
        }
      }
    
      &__prev_btn {
        border: 1px solid #099c73;
        margin-right: 20px;
      }
    
      &__next_btn {
        &:hover {
          background: linear-gradient(180deg, #018687 22.33%, #014456 112.08%);
         
        }
        background: linear-gradient(125.2deg, #099c73 8.04%, #015462 127.26%);
      }
    }
    
    @media (max-width: 768px) {
      &__footer {
        flex-direction: column; 
        align-items: center;
        margin: 10px; 
      }

      .domain-assessment__footer__sub{
        display: flex;
        flex-direction: column;
      }
    
      .domain-assessment__footer__prev_btn {
        margin: 10px 0;
      }

      .domain-assessment__footer__next_btn {
        margin-bottom: 4rem;
      }
    }
}

.sub-content {
  margin-top: 10px;
  display: flex;
  flex-direction: row;
  font-size: 14px;
  margin-bottom: 10px;
  color: black;
}

.dot {
  content: "";
  width: 7px;
  height: 7px;
  background-color: #099C73;
  border-radius: 50%;
  margin: auto 10px;
}

@media only screen and (max-width: 600px) {
  .sub-content, .routes, .assessment-title {
    margin: auto;
  }

  .sub-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .routes {
    font-size: 14px;
    text-align: center;
  }

  .assessment-title {
    text-align: center;
  }
}

@media (max-width: 350px) {
  .sub-content {
    flex-direction: column; 
  }
}

.routes {
  font-size: 16px;
}

.link {
  color: black;
  cursor: pointer;
}

.disabled-link {
  cursor: not-allowed;
  color: gray;
}


@media only screen and (min-width: 1000px) {
  .table-screen-dropdown{
    display: none;
  }
}

@media only screen and (max-width: 1000px) {
  .table-screen-dropdown{
      margin-left: 30px;
  }
}