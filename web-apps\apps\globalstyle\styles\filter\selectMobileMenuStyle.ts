export const selectMobileMenuStyle = {
  control: (provided: any, state: any) => ({
    ...provided,
    borderRadius: '4px',
    borderColor: '#E7F4F2',
    background: '#E7F4F2',
    fontSize: '14px',
    minHeight: '44px',
    width: '100%',
    boxShadow: state.isFocused ? '#ACD8D1' : '',
    ':hover': {
      borderColor: '#E7F4F2',
    },
    '> div:first-of-type': {
      display: 'flex',
      width: 'calc(100% - 42px)',
    },
    '> div:last-of-type': {
      width: '42px',
    },
  }),
  multiValue: (provided: any, state: any) => ({
    ...provided,
    borderRadius: '4px',
    background: 'rgba(2,156,165,0.1)',
  }),
  multiValueLabel: (provided: any, state: any) => ({
    ...provided,
    fontSize: '14px',
    color: '#2A2C33',
  }),
  multiValueRemove: (provided: any, state: any) => ({
    ...provided,
    color: '#015462',
    borderRadius: 0,
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: 'unset',
      color: '#2A2C33',
    },
  }),
  clearIndicator: (provided: any, state: any) => ({
    ...provided,
    color: '#737980',
    cursor: 'pointer',
    '&:hover': {
      color: '#2A2C33',
    },
  }),
  dropdownIndicator: (provided: any, state: any) => ({
    ...provided,
    color: '#737980',
    cursor: 'pointer',
    '&:hover': {
      color: '#099C73',
    },
  }),
  option: (provided: any, state: any) => ({
    ...provided,
    margin: '5px 0',
    color: '#343B43',
    fontSize: '14px',
    display: 'flex',
    alignItems: 'center',
    cursor: 'pointer',
    backgroundColor: state.isFocused ? '#fff' : '#fff',
    ':focus': {
      backgroundColor: '#fff',
    },
    ':hover': {
      backgroundColor: '#fff',
    },
  }),
  placeholder: (provided: any) => ({
    ...provided,
    color: '#999EA5',
    fontSize: '14px',
    paddingRight: 16,
    position: 'absolute',
  }),
  valueContainer: (provided: any) => ({
    ...provided,
    padding: '7px 16px',
    fontSize: '14px',
  }),
  menu: (provided: any) => ({
    ...provided,
    zIndex: 20,
  }),
  singleValue: (provided: any) => ({
    ...provided,
    color: '#099C73',
    fontWeight: 600,
  }),
}