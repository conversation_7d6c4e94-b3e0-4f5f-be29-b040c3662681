import { Column, DataType, Model, Table } from "sequelize-typescript";
import { ApiProperty } from "@nestjs/swagger";

@Table({ tableName: "mailbox" })
export class Mailbox extends Model<Mailbox> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: "<EMAIL>",
    description: "sender mail id",
  })
  @Column({ type: DataType.INTEGER, allowNull: false })
  userId: number;

  @ApiProperty({
    example: "<EMAIL>",
    description: "sender mail id",
  })
  @Column({ type: DataType.STRING, allowNull: false })
  sender: string;

  @ApiProperty({
    example: "<EMAIL>",
    description: "recipients mail id",
  })
  @Column({ type: DataType.STRING, allowNull: false })
  recipient: string;

  @ApiProperty({
    example: "Job application",
    description: "Subject of the mail",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  subject: string;

  @ApiProperty({
    example: "Job application",
    description: "Body of the mail",
  })
  @Column({ type: DataType.TEXT, allowNull: true })
  body: string;

  @ApiProperty({
    example: "Job application",
    description: "Body of the mail",
  })
  @Column({ type: DataType.BOOLEAN, allowNull: false })
  isRead: boolean;

  @ApiProperty({ example: "s3FileFiles", description: "s3File" })
  @Column({ type: DataType.STRING, allowNull: true })
  s3File: any;

  @ApiProperty({ example: "inbox", description: "source of mailbox" })
  @Column({ type: DataType.STRING, allowNull: true })
  source: any;
}

