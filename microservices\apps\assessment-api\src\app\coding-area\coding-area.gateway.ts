import { CodingAreaService, CodingArea } from '@microservices/db';
import { OnModuleInit } from '@nestjs/common';
import {
  OnGatewayConnection,
  OnGatewayDisconnect,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Socket, Server } from 'socket.io';
import { EditorManagerService, RoomUserService, ISettings } from '@microservices/editor-manager';


@WebSocketGateway({
  cors: {
    origin: [
      /\.urecruits\.com$/,
      'http://localhost:9000',
      'http://localhost:9001',
      'http://127.0.0.1:5500',
    ],
  },
})
export class CodingAreaGateway
implements OnGatewayConnection, OnGatewayDisconnect, OnModuleInit {
  @WebSocketServer() server: Server;

  constructor(
    private codingAreaService: CodingAreaService,
    private editorManagerService: EditorManagerService,
    private roomUserService: RoomUserService,
  ) {}

  async onModuleInit() {
    console.log('Web socket module is inites!');
  }

  async handleConnection(socket: Socket) {
    socket.on('join', ({ name, room }, callback) => {
      console.log('User has joined', name);
      const color = `#${Math.floor(Math.random() * 16777215).toString(16)}`;
      const { error, user } = this.roomUserService.addUser({
        name,
        id: socket.id,
        color,
        room,
      });
      const editorState = this.editorManagerService.getEditorState(room);

      if (error) {
        return callback(error);
      } else {
        socket.join(user.room);
        socket.broadcast.to(user.room).emit('notification', {
          text: `${user.name} has joined!`,
          type: 'connect',
          user,
        });

        this.server.to(user.room).emit('roomData', {
          room: user.room,
          users: this.roomUserService.usersInRoom(user.room),
        });

        return callback(error, user, editorState);
      }
    });
  }

  async handleDisconnect(socket: Socket) {
    const user = this.roomUserService.removeUser(socket.id);
    if (user) {
      this.server.to(user.room).emit('notification', {
        text: `${user.name} has left`,
        type: 'disconnect',
        user,
      });
      this.server.to(user.room).emit('roomData', {
        room: user.room,
        users: this.roomUserService.usersInRoom(user.room),
      });
    }
    console.log(`Disconnected: ${  socket.client}`);
  }

  @SubscribeMessage('CODING_AREA:ON_CHANGE')
  async onInsert(socket: Socket, data: CodingArea) {
    const { assignmentId, editorState } = data;
    this.editorManagerService.setEditorState({ assignmentId, editorState });
    await this.codingAreaService.create(data);
    const user = this.roomUserService.findUser(socket.id);
    socket.broadcast.to(user.room).emit('CODING_AREA:CHANGED', data);
  }

  @SubscribeMessage('SETTINGS:ON_CHANGE')
  async onShare(socket: Socket, data: ISettings) {
    const user = this.roomUserService.findUser(socket.id);
    socket.broadcast.to(user.room).emit('SETTINGS:CHANGED', data);
  }
}
