// Simple test script to verify take-home submission creation endpoint
const axios = require('axios');

const API_BASE_URL = 'http://localhost:5481/api'; // Adjust port if needed
const TEST_TOKEN = 'your-jwt-token-here'; // Replace with actual JWT token

async function testSubmissionCreation() {
  try {
    console.log('Testing take-home submission creation...');
    
    const submissionData = {
      jobId: 1,
      candidateId: 1,
      assessmentId: 1,
      assignmentId: 1,
      duration: "45:30",
      language: "javascript",
      submittedAt: new Date().toISOString()
    };

    const response = await axios.post(
      `${API_BASE_URL}/take-home-submissions`,
      submissionData,
      {
        headers: {
          'Authorization': `Bearer ${TEST_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('✅ Submission created successfully!');
    console.log('Response:', response.data);
    
    // Test getting the submission
    const getResponse = await axios.get(
      `${API_BASE_URL}/take-home-submissions/${response.data.id}`,
      {
        headers: {
          'Authorization': `Bearer ${TEST_TOKEN}`
        }
      }
    );
    
    console.log('✅ Submission retrieved successfully!');
    console.log('Retrieved submission:', getResponse.data);
    
  } catch (error) {
    console.error('❌ Test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Run the test
testSubmissionCreation();
