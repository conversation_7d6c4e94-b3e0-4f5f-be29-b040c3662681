@use "config" as *;
@use "mixins" as *;
.mobile-menu {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 20;
  background: #fff;
  width: 100%;
  height: calc(100vh - 52px);
  overflow: auto;
  transform: translateY(-100%);
  transition: transform .4s linear;

  &__list {
    display: flex;
    flex-direction: column;
    @include media(xs){
      @include safari_fixes(){
        margin-top: 80px;
      }
      @include ios{
        margin-top: 80px;
      }
    }
  }

  &__item {
    border-bottom: 1px solid $grayTone2;

    &__head {
      width: 100%;
      padding: 20px;
      display: flex;
      justify-content: space-between;
    }

    &__name {
      color: $grayTone4;
      font-size: 16px;
      font-weight: 800;
      line-height: 1;
      text-transform: uppercase;
      word-break: break-word;
      padding-right: 12px;
    }

    &__icon {
      min-width: 10px;
      width: 10px;
      height: 7px;
      margin-top: 6px;
    }

    &.active {
      .mobile-menu__item__name {
        color: #273443;
      }
      .mobile-menu__item__icon{
        transform: rotate(180deg);
        path{
          fill: $mainGreen;
        }
      }

    }
  }

  &__sublist {

    &__item {
    }

    &__link {
      width: 100%;
      padding: 8px 40px;
      display: flex;
      margin-bottom: 16px;
    }

    &__icon {
      min-width: 20px;
      width: 20px;
      height: 20px;
      margin-right: 10px;
      transition: .3s ease-in, .3s ease-in-out;

      path {
        transition: .3s ease-in, .3s ease-in-out;
      }
    }

    &__name {
      font-size: 14px;
      color: $grayTone6;
    }
  }
}

.mobile-menu.active {
  transform: translateY(0);
  bottom: 0;
  top: unset;
  z-index: 17;
}