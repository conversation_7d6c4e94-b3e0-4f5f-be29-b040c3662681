export interface State {
  description: string;
  name: string;
  language: any | null;
  packageLiveTask: any | null;
  isOnline: boolean;
  instruction: any;
  starterCode: string;
  nextStep: number;
  validStatusBar: ValidStatusBar;
  databases: IDatabases[] | null;
  database: IDatabases | null;
}

export interface ValidStatusBar {
  stepOne?: boolean | null;
  stepTwo?: boolean | null;
}

export interface IDatabases {
  id: number;
  title: string | null;
  packages: null;
  description: string | null;
  script: string | null;
  questionId: any;
}
