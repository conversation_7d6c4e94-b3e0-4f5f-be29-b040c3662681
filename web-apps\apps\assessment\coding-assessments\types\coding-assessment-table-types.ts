
export interface ICodingAssessmentTable {
    isLoading:boolean,
    filters: {
        searchValue: string,
        sortBy: string,
        sortType: string,
        languages:any[],
        questionType:string,
        createdOn:{
            from: Date| null,
            to: Date|null,
        },
        status:string[],
    },
    tableItems:any[],
    pagination: {
        currentPage: number,
        limit: number,
        totalCount: number,
    },
    filterInfo: {
        languageList:any[],
        tabs: Array<string>
    },
    deletePopup:boolean
}