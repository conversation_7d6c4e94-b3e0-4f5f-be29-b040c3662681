import axios from "axios";

const getConfig= ()=>{
  const token: string = localStorage.getItem("token")as string;
return {
  headers: {
    "Content-Type": "application/json",
    Authorization: `Bearer ${token}`,
  },
};
}
const fetchData = async (api) => {
  try {
    const res = await axios.get(api, getConfig());
    if (res && res.data) {
      return res.data;
    }
  } catch (error) {
    throw Error("Unexpected Error");
  }
};

export const postData = async (api, body) => {
  try {
    const res = await axios.post(api, body, getConfig());
    if (res && res.data) {
      return res.data;
    }
    console.log(res);
  } catch (error) {
    console.log(error);
    throw Error("Unexpected Error");
  }
};

export const postFormData = async (api, body) => {
  try {
    const res = await axios({
      method: "post",
      url: api,
      data: body,
      ...getConfig()
    });
    if (res && res.data) {
      return res.data;
    }
  } catch (error) {
    throw Error("Unexpected Error");
  }
};

export default fetchData;
