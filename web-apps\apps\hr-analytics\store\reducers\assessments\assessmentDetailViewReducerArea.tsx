import type { PayloadAction } from "@reduxjs/toolkit";
// eslint-disable-next-line no-duplicate-imports
import { createSlice } from "@reduxjs/toolkit";
import {
    IAssessmentAreaArray,
    IAssessmentDetailAreaView,
} from "../../../types/redux/assessments";

const initialStateATM: IAssessmentDetailAreaView = {
  assessmentStatus: "all",
  assessmentsData: [],
  duration: "week",
};

export const assessmentsDetailAreaView = createSlice({
  name: "assessments_area",
  initialState: initialStateATM,
  reducers: {
    setAreaAssessments: (state, action: PayloadAction<IAssessmentAreaArray>) => {
      state.assessmentsData = action.payload;
    },
    setAreaDuration: (state, action: PayloadAction<string>) => {
      state.duration = action.payload;
    },
    setAreaStatus: (state, action: PayloadAction<string>) => {
      state.assessmentStatus = action.payload;
    },
  },
});

export const { setAreaDuration, setAreaAssessments, setAreaStatus } =
  assessmentsDetailAreaView.actions;

export default assessmentsDetailAreaView.reducer;