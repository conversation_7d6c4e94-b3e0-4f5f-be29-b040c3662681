@use "../config" as *;
@use "../mixins" as *;
$white: white;
$red: red;

.add-btn {
  padding: 16px 28px;
  font-size: 14px;
  text-align: -webkit-center;
  white-space: nowrap;
  line-height: 1;
  font-weight: 800;
  border-radius: 4px;
  border: 1px solid transparent;
  box-sizing: border-box;
  transition: 0.2s ease-out;

  &--red {
    color: $red;
    border-color: $red;
    transition: .3s;

    &:hover {
      background-color: $red;
      color: $white;
    }
  }

  &--white {
    color: $grayTone7;
    background-color: $white;
    transition: .3s;
    width: 100px;

    &:hover {
      background-color: $grayTone2;
      color: $grayTone7;
      transition: .3s;
    }
  }

  &--white-green {
    color: $mainGreen;
    background-color: $white;
    transition: .3s;
    width: 100px;

    &:hover {
      background-color: $grayTone2;
      color: $mainGreen;
      transition: .3s;
    }
  }

  &--green {
    background: linear-gradient(125.2deg, #099c73 8.04%, #015462 127.26%);
    color: $white;
    border: none;
    transition: .3s;
    width: auto;
    @include media(xs) {
      width: calc(100vw - 35px);
    }

    &:disabled {
      background: linear-gradient(125.2deg, rgba(9, 156, 115, 0.6) 8.04%, rgba(1, 84, 98, 0.6) 127.26%);

      &:hover {
        background: linear-gradient(125.2deg, rgba(9, 156, 115, 0.6) 8.04%, rgba(1, 84, 98, 0.6) 127.26%);
      }
    }

    &:hover {
      background: linear-gradient(180deg, #018687 22.33%, #014456 112.08%);
      color: $white;
    }
  }
}

// .btn-small {
//   padding: 10px 11px;
//   font-size: 12px;
// }

// .btn-big {
//   padding: 16px 28px;
//   font-size: 14px;
// }
