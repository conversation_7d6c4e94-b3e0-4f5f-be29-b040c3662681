// import { Controller, Get, Post, Body } from '@nestjs/common';
// import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
// import { AssessmentChatbotService, ConversationDto } from 'libs/db/src/lib/assessment-chatbot/assessmentChatbot.service';

// @Controller('assessment-chatbot')
// @ApiBearerAuth("access-token")
// export class AssessmentChatbotController {
//     constructor(private readonly chatbotService: AssessmentChatbotService) { }

//     @ApiOperation({ summary: 'Generate a question based on user input' })
//     @ApiResponse({ status: 200 })
//     @Post("generate-question")
//     async generateQuestion(@Body() dto: ConversationDto) {
//         return this.chatbotService.generateQuestion(dto);
//     }
// }


import { Controller, Post, Body, Headers, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { AssessmentChatbotService } from 'libs/db/src/lib/assessment-chatbot/assessmentChatbot.service';
import { ConversationDto } from 'libs/db/src/lib/assessment-chatbot/assessmentChatbot.types';
import { AuthGuard } from '@nestjs/passport';
import { AuthUser, Permissions, PermissionsGuard } from '@microservices/auth';

@Controller('assessment-chatbot')
@ApiBearerAuth('access-token')
export class AssessmentChatbotController {
  constructor(private readonly chatbotService: AssessmentChatbotService) {}

  @ApiOperation({ summary: 'Handle chatbot conversation for assessment creation' })
  @ApiResponse({ status: 200, description: 'Conversation response with session ID and message' })
  @ApiResponse({ status: 401, description: 'Unauthorized if JWT is invalid' })
  @ApiResponse({ status: 403, description: 'Forbidden if user lacks recruiter permission' })
  @ApiBody({ type: ConversationDto })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Permissions('recruiter')
  @Post('converse')
  async converse(
    @AuthUser() user: any,
    @Headers('authorization') authHeader: string,
    @Body() dto: ConversationDto,
  ) {
    const companyId = user['https://urecruits.com/companyId'];
    const userId = user['https://urecruits.com/userId'];

    return this.chatbotService.handleConversation({
      ...dto,
      companyId,
      userId,
      workflowContext: dto.workflowContext, // Pass through workflow context
    });
  }
}