@use "../../styles/mixins" as *;
@use "../../styles/config" as *;

.checkbox {
  display: inline-block;

  &.active {
    label {
      border-color: $mainGreen;
    }

    img {
      visibility: visible;
    }
  }

  &__label {
    min-width: 16px;
    width: 16px;
    height: 16px;
    border: 1px solid $grayTone3;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  &__icon {
    min-width: 10px;
    width: 10px;
    height: 9px;
    object-fit: contain;
    visibility: hidden;
  }
}