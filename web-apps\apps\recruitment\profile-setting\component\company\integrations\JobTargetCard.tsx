import { getEnv } from "@urecruits/api";
import React, { useEffect, useState } from "react";
import axios from "axios";
import { Button, getConfig } from "@ucrecruits/globalstyle/src/ucrecruits-globalstyle";
import <PERSON>Loader from "../../SmallLoader";
import Snackbar from "@mui/material/Snackbar";
import { IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

const { API_RECRUITMENT } = getEnv();

const JobTargetCard = () => {
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [toastNotification, setToastNotification] = useState({
    state: false,
    message: "",
  });

  useEffect(() => {
    const getJobTargetCredentials = async () => {
      try {
        const response = await axios(
          `${API_RECRUITMENT}/api/jobtarget/account/get`,
          getConfig()
        );

        if (response.status === 200 && response.data) {
          setIsConnected(true);
        }
      } catch (error) {
        console.error("Error fetching JobTarget credentials:", error);
        setIsConnected(false);
      }
    };

    getJobTargetCredentials();
  }, []);

  const handleToastNotificationClose = () => {
    setToastNotification({
      state: false,
      message: "",
    });
  };

  const handleConnect = async () => {
    setIsPublishing(true);
    try {
      const response = await axios.post(
        `${API_RECRUITMENT}/api/jobtarget/integrate`,
        {},
        getConfig()
      );

      if (response.status === 200 || response.status === 201) {
        setIsConnected(true);
      }
      setToastNotification({
        state: true,
        message: "JobTarget Connected",
      });
    } catch (error) {
      console.error("Integration failed:", error);
      setToastNotification({
        message: error.message,
        state: true,
      });
    } finally {
      setIsPublishing(false);
    }
  };

  const handleDisconnect = async () => {
    setIsPublishing(true);
    try {
      const response = await axios.delete(
        `${API_RECRUITMENT}/api/jobtarget/account/remove`,
        getConfig()
      );

      if (response.status === 200) {
        setIsConnected(false);
      }
      setToastNotification({
        state: true,
        message: "JobTarget Disconnected",
      });
    } catch (error) {
      console.error("Disconnection failed:", error);
      setToastNotification({
        message: error.message,
        state: true,
      });
    } finally {
      setIsPublishing(false);
    }
  };

  const action = (
    <React.Fragment>
      <IconButton
        size="small"
        aria-label="close"
        color="inherit"
        onClick={handleToastNotificationClose}>
        <CloseIcon fontSize="small" />
      </IconButton>
    </React.Fragment>
  );
  return (
    <>
      <Snackbar
        open={toastNotification.state}
        onClose={handleToastNotificationClose}
        message={toastNotification.message}
        action={action}
        sx={{
          "& .css-1eqdgzv-MuiPaper-root-MuiSnackbarContent-root": {
            background: "linear-gradient(125.2deg, #099c73 8.04%, #015462 127.26%)",
            color: "white",
          },
        }} />
      <div>
        <div className="integrations__card__integration-button">
          {!isConnected ? (
            <Button
              label="Connect"
              isLoading={isPublishing}
              className="integrations__card__integration-button__button button--filled"
              onClick={handleConnect}
            />
          ) : (
            <Button
              label="Disconnect"
              isLoading={isPublishing}
              className="integrations__card__integration-button__button button--empty"
              onClick={handleDisconnect}
            />
          )}
        </div>
      </div>
    </>
  );
};

export default JobTargetCard;
