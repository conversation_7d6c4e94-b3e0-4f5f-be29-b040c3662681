import {IAsync<PERSON>heckBoxList, ITabsFilter} from "../global/global";

export interface IJobsOffersTable {
	noResultSearch: boolean,
	tableEmpty: boolean,
	offers: Array<IJobOfferItem>
	filters: {
		location: Array<IAsyncCheckBoxList>
		searchValue: string,
		sortBy: string,
		sortType: string
	},
	pagination: {
		currentPage: number,
		limit: number,
		totalCount: number
	},
	fixedTab: {
		id: number,
		displayName: string
	},
	tabFilter: Array<ITabsFilter>,
	filterInfo: {
		tabs: Array<any>
	}
}

//TODO: need to add interface for job offer item
export interface IJobOfferItem {
	id: number,
	jobTitle: string;
}