import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { Candidate } from '../candidates/candidates.model'

interface SkillAttrs {
  name?: string;
  years?: string;
  candidateId: number;
}

@Table({ tableName: 'skills' })
export class Skill extends Model<Skill, SkillAttrs> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: 'PHP',
    description: 'Skill Name',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  name: string;

  @ApiProperty({
    example: '5',
    description: 'Years of Experience in the skills',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  years: string;

  @ApiProperty({ example: '1', description: 'Candidate ID' })
  @ForeignKey(() => Candidate)
  @Column({ type: DataType.INTEGER, allowNull: false })
  candidateId: number;

  @BelongsTo(() => Candidate)
  candidate: Candidate;
}
