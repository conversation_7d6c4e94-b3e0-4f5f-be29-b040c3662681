import { useEffect, useState } from "react";
import {
  setAssessmentQuestion,
  setValidStatusBar,
} from "../../store/assessmentTask/assessmentTask.actions";
import NavigateToSteps from "./NavigateToSteps";
import { useDispatch } from "react-redux";
import { getEnv } from "@urecruits/api";
import fetchData from "../../hook/fetchData";
import postData from "../../hook/postData";
import deleteData from "../../hook/deleteData";
import editIcon from "../../images/icon/edit_ic.svg";
import deleteIcon from "../../images/icon/delete_ic.svg";
import approve from "../../images/icon/approve_job_ic.svg";

const { API_ASSESSMENT } = getEnv();

const typeList = [
  { value: "Integer", label: "Integer" },
  { value: "String", label: "String" },
  { value: "Inter Array", label: "Inter Array" },
  { value: "String Array", label: "String Array" },
];

const HomeTaskStepTwo = (props) => {
  const { homeQuestion, validFields, setValidFields } = props;
  const dispatch = useDispatch();
  const [showAITestCases, setShowAITestCases] = useState(false);
  const [isRegeneratingTestCases, setIsRegeneratingTestCases] = useState(false);
  const [editingTestCase, setEditingTestCase] = useState<number | null>(null);
  const [editedTestCases, setEditedTestCases] = useState<any[]>([]);

  // Apply AI-generated test cases if available
  useEffect(() => {
    // Check both possible locations for test cases
    const hasTestCases =
      homeQuestion.aiGeneratedTestCases || homeQuestion.testCases;
    if (hasTestCases) {
      setShowAITestCases(true);

      // Ensure test cases are properly stored in Redux for the save function
      const testCaseInputs =
        homeQuestion.testCaseInputs ||
        homeQuestion.aiGeneratedTestCases?.testCaseInputs ||
        homeQuestion.testCases?.testCaseInputs;

      const testCases =
        homeQuestion.testCases?.testCases ||
        homeQuestion.testCases ||
        homeQuestion.aiGeneratedTestCases?.testCases;

      // Update Redux with the test case data to ensure it's available for saving
      if (testCaseInputs || testCases) {
        dispatch(
          setAssessmentQuestion({
            ...homeQuestion,
            testCaseInputs: testCaseInputs,
            testCases: testCases,
          })
        );
      }
    }
  }, [
    homeQuestion.aiGeneratedTestCases,
    homeQuestion.testCases,
    dispatch,
    homeQuestion,
  ]);

  useEffect(() => {
    dispatch(
      setValidStatusBar({
        questionId: homeQuestion.id,
        stepOne: true,
        stepTwo: validFields,
      })
    );
  }, [dispatch, homeQuestion.id, validFields]);

  useEffect(() => {
    setValidFields(true);
  }, [homeQuestion.formStep, setValidFields]);

  const regenerateTestCases = async () => {
    if (!homeQuestion.languages || !homeQuestion.questionDescription) {
      return null;
    }

    setIsRegeneratingTestCases(true);
    try {
      // Delete existing test cases if in edit mode
      if (homeQuestion.id && homeQuestion.id !== "new") {
        try {
          await deleteData(
            `${API_ASSESSMENT}/api/take-home-task/question/${homeQuestion.id}/testcases`,
            {}
          );
        } catch (deleteError) {
          console.error("Error deleting existing test cases:", deleteError);
        }
      }

      try {
        // Step 1: POST request to create test cases
        const createResponse = await postData(
          `${API_ASSESSMENT}/api/generate-test-cases`,
          {
            languageId: homeQuestion.languages.id,
            questionDescription: homeQuestion.questionDescription,
            nameQuestion: homeQuestion.nameQuestion,
            // Add more context to help the AI generate better test cases
            questionType: "sorting", // Hint about the type of question
            expectedOutputType: "array", // Hint about the expected output
          }
        );


        if (createResponse && createResponse.id) {
          // Step 2: GET request to retrieve the generated test cases
          const getResponse = await fetchData(
            `${API_ASSESSMENT}/api/generate-test-cases/${createResponse.id}`,
            (data) => console.log("Test cases data received:", data)
          );

          if (getResponse && getResponse.data) {
            // Check if the response has the expected structure
            const testCasesData = getResponse.data.testCasesData || getResponse.data;
            const testCases = testCasesData.testCases || [];
            const testCaseInputs = testCasesData.testCaseInputs || [];

            dispatch(
              setAssessmentQuestion({
                ...homeQuestion,
                aiGeneratedTestCases: getResponse.data,
                testCases: testCases,
                testCaseInputs: testCaseInputs,
              })
            );
            return true;
          }
        }
      } catch (apiError) {
        console.error("Error generating test cases:", apiError);

        // Show more specific error message
        let errorMessage = "Failed to regenerate test cases. ";
        if (apiError.message) {
          errorMessage += `Error: ${apiError.message}`;
        } else {
          errorMessage += "Please check your internet connection and try again.";
        }

        alert(errorMessage);
        return false;
      }

      return true;
    } catch (error) {
      console.error("Error in regenerateTestCases:", error);
      return false;
    } finally {
      setIsRegeneratingTestCases(false);
    }
  };

  // Initialize edited test cases when test cases change
  useEffect(() => {
    // Get the test cases from the correct location in the structure
    const rawTestCases =
      homeQuestion.testCases?.testCases ||
      homeQuestion.testCases ||
      homeQuestion.aiGeneratedTestCases?.testCases ||
      [];

    // Create deep copies to avoid read-only issues
    const deepCopiedTestCases = rawTestCases.map(testCase => ({
      ...testCase,
      inputs: testCase.inputs ? testCase.inputs.map(input => ({ ...input })) : [],
      output: testCase.output
    }));

    // Ensure each test case has the correct structure
    const formattedTestCases = deepCopiedTestCases.map(testCase => {
      // Make sure each test case has inputs array
      if (!testCase.inputs || !Array.isArray(testCase.inputs)) {
        testCase.inputs = [];
      }

      // Make sure output is a string
      if (testCase.output === undefined || testCase.output === null) {
        testCase.output = "";
      }

      return testCase;
    });

    setEditedTestCases(formattedTestCases);
  }, [homeQuestion.testCases, homeQuestion.aiGeneratedTestCases]);

  const handleEditTestCase = (index: number) => {
    setEditingTestCase(index);
  };

  const handleCancelEdit = () => {
    setEditingTestCase(null);
    // Reset edited test cases to original values with deep copies
    const rawTestCases =
      homeQuestion.testCases?.testCases ||
      homeQuestion.testCases ||
      homeQuestion.aiGeneratedTestCases?.testCases ||
      [];
    const deepCopiedTestCases = rawTestCases.map(testCase => ({
      ...testCase,
      inputs: testCase.inputs ? testCase.inputs.map(input => ({ ...input })) : [],
      output: testCase.output
    }));
    setEditedTestCases(deepCopiedTestCases);
  };

  const handleSaveEdit = () => {
    // Update the test cases in Redux with proper structure
    const updatedTestCases = {
      testCases: editedTestCases,
      testCaseInputs: homeQuestion.testCaseInputs || homeQuestion.aiGeneratedTestCases?.testCaseInputs || []
    };

    dispatch(
      setAssessmentQuestion({
        ...homeQuestion,
        testCases: updatedTestCases,
        aiGeneratedTestCases: {
          ...homeQuestion.aiGeneratedTestCases,
          testCases: editedTestCases,
        },
      })
    );
    setEditingTestCase(null);
  };

  const handleInputChange = (testCaseIndex: number, inputIndex: number, field: string, value: string) => {
    const updatedTestCases = editedTestCases.map((testCase, index) => {
      if (index === testCaseIndex) {
        const updatedTestCase = { ...testCase };
        if (!updatedTestCase.inputs) {
          updatedTestCase.inputs = [];
        }
        const updatedInputs = [...updatedTestCase.inputs];
        if (!updatedInputs[inputIndex]) {
          updatedInputs[inputIndex] = {};
        }

        // Process the value based on the input type
        let processedValue: any = value;

        // Get the test case input type to determine how to process the value
        const testCaseInputs = homeQuestion.testCaseInputs || homeQuestion.aiGeneratedTestCases?.testCaseInputs || [];
        const inputType = testCaseInputs[inputIndex]?.type;

        // If it's an array type and the value looks like an array string, try to parse it
        if ((inputType === "Inter Array" || inputType === "String Array") &&
          typeof value === 'string' && value.trim().startsWith('[') && value.trim().endsWith(']')) {
          try {
            // Parse the array string and store it as an array
            const parsedArray = JSON.parse(value);
            if (Array.isArray(parsedArray)) {
              processedValue = parsedArray;
            }
          } catch (e) {
            // If parsing fails, keep the original string value
            processedValue = value;
          }
        }

        updatedInputs[inputIndex] = { ...updatedInputs[inputIndex], [field]: processedValue };
        updatedTestCase.inputs = updatedInputs;
        return updatedTestCase;
      }
      return { ...testCase, inputs: testCase.inputs ? [...testCase.inputs] : [] };
    });
    setEditedTestCases(updatedTestCases);
  };

  const handleOutputChange = (testCaseIndex: number, value: string) => {
    const updatedTestCases = editedTestCases.map((testCase, index) => {
      if (index === testCaseIndex) {
        return { ...testCase, output: value };
      }
      return { ...testCase, inputs: testCase.inputs ? [...testCase.inputs] : [] };
    });
    setEditedTestCases(updatedTestCases);
  };

  const handleAddTestCase = () => {
    const newTestCase = {
      inputs: [
        {
          name: "input",
          value: "",
        },
      ],
      output: "",
    };
    const updatedTestCases = [...editedTestCases, newTestCase];
    setEditedTestCases(updatedTestCases);

    // Update Redux immediately with proper structure
    const updatedTestCasesStructure = {
      testCases: updatedTestCases,
      testCaseInputs: homeQuestion.testCaseInputs || homeQuestion.aiGeneratedTestCases?.testCaseInputs || []
    };

    dispatch(
      setAssessmentQuestion({
        ...homeQuestion,
        testCases: updatedTestCasesStructure,
        aiGeneratedTestCases: {
          ...homeQuestion.aiGeneratedTestCases,
          testCases: updatedTestCases,
        },
      })
    );
  };

  const handleDeleteTestCase = (index: number) => {
    const updatedTestCases = editedTestCases
      .filter((_, i) => i !== index)
      .map(testCase => ({
        ...testCase,
        inputs: testCase.inputs ? testCase.inputs.map(input => ({ ...input })) : []
      }));
    setEditedTestCases(updatedTestCases);

    // Update Redux immediately with proper structure
    const updatedTestCasesStructure = {
      testCases: updatedTestCases,
      testCaseInputs: homeQuestion.testCaseInputs || homeQuestion.aiGeneratedTestCases?.testCaseInputs || []
    };

    dispatch(
      setAssessmentQuestion({
        ...homeQuestion,
        testCases: updatedTestCasesStructure,
        aiGeneratedTestCases: {
          ...homeQuestion.aiGeneratedTestCases,
          testCases: updatedTestCases,
        },
      })
    );

    // Reset editing state if we were editing the deleted test case
    if (editingTestCase === index) {
      setEditingTestCase(null);
    } else if (editingTestCase !== null && editingTestCase > index) {
      setEditingTestCase(editingTestCase - 1);
    }
  };



  const formatValue = (value: any): string => {
    if (value === undefined || value === null) {
      return "";
    }

    if (Array.isArray(value)) {
      // If array of objects with label/value, format nicely
      if (
        value.length > 0 &&
        typeof value[0] === "object" &&
        value[0] !== null &&
        !Array.isArray(value[0])
      ) {
        return value
          .map((item) =>
            item.label !== undefined && item.value !== undefined
              ? `${item.label}: ${item.value}`
              : JSON.stringify(item)
          )
          .join(", ");
      }
      // For simple arrays (numbers, strings), format as JSON array
      return JSON.stringify(value);
    }
    if (typeof value === "object") {
      if (value.label !== undefined && value.value !== undefined) {
        return `${value.label}: ${value.value}`;
      }
      return JSON.stringify(value);
    }
    return String(value);
  };

  const testCaseInputs =
    homeQuestion.testCaseInputs ||
    homeQuestion.aiGeneratedTestCases?.testCaseInputs ||
    homeQuestion.testCases?.testCaseInputs ||
    [];

  const rawTestCases =
    homeQuestion.testCases?.testCases ||
    homeQuestion.testCases ||
    homeQuestion.aiGeneratedTestCases?.testCases ||
    [];
  const testCases = rawTestCases.map((testCase, index) => {
    if (!testCase || !testCase.inputs || Object.keys(testCase).length === 0) {
      const validTestCase = rawTestCases.find(
        (tc) =>
          tc && tc.inputs && Array.isArray(tc.inputs) && tc.inputs.length > 0
      );

      if (validTestCase) {
        return {
          inputs: validTestCase.inputs.map((input) => ({
            name: input.name || "input",
            value: "(Example value)",
          })),
          output: "(Example output)",
        };
      } else {
        return {
          inputs: [{ name: "input", value: "(Example value)" }],
          output: "(Example output)",
        };
      }
    }

    const transformedInputs = testCase.inputs.map((input) => {
      if (Array.isArray(input.name)) {
        return {
          name: input.name[0]?.label || "input",
          value: input.name,
        };
      }
      return input;
    });

    return {
      ...testCase,
      inputs: transformedInputs,
    };
  });

  return (
    <NavigateToSteps>
      <div className="task-home">
        <div className="task-home__container">
          <h3 className="task-home__container__title">
            02. Test Cases & Inputs/Outputs
          </h3>

          {(homeQuestion.aiGeneratedTestCases || homeQuestion.testCases) && (
            <div className="ai-generated-banner">
              <div className="ai-banner-content">
                <div>
                  <div className="ai-banner-text">
                    AI-Generated Test Cases
                  </div>
                  <div className="ai-banner-description">
                    Test cases have been automatically generated based on your
                    question.
                  </div>
                </div>
                <button
                  onClick={regenerateTestCases}
                  disabled={isRegeneratingTestCases}
                  className="regenerate-button"
                  style={{
                    opacity: isRegeneratingTestCases ? 0.6 : 1,
                    whiteSpace: "nowrap",
                  }}
                >
                  {isRegeneratingTestCases ? "Regenerating..." : "Regenerate"}
                </button>
              </div>
            </div>
          )}

          {/* Test Case Inputs Section */}
          <div className="task-home__container__inout">Inputs</div>
          <div className="task-home__container__inout-hint">
            The following inputs will be used in the test cases
          </div>

          {testCaseInputs && testCaseInputs.length > 0 ? (
            <div
              className="test-case-inputs-table"
              style={{ marginBottom: "20px" }}
            >
              <table className="test-cases-table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Description</th>
                  </tr>
                </thead>
                <tbody>
                  {testCaseInputs?.map((input, index) => (
                    <tr key={index}>
                      <td>{input.name}</td>
                      <td>{input.type}</td>
                      <td>{input.description}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div
              style={{
                padding: "15px",
                backgroundColor: "#f9f9f9",
                marginBottom: "20px",
                borderRadius: "4px",
              }}
            >
              No input definitions available
            </div>
          )}

          {/* Test Cases Section */}
          <div className="task-home__container__inout">Test Cases</div>
          <div className="task-home__container__inout-hint">
            The following test cases will be used to evaluate the candidate's
            solution
          </div>

          {testCases && testCases.length > 0 ? (
            <div>
              <table className="test-cases-table">
                <thead>
                  <tr>
                    <th>Test Case</th>
                    <th>Inputs</th>
                    <th>Expected Output</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {testCases.map((testCase, index) => {
                    const isEditing = editingTestCase === index;
                    const currentTestCase = isEditing ? editedTestCases[index] : testCase;

                    return (
                      <tr key={index}>
                        <td>#{index + 1}</td>
                        <td>
                          {currentTestCase?.inputs && currentTestCase.inputs.map ? (
                            currentTestCase.inputs.map((input, idx) => (
                              <div key={idx} className="test-case-input-container">
                                <span className="test-case-input-label">
                                  {input.name !== undefined
                                    ? input.name
                                    : `Input ${idx + 1}`}
                                  :
                                </span>{" "}
                                {isEditing ? (
                                  <input
                                    type="text"
                                    value={
                                      Array.isArray(input.value)
                                        ? JSON.stringify(input.value)
                                        : (input.value || "")
                                    }
                                    onChange={(e) =>
                                      handleInputChange(index, idx, "value", e.target.value)
                                    }
                                    className="input-field"
                                    style={{
                                      width: "150px", // Made wider to accommodate arrays
                                    }}
                                    placeholder="Enter value (e.g., [1,2,3] for arrays)"
                                  />
                                ) : (
                                  formatValue(
                                    input.value !== undefined
                                      ? input.value
                                      : input.name
                                  )
                                )}
                              </div>
                            ))
                          ) : (
                            <div>No inputs defined</div>
                          )}
                        </td>
                        <td>
                          {isEditing ? (
                            <input
                              type="text"
                              value={currentTestCase?.output || ""}
                              onChange={(e) =>
                                handleOutputChange(index, e.target.value)
                              }
                              style={{
                                border: "1px solid #ccc",
                                borderRadius: "4px",
                                padding: "4px 8px",
                                fontSize: "12px",
                                width: "120px",
                              }}
                            />
                          ) : (
                            currentTestCase?.output !== undefined
                              ? formatValue(currentTestCase.output)
                              : "No output defined"
                          )}
                        </td>
                        <td>
                          {isEditing ? (
                            <div className="action-buttons">
                              <button
                                onClick={handleSaveEdit}
                                className="icon-button"
                                title="Save"
                              >
                                <img src={approve} alt="save" width="16" height="16" />
                              </button>
                              <button
                                onClick={handleCancelEdit}
                                className="icon-button"
                                title="Cancel"
                              >
                                <img src={deleteIcon} alt="cancel" width="16" height="16" />
                              </button>
                            </div>
                          ) : (
                            <div className="action-buttons">
                              <button
                                onClick={() => handleEditTestCase(index)}
                                className="icon-button"
                                title="Edit"
                              >
                                <img src={editIcon} alt="edit" width="16" height="16" />
                              </button>
                              <button
                                onClick={() => handleDeleteTestCase(index)}
                                className="icon-button"
                                title="Delete"
                              >
                                <img src={deleteIcon} alt="delete" width="16" height="16" />
                              </button>
                            </div>
                          )}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>

              {/* Add Test Case Button */}
              <div className="add-test-case-container">
                <button
                  onClick={handleAddTestCase}
                  className="add-test-case-button"
                  title="Add Test Case"
                >
                  <span>Add Test Case</span>
                </button>
              </div>
            </div>
          ) : (
            <div
              style={{
                padding: "15px",
                backgroundColor: "#f9f9f9",
                marginBottom: "20px",
                borderRadius: "4px",
              }}
            >
              No test cases available

              {/* Add Test Case Button when no test cases exist */}
              <div className="add-test-case-container center">
                <button
                  onClick={handleAddTestCase}
                  className="add-test-case-button"
                  title="Add Test Case"
                >
                  <span>Add Test Case</span>
                </button>
              </div>
            </div>
          )}

          <div className="group-button-task" style={{ marginTop: "30px" }}>
            <div>
              <button
                onClick={() =>
                  dispatch(
                    setAssessmentQuestion({
                      ...homeQuestion,
                      formStep: homeQuestion.formStep - 1,
                    })
                  )
                }
                className="back-button-task"
              >
                Back
              </button>
            </div>
            <div>
              <button
                onClick={() =>
                  dispatch(
                    setAssessmentQuestion({
                      ...homeQuestion,
                      formStep: homeQuestion.formStep + 1,
                    })
                  )
                }
                className="button-save"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      </div>
    </NavigateToSteps>
  );
};

export default HomeTaskStepTwo;
