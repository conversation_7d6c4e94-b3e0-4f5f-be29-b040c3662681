import React from 'react';
import { useDispatch } from 'react-redux';
import { setSelectedQuestion, setConversationStep } from '../../../store/chatbot/chatAssessmentReducer';

interface Question {
  title: string;
  description: string;
}

interface QuestionCardsProps {
  questions: Question[];
  setState: any;
  createChatBotMessage: any;
}

const QuestionCards: React.FC<QuestionCardsProps> = ({ questions, setState, createChatBotMessage }) => {
  const dispatch = useDispatch();

  const handleSelect = (question: Question) => {
    dispatch(setSelectedQuestion(question));
    dispatch(setConversationStep('askingLanguage'));
    
    // Ask about programming language
    const message = createChatBotMessage("Which programming language should this question use?", { delay: 1000 });
    setState((prev: any) => ({ ...prev, messages: [...prev.messages, message] }));
  };

  const handleRegenerate = () => {
    dispatch(setConversationStep('askingNumQuestions'));
    
    // Ask for number of questions again
    const message = createChatBotMessage("How many coding questions would you like me to generate?", { delay: 1000 });
    setState((prev: any) => ({ ...prev, messages: [...prev.messages, message] }));
  };

  return (
    <div className="question-cards">
      <p>Here are the generated questions:</p>
      <div className="question-list">
        {questions.map((question, index) => (
          <div key={index} className="question-card">
            <h3>{question.title}</h3>
            <p>{question.description}</p>
            <button onClick={() => handleSelect(question)}>Select</button>
          </div>
        ))}
      </div>
      <button onClick={handleRegenerate}>Regenerate Questions</button>
    </div>
  );
};

export default QuestionCards; 