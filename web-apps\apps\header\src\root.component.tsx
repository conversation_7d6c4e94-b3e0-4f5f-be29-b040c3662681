import '../styles/main.scss';
import { useState } from 'react';
import MobileMenu from '../components/MobileMenu';
import { Auth0Provider } from "@auth0/auth0-react";
import LoginRedirect from "../components/Login";
import Header from "../components/Header";
import GlobalChatBot from "../components/Chatbot/Chatbot";
import { useHasPermission } from "@ucrecruits/globalstyle/src/ucrecruits-globalstyle";
import { getEnv } from '@urecruits/api';

function HeaderContent({ mobileMenu, setMobile }) {
  const { checkUserPermission, companyId } = useHasPermission();

  // Show chatbot only for recruiters (users with companyId or recruiter permissions)
  const isRecruiter = companyId || checkUserPermission('recruiter') || checkUserPermission('company-admin');

  return (
    <>
      <LoginRedirect/>
      <Header mobileMenu={mobileMenu} setMobile={setMobile}/>
      <MobileMenu mobileMenu={mobileMenu} setMobile={setMobile}/>
      {isRecruiter && <GlobalChatBot />}
    </>
  );
}

export default function Root () {
  const [mobileMenu, setMobile] = useState(false)
  const { AUTH0_DOMAIN, AUTH0_CLIENT_ID, AUTH0_AUDIENCE } = getEnv();

  console.log(AUTH0_DOMAIN, AUTH0_CLIENT_ID, AUTH0_AUDIENCE, "AUTH0_DOMAIN, AUTH0_CLIENT_ID, AUTH0_AUDIENCE");

  return (
    <Auth0Provider
      // domain="dev-9zt22me9.us.auth0.com"
      domain={AUTH0_DOMAIN}
      clientId={AUTH0_CLIENT_ID}
      redirectUri={window.location.origin}
      audience={AUTH0_AUDIENCE}
      cacheLocation="localstorage"
      // useRefreshToken={true}
      scope='openid profile email offline_access'
    >
      <HeaderContent mobileMenu={mobileMenu} setMobile={setMobile} />
    </Auth0Provider>
  )
}