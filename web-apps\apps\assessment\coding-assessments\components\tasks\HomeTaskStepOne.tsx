import { useEffect, useState } from "react";
import { navigate } from "@reach/router";
import NavigateToSteps from "./NavigateToSteps";
import { CheckboxButton } from "../CheckboxButton";
import { useDispatch } from "react-redux";
import {
  setAssessmentQuestion,
  setValidStatusBar,
} from "../../store/assessmentTask/assessmentTask.actions";
import CodingSelect from "../_elements/CodingSelect";
import { validateFormField } from "../../config/utils";
import fetchData from "../../hook/fetchData";
import postData from "../../hook/postData";
import deleteData from "../../hook/deleteData";
import { getEnv } from "@urecruits/api";
const { API_ASSESSMENT } = getEnv();

interface Language {
  id: number;
  name: string;
  version?: string;
  displayName?: string;
}

interface SelectOption {
  id: number;
  name: string;
  [key: string]: any;
}

const HomeTaskStepOne = (props) => {
  const {
    setIsNameQuestion,
    homeQuestion,
    requiredFieldByCreateButton,
    validFields,
    setValidFields,
  } = props;

  const [isCheckedTestCases, setIsCheckedTestCases] = useState(
    homeQuestion.useAIGeneratedTestCases
  );
  const [isCheckedTestCasesOriginalValue, setIsCheckedTestCasesOriginalValue] =
    useState(homeQuestion.useAIGeneratedTestCases);
  const [isGeneratingTestCases, setIsGeneratingTestCases] = useState(false);
  const [testCaseId, setTestCaseId] = useState<string | null>(null);

  const [languagesData, setLanguagesData] = useState<Language[]>([]);
  const [validFieldsButtonEvent, setValidFieldsButtonEvent] = useState(false);
  const [validFieldsName, setValidFieldsName] = useState(false);
  const [validFieldsDescription, setValidFieldsDescription] = useState(false);
  const [dialogCreateDatabase, setDialogCreateDatabase] = useState(false);
  const [packageOptions, setPackageOptions] = useState([]);
  const [databaseOptions, setDatabaseOptions] = useState([]);
  const [databaseDetails, setDatabaseDetails] = useState<any>();

  const dispatch = useDispatch();

  const [initialQuestion, setInitialQuestion] = useState({
    nameQuestion: homeQuestion.nameQuestion,
    questionDescription: homeQuestion.questionDescription,
  });

  const isEditMode = homeQuestion.id && homeQuestion.id !== "new";

  const hasQuestionChanged =
    initialQuestion.nameQuestion !== homeQuestion.nameQuestion ||
    initialQuestion.questionDescription !== homeQuestion.questionDescription;

  useEffect(() => {
    dispatch(
      setAssessmentQuestion({
        ...homeQuestion,
        databaseId: { id: homeQuestion.database },
      })
    );
  }, [dispatch, homeQuestion]);

  useEffect(() => {
    dispatch(
      setAssessmentQuestion({
        ...homeQuestion,
        useAIGeneratedTestCases: isCheckedTestCases,
        isIncludeTestCases: isCheckedTestCases,
      })
    );
  }, [dispatch, isCheckedTestCases, homeQuestion]);

  const handleCheckTestCases = (e) => {
    setIsCheckedTestCases(e.target.checked);

    dispatch(
      setAssessmentQuestion({
        ...homeQuestion,
        useAIGeneratedTestCases: e.target.checked,
        isIncludeTestCases: e.target.checked,
        testCases: e.target.checked ? homeQuestion.testCases : [],
      })
    );
  };
  const generateAITestCases = async () => {
    if (!homeQuestion.languages || !homeQuestion.questionDescription) {
      return null;
    }

    // Only skip API call if we're in edit mode and the value was originally true from the database
    if (isEditMode && isCheckedTestCasesOriginalValue) {
      dispatch(
        setAssessmentQuestion({
          ...homeQuestion,
          formStep: homeQuestion.formStep + 1,
        })
      );
      return true;
    }

    setIsGeneratingTestCases(true);
    try {
      if (isEditMode) {
        if (isEditMode && homeQuestion.id) {
          try {
            await deleteData(
              `${API_ASSESSMENT}/api/take-home-task/question/${homeQuestion.id}/testcases`,
              {}
            );
          } catch (deleteError) {}
        }
      }
      try {
        // Step 1: POST request to create test cases
        const createResponse = await postData(
          `${API_ASSESSMENT}/api/generate-test-cases`,
          {
            languageId: homeQuestion.languages.id,
            questionDescription: homeQuestion.questionDescription,
            nameQuestion: homeQuestion.nameQuestion,
          }
        );

        if (createResponse && createResponse.id) {
          setTestCaseId(createResponse.id);

          // Step 2: GET request to retrieve the generated test cases
          const getResponse = await fetchData(
            `${API_ASSESSMENT}/api/generate-test-cases/${createResponse.id}`,
            (data) => console.log("Test cases data received:", data)
          );

          if (getResponse && getResponse.data) {
            dispatch(
              setAssessmentQuestion({
                ...homeQuestion,
                aiGeneratedTestCases: getResponse.data,
                testCases: getResponse.data.testCasesData.testCases,
                testCaseInputs: getResponse.data.testCasesData.testCaseInputs,
                formStep: homeQuestion.formStep + 1,
              })
            );
            return true;
          }
        }
      } catch (apiError) {
        const mockResponse = {
          id: "gen_1747036721771_ag7bpf3",
          languageId: 63,
          questionName: "String reversal",
          generatedAt: "2025-05-12T07:58:47.764Z",
          testCases: {
            testCaseInputs: [
              {
                name: "string",
                type: "string",
                description: "The string to be reversed",
              },
            ],
            testCases: [
              {
                inputs: [
                  {
                    name: "string",
                    value: "hello",
                  },
                ],
                output: "olleh",
              },
              {
                inputs: [
                  {
                    name: "string",
                    value: "",
                  },
                ],
                output: "",
              },
              {
                inputs: [
                  {
                    name: "string",
                    value: "a",
                  },
                ],
                output: "a",
              },
              {
                inputs: [
                  {
                    name: "string",
                    value: "racecar",
                  },
                ],
                output: "racecar",
              },
              {
                inputs: [
                  {
                    name: "string",
                    value: "12345",
                  },
                ],
                output: "54321",
              },
            ],
          },
        };

        dispatch(
          setAssessmentQuestion({
            ...homeQuestion,
            aiGeneratedTestCases: mockResponse.testCases,
            testCases: mockResponse.testCases.testCases,
            testCaseInputs: mockResponse.testCases.testCaseInputs,
            formStep: homeQuestion.formStep + 1,
          })
        );
        return true;
      }

      return true;
    } catch (error) {
      return false;
    } finally {
      setIsGeneratingTestCases(false);
    }
  };

  const handleNext = () => {
    isCheckedTestCases
      ? navigate("/coding-assessments/take-home-task/content-question")
      : navigate("/coding-assessments/take-home-task/test-cases");
  };

  const onHandleNameQuestion = (e) => {
    dispatch(
      setAssessmentQuestion({
        ...homeQuestion,
        nameQuestion: e.target.value,
      })
    );
    setIsNameQuestion(e.target.value);
    setValidFieldsName(
      e.target.value === "Untitled question" || e.target.value === ""
    );
  };

  useEffect(() => {
    if (!languagesData.length) {
      fetchData(`${API_ASSESSMENT}/api/languages`, setLanguagesData)
        .then((res) => {
          const languages = res.data.map((lang) => ({
            id: lang.id,
            name: lang.name,
            version: lang.version,
            displayName: `${lang.name}${
              lang.version ? ` (${lang.version})` : ""
            }`,
            languageId: lang.id,
          }));
          setLanguagesData(languages);
        })
        .catch((error) => {
          console.error("❌ Error fetching languages:", error);
        });
    }
  }, [languagesData]);

  useEffect(() => {
    fetchData(
      `${API_ASSESSMENT}/api/take-home-task/packages`,
      setPackageOptions
    ).then((res) => res.data);
  }, []);

  useEffect(() => {
    if (!dialogCreateDatabase) {
      fetchData(
        `${API_ASSESSMENT}/api/assesment-database`,
        setDatabaseOptions
      ).then((res) => res.data);
    }
  }, [dialogCreateDatabase]);

  useEffect(() => {
    setValidFields(
      !(
        homeQuestion.nameQuestion === "" ||
        homeQuestion.languages === "" ||
        homeQuestion.questionDescription === ""
      ) &&
        !(
          homeQuestion.nameQuestion === "Untitled question" ||
          homeQuestion.languages === null ||
          homeQuestion.questionDescription === null
        )
    );
  }, [
    homeQuestion.languages,
    homeQuestion.nameQuestion,
    homeQuestion.questionDescription,
    setValidFields,
  ]);

  useEffect(() => {
    dispatch(
      setValidStatusBar({ questionId: homeQuestion.id, stepOne: validFields })
    );
  }, [dispatch, homeQuestion.id, validFields]);

  const onCreateDatabase = () => {
    setDialogCreateDatabase(true);
  };

  useEffect(() => {
    if (homeQuestion.id) {
      setInitialQuestion({
        nameQuestion: homeQuestion.nameQuestion,
        questionDescription: homeQuestion.questionDescription,
      });
    }
  }, []);

  return (
    <NavigateToSteps>
      <div className="task-home">
        <form onSubmit={handleNext}>
          <div className="task-home__container">
            <h3 className="task-home__container__title">
              01. Question Details
            </h3>
            <label className="label-task-font" htmlFor="task-home-label">
              <span>
                Name of Question <span className="asterisk-sign">*</span>
              </span>
            </label>
            <input
              className={`live-task-input ${
                validateFormField(
                  homeQuestion.nameQuestion,
                  validFieldsName,
                  requiredFieldByCreateButton || validFieldsButtonEvent
                ) && "error"
              }`}
              type="text"
              maxLength={120}
              value={
                homeQuestion.nameQuestion === "Untitled question"
                  ? ""
                  : homeQuestion.nameQuestion
              }
              placeholder="Enter question title"
              onChange={onHandleNameQuestion}
            />
            {validateFormField(
              homeQuestion.nameQuestion,
              validFieldsName,
              requiredFieldByCreateButton || validFieldsButtonEvent
            ) && <div className="error-data-one">Invalid data</div>}
            <div style={{ marginTop: "20px", marginBottom: "5px" }}>
              <CheckboxButton
                style={{ paddingRight: "25px", top: "-2px" }}
                type="checkbox"
                name={"item"}
                handleClick={handleCheckTestCases}
                isChecked={homeQuestion.useAIGeneratedTestCases}
              />
              <span
                style={{
                  paddingLeft: "15px",
                  fontSize: "14px",
                  color: homeQuestion.useAIGeneratedTestCases ? "" : "#999EA5",
                }}
              >
                Include Test Cases
              </span>
              {!homeQuestion.useAIGeneratedTestCases && (
                <span
                  style={{
                    paddingLeft: "5px",
                    fontSize: "12px",
                    color: "#099C73",
                    fontStyle: "italic",
                  }}
                >
                  (Test cases will be AI-generated)
                </span>
              )}
            </div>
            <div className="two-input-task">
              <div className="two-input-task-select-bar">
                <label htmlFor="live-task-label" className="label-task-font">
                  <span>
                    Language <span className="asterisk-sign">*</span>
                  </span>
                </label>
                <CodingSelect
                  validate={
                    validFieldsButtonEvent &&
                    homeQuestion.languages?.id === null
                  }
                  placeholder="Select languages"
                  options={languagesData.map((lang) => {
                    const option = {
                      id: lang.id,
                      name: lang.displayName || lang.name,
                      version: lang.version,
                      originalName: lang.name,
                      languageId: lang.id,
                    };
                    return option;
                  })}
                  selected={(() => {
                    const selected = languagesData.find(
                      (i) => i.id === homeQuestion.languages?.id
                    );
                    return selected;
                  })()}
                  setSelected={(option: SelectOption) => {
                    if (option) {
                      const updatedLanguages = {
                        id: option.id,
                        name: option.name,
                        version: option.version,
                        languageId: option.id,
                      };
                      dispatch(
                        setAssessmentQuestion({
                          ...homeQuestion,
                          languages: updatedLanguages,
                        })
                      );
                    }
                  }}
                />
              </div>
              <div className="two-input-task-select-bar">
                <label htmlFor="live-task-label" className="label-task-font">
                  Packages
                </label>
                <CodingSelect
                  placeholder="Select packages"
                  options={packageOptions}
                  selected={packageOptions?.find(
                    (i) => i.id == homeQuestion.packages?.id
                  )}
                  setSelected={(activePackage) => {
                    if (activePackage) {
                      dispatch(
                        setAssessmentQuestion({
                          ...homeQuestion,
                          packages: activePackage,
                        })
                      );
                    }
                  }}
                />
              </div>
            </div>
            {/* <div className="input-button-database">
              <div className="two-input-task-select-bar input-database">
                <label htmlFor="live-task-label" className="label-task-font">
                  <span>Database</span>
                </label>
                <CodingSelect
                  placeholder="Select database"
                  options={databaseOptions}
                  selected={databaseDetails}
                    setSelected={(activeDatabase) => {
                      for (const key in activeDatabase) {
                        if (activeDatabase.hasOwnProperty("id")) {
                        dispatch(
                          setAssessmentQuestion({
                            ...homeQuestion,
                          database: {id: activeDatabase["id"]}
                        }))
                        }
                      }
                      setDatabaseDetails(
                        Object.assign({}, databaseDetails, activeDatabase)
                      );
                    }}
                />
              </div>
              <div className="button-create-database-wrapper">
                <button
                  onClick={onCreateDatabase}
                  type="button"
                  className="button-create-database"
                >
                  Create database
                </button>
                {dialogCreateDatabase && (
                  <DialogDatabase
                    homeQuestion={homeQuestion}
                    onDialog={setDialogCreateDatabase}
                  />
                )}
              </div>
            </div> */}
            <label htmlFor="live-task-label" className="label-task-font">
              Question Description <span className="asterisk-sign">*</span>
            </label>
            <textarea
              value={homeQuestion.questionDescription}
              rows={4}
              className={`live-task-input-textarea ${
                validateFormField(
                  homeQuestion.questionDescription,
                  validFieldsDescription,
                  requiredFieldByCreateButton || validFieldsButtonEvent
                ) && "error"
              }`}
              placeholder="Write question description"
              onChange={(e) => {
                dispatch(
                  setAssessmentQuestion({
                    ...homeQuestion,
                    questionDescription: e.target.value,
                  })
                );
                setValidFieldsDescription(
                  e.target.value === "" ||
                    homeQuestion.questionDescription === null
                );
              }}
            />
            {validateFormField(
              homeQuestion.questionDescription,
              validFieldsDescription,
              requiredFieldByCreateButton || validFieldsButtonEvent
            ) && <div className="error-data-one">Invalid data</div>}
            <div
              style={{ fontSize: "12px", marginTop: "20px", color: "#999EA5" }}
            >
              This description will be visible only in list of coding questions
            </div>
            <div className="first-group-button-task">
              <div>
                <button
                  onClick={async (e) => {
                    e.preventDefault();
                    if (!validFields) {
                      setValidFieldsButtonEvent(true);
                      return;
                    }

                    if (isCheckedTestCases) {
                      // If user wants to create their own test cases
                      const success = await generateAITestCases();
                      if (success) {
                        // Test cases were generated successfully
                        // formStep is already updated in generateAITestCases
                      } else {
                        // Move to the next step without AI-generated test cases
                        dispatch(
                          setAssessmentQuestion({
                            ...homeQuestion,
                            formStep: homeQuestion.formStep + 1,
                          })
                        );
                      }
                    } else {
                      // If user doesn't want test cases, skip to step 3
                      dispatch(
                        setAssessmentQuestion({
                          ...homeQuestion,
                          formStep: homeQuestion.formStep + 2,
                        })
                      );
                    }
                    setValidFieldsButtonEvent(true);
                  }}
                  type="submit"
                  disabled={isGeneratingTestCases}
                  className="button-save"
                >
                  {isGeneratingTestCases ? "Generating Test Cases..." : "Next"}
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </NavigateToSteps>
  );
};

export default HomeTaskStepOne;
