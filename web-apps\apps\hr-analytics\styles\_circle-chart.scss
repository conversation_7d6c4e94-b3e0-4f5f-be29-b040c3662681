@use "config" as *;
@use "mixins" as *;
.circle-chart {
  width: calc(50% - 28px);
  margin: 0 28px 28px 0;

  @include media(md) {
    width: 100%;
    margin: 0 0 28px 0;
  }
}

.progress-circle {
  &:nth-child(1) {
    margin-right: 14px;
    @include media(sm) {
      margin-right: 0;
    }
  }

  &:nth-child(2) {
    margin-left: 14px;
    @include media(sm) {
      margin-left: 0;
    }
  }
}

.circle-chart {
  background: $white;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid $grayTone2;
  width: 100%;

  .circle-chart__header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    @include media(sm) {
      flex-direction: column;
      align-items: start;
      justify-content: start;
      gap: 20px;
    }
  }

  &__select {
    width: 20%;
    @include media(sm) {
      width: 40%;
    }
  }

  &__headline {
    color: $black;
    font-size: 20px;
    text-transform: uppercase;
    line-height: 1;
    font-family: "Inter", "Poppins", sans-serif;
    font-weight: 600;

    @include media(sm) {
      font-size: 14px;
    }
  }

  &__inner {
    display: flex;
    margin-top: 24px;
    align-items: center;
    justify-content: space-around;
  }

  &__left {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    .recharts-wrapper {
      display: flex;
      justify-content: center;
      svg {
        width: 100% !important;
      }
    }
    @include media(sm) {
      width: 150px !important;
      svg {
        width: 150px !important;
      }
    }
  }

  &__info {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    animation: 1.5s ease-in smooth;

    &__count {
      color: $black;
      font-size: 48px;
      font-weight: 600;
      line-height: 1;
      margin-bottom: 4px;
      font-family: "Inter", "Poppins", "sans-serif";
    }

    &__label {
      color: $grayTone4;
      font-size: 14px;
      line-height: 1;
      font-family: "Inter", "Poppins", "sans-serif";
    }
  }

  &__list {
    padding-left: 0;
    animation: 1.5s ease-in smooth;
    @include media(sm) {
      padding-left: 30px;
    }
  }

  &__item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  &__point {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 12px;
    display: block;
  }

  &__name {
    line-height: 1;
    font-size: 14px;
    color: $grayTone5;

    span {
      font-weight: 700;
      color: $black;
    }
  }
}

.progress-circle {
  // @include card;
  width: calc(50% - 14px);
  margin-bottom: 28px;
  @include media(sm) {
    width: 100%;
  }

  &__headline {
    // @include headline;
    margin-bottom: 24px;
    font-size: 12px;
    text-transform: uppercase;
  }

  &__inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__value {
    font-size: 48px;
    font-weight: 600;
    color: $black;
    line-height: 1;
    font-family: "Inter", "Poppins", sans-serif;
    animation: 1.5s ease-in smooth;

    span {
      font-size: 14px;
      color: $grayTone4;
    }
  }

  &__label {
    line-height: 1;
    font-size: 14px;
    font-family: "Inter", "Poppins", sans-serif;
    color: $grayTone6;
    font-weight: 400;
  }
}
