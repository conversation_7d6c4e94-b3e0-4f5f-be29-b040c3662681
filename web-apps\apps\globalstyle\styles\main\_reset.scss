:before, :after, a, abbr, acronym, address, applet, article, aside, audio, b, big, blockquote, body, button, canvas, caption, center, cite, code, dd, del, details, dfn, div, dl, dt, em, embed, fieldset, figcaption, figure, footer, form, h1, h2, h3, h4, h5, h6, header, html, i, iframe, img, input, ins, kbd, label, legend, li, mark, menu, nav, object, ol, output, p, pre, q, ruby, s, samp, section, select, small, span, strike, strong, sub, summary, sup, table, tbody, td, textarea, tfoot, th, thead, time, tr, tt, u, ul, var, video {
  font: inherit;
  vertical-align: baseline;
  border: none;
  outline: 0;
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

article, aside, details, figcaption, figure, footer, header, menu, nav, section {
  display: block;
}

body {
  line-height: 1em;
}

a {
  text-decoration: none;
  cursor: pointer;
}

img {
  width: 100%;
  max-width: 100%;
  height: auto;
  vertical-align: top;
}

svg {
  width: 100%;
  height: auto;
  overflow: visible;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:after,
blockquote:before,
q:after,
q:before {
  content: '';
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

button,
input,
select,
textarea {
  background: none;
}

button,
input[type='submit'],
input[type='button'],
input[type='reset'] {
  cursor: pointer;
}

input::-ms-clear {
  display: none;
}
