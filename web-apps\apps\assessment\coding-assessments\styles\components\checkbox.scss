@use "../config" as *;
@use "../mixins" as *;
/* The container */
.container {
    position: relative;
    padding-left: 10px;
    margin: 0 0 12px 5px;
    cursor: pointer;
    font-size: 22px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
}

  /* Hide the browser's default checkbox */
  .container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }

  /* Create a custom checkbox */
  .checkmark {
    position: absolute;
    top: 5px;
    left: 0;
    height: 16px;
    width: 16px;
    background-color: white;
    border: 1px solid #C1C5CB;
    border-radius: 2px;
  }
  .checkmark2:checked {
    position: absolute;
    top: 0;
    left: 0;
    height: 16px;
    width: 16px;
    // background-color: #eee;
    border: 1px solid #099C73;
    border-radius: 2px;
  }
  .container2:active input ~ .checkmark {
    height: 16px;
    width: 16px;
    border: 1px solid #099C73;
  }

  /* On mouse-over, add a grey background color */
  .container:hover input ~ .checkmark {
    // background-color: #ccc;
    height: 16px;
    width: 16px;
    border: 1px solid #099C73;
  }

  /* When the checkbox is checked, add a blue background */
  .container input:checked ~ .checkmark {
    // background-color: #2196F3;
    height: 16px;
    width: 16px;
    border: 1px solid #099C73;
  }

  /* Create the checkmark/indicator (hidden when not checked) */
  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }

  /* Show the checkmark when checked */
  .container input:checked ~ .checkmark:after {
    display: block;
  }

  .container2 input:checked ~ .checkmark:after {
    display: block;
  }
  .container .checkmark:after {
    left: 4.5px;
    top: 1.5px;
    width: 5px;
    height: 8px;
    border: solid #099C73;
    border-width: 0 1px 1px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
  }

  /* Style the checkmark/indicator */
  .container .checkmark:after {
    left: 4.5px;
    top: 1.5px;
    width: 5px;
    height: 8px;
    border: solid #099C73;
    border-width: 0 1px 1px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
  }

// input[type="checkbox"] {
//     display: none;
//   }
//   input[type="checkbox"] + span {
//     display: inline-block;
//     position: relative;
//     top: -1px;
//     width: 12px;
//     height: 12px;
//     margin: -1px 0px 0 0;
//     vertical-align: middle;
//     background: white left top no-repeat;
//     border: 1px solid #ccc;
//     cursor: pointer;
//   }
//   input[type="checkbox"]:checked + span {
//     background: red -19px top no-repeat;
//   }

//   input[type="checkbox"] + span {
//     margin-right: 4px;
//   }
