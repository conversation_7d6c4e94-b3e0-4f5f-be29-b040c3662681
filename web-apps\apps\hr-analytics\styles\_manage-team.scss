@use "config" as *;
@use "mixins" as *;

.manage-team {
  &__top {
    &__svg {
      margin-left: auto;
      position: relative;
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: .3s ease-in, .3s ease-in-out;
      border-radius: 50%;

      @include media(md) {
        display: none;
      }

      &.active {
        background: $grayTone1;
      }

      svg {
        width: 24px;
        height: 24px;
        cursor: pointer;

      }
    }


    &__left {
      width: 90%;
      display: flex;
      align-items: center;

      @include media(md) {
        width: 100%;
      }
    }
  }

  &__name {
    display: flex;
    align-items: center;

    &__avatar {
      width: 28px;
      height: 28px;
      object-fit: contain;
      border-radius: 50%;
    }

    &__text {
      font-weight: 500;
      font-size: 14px;
      line-height: 1;
      color: $black;
      padding-left: 6px;
      text-transform: capitalize;
    }
  }

  &__dropdown {
    position: relative;
    width: 100%;
    display: flex;
    align-items: flex-start;
    height: 29px;

    &__button {
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding: 0 12px 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0);

      p {
        width: calc(100% - 32px);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: left;
      }

      svg {
        width: 16px;
        height: 16px;
        transition: .3s ease-in, .3s ease-in-out;
      }
    }

    &__wrap {
      border-radius: 4px;
      position: absolute;
      left: 0;
      top: 0;
      width: calc(100% + 13px);
      padding: 8px 0;
      border: 1px solid rgba(255, 255, 255, 0);
      transform: translate(-13px, -6px);
      z-index: 0;

      &.active {
        border: 1px solid $grayTone2;
        background: $white;
        z-index: 1;

        .manage-team__dropdown__button {
          border-bottom: 1px solid $grayTone2;

          svg {
            transform: rotate(180deg);

            path {
              fill: $mainGreen;
            }
          }
        }

        .manage-team__dropdown__list {
          display: flex;

        }
      }
    }


    &__list {
      display: none;
      flex-direction: column;
      padding: 0 12px;
    }

    &__item {
      margin-top: 16px;

      &:first-child {
        margin-top: 8px;
      }
    }
  }

  &__text {
    font-weight: 400;
    font-size: 14px;
    line-height: 1.3;
    word-break: break-word;

    &__button {
      margin-left: 12px;
    }

    &--green {
      font-weight: 500;
      color: $mainGreen;
    }

    &--gray5 {
      color: $grayTone5;
    }

    &--gray7 {
      font-weight: 500;
      color: $grayTone7;
    }

    &--black {
      color: $black;
    }

    &--link {
      font-weight: 500;
      color: $mainGreen;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  &__column {
    &__default {
      width: 200px;
    }

    &__middle {
      width: 280px;
    }
  }
}

.order-popup-mtm {
  position: absolute;
  top: 49px;
  right: 0;
  width: 230px;
  background: $white;
  box-shadow: 0px 0px 8px rgba(13, 16, 37, 0.15);
  border-radius: 3px;
  padding: 12px;
  cursor: default;
  display: flex;
  flex-direction: column;
  z-index: 10;

  &__item {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 2px 0;
    margin-bottom: 12px;

    .custom-checkbox {
      .custom-checkbox__label {
        align-items: center;

        .custom-checkbox__item {
          margin-top: 0;
        }

        .custom-checkbox__text {
          line-height: 1;
          font-size: 12px;
          margin-top: 0;
        }
      }
    }

    svg {
      width: 16px;
      height: 16px;
    }

    &.inactive {
      svg {
        cursor: default;
      }

      .custom-checkbox {
        .custom-checkbox__label {
          cursor: default;

          .custom-checkbox__item {
            border: 1px solid $grayTone3;
            cursor: default;

            span {
              background: url("../image/icon/done__inactive_ic.svg") no-repeat;
              background-size: contain;
            }
          }

          .custom-checkbox__text {}
        }
      }
    }
  }

  &__submit {
    margin-top: 12px;
    font-weight: 900;
    font-size: 14px;
    line-height: 1;
    color: $grayTone7;
    margin-left: auto;
  }
}

.order-popup-mtm-enter {
  opacity: 0;
  transform: scale(0.9);
}

.order-popup-mtm-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 300ms, transform 300ms;
}

.order-popup-mtm-exit {
  opacity: 1;
}

.order-popup-mtm-exit-active {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 300ms, transform 300ms;
}

.filters-popup-mtm-buttons-list {
  display: flex;
  flex-wrap: wrap;
  margin: -12px -6px 0 -6px;

  &__item {
    margin: 12px 6px 0 6px;
    border: 1px solid $grayTone2;
    cursor: pointer;
    padding: 12px 20px;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1;
    color: $grayTone7;
    transition: .3s ease-in, .3s ease-in-out;
    &.active {
      background: rgba(172, 216, 209, 0.3);
      border-radius: 1px 4px 4px 1px;
      color: $mainGreen;
  
      &:before {
        opacity: 1;
        background: $mainGreen;
        border-radius: 3px 0px 0px 3px;
      }
    }
  }
}

.filters-popup-mtm-datepicker {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 20px;

  &__item {
    width: calc(50% - 8px);

    @include media(xs) {
      width: 100%;
      margin-bottom: 16px;
    }
  }

  &__label {
    @include label;
  }

  &__date {
    position: relative;


    &--readonly {
      position: relative;

      .react-datepicker-wrapper {
        .react-datepicker__input-container {
          background: $grayTone1;

          input {
            border-color: $grayTone1;
            cursor: default;
          }
        }
      }
    }

    &:before {
      content: "";
      position: absolute;
      right: 0;
      top: 0;
      transform: translate(-14px, 14px);
      z-index: 0;
      width: 16px;
      height: 16px;
      background: url("../image/icon/calendar_ic.svg");
      background-size: contain;
    }
  }

}