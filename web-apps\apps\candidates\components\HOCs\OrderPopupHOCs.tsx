// import OrderPopupInner from "../Global/table/OrderPopup/OrderPopupInner";
// // import {setOrderCandidateJobs} from "../../store/reducers/candidateJobsReducer";

// const setData = (WrappedComponent, setOrderFunc) => {
// 	return function (props) {
// 		const {stateFunc, dispatchFunc, fixedTabFunc} = setOrderFunc();
// 		return <WrappedComponent {...props} stateFunc={stateFunc} dispatchFunc={dispatchFunc} fixedTabFunc={fixedTabFunc}/>;
// 	};
// };


// export const CandidateJobsOrder = setData(OrderPopupInner, () => {
// 	return {
// 		stateFunc: store => store.candidate_jobs.tabFilter,
// 		// dispatchFunc: setOrderCandidateJobs,
// 		fixedTabFunc: store => store.candidate_jobs.fixedTab,
// 	};
// });
