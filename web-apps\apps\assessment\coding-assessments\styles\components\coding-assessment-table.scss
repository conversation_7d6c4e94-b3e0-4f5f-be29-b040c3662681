@use '../mixins' as *;
@use '../config' as *;

.coding-assessment-table{
    &__item{
        &__text{
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            &--green {
              font-weight: 500;
              color: $mainGreen;
              &.bold{
                font-weight: 700;
              }
            }
        }
    }
}

.filters-popup-coding-buttons-list {
    display: flex;
    flex-wrap: wrap;
    margin: -12px -6px 0 -6px;
  
    &__item {
      margin: 12px 6px 0 6px;
      border: 1px solid $grayTone2;
      cursor: pointer;
      padding: 12px 20px;
      border-radius: 4px;
      font-size: 14px;
      line-height: 1;
      color: $grayTone7;
      transition: .3s ease-in, .3s ease-in-out;
      &.active {
        background: rgba(172, 216, 209, 0.3);
        border-radius: 1px 4px 4px 1px;
        color: $mainGreen;
    
        &:before {
          opacity: 1;
          background: $mainGreen;
          border-radius: 3px 0px 0px 3px;
        }
      }
    }
  }
  
  .filters-popup-coding-datepicker {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-bottom: 20px;
  
    &__item {
      width: calc(50% - 8px);
  
      @include media(xs) {
        width: 100%;
        margin-bottom: 16px;
      }
    }
  
    &__label {
      @include label;
    }
  
    &__date {
      position: relative;
  
  
      &--readonly {
        position: relative;
  
        .react-datepicker-wrapper {
          .react-datepicker__input-container {
            background: $grayTone1;
  
            input {
              border-color: $grayTone1;
              cursor: default;
            }
          }
        }
      }
  
      &:before {
        content: "";
        position: absolute;
        right: 0;
        top: 0;
        transform: translate(-14px, 14px);
        z-index: 0;
        width: 16px;
        height: 16px;
        background-size: contain;
      }
    }
  
  }