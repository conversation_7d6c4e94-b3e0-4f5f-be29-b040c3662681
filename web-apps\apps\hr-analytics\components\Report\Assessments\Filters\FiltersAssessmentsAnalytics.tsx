import AsyncSelect from 'react-select/async';
import { AssessmentsTab } from '../../../../enums/assessments/assessmentsEnums';
import { store, useTypedSelector } from "../../../../store";
import {
  setAssessmentTypeFilterAssessments,
  setStatusFilterAssessments,
  setDateOfCreationFilterAssessments,
  setAssessmentTypeListFilterAssessments,
  setStatusListFilterAssessments
} from '../../../../store/reducers/assessments/assessmentsTableReducer';
import { memo, useEffect } from "react";
import Option from "../../../Global/SelectOptions";
import { selectCustomStyle } from '@ucrecruits/globalstyle/styles/selectCustomStyle';
import DatePicker from 'react-datepicker';
import { IAssessmentsDateOfCreationItem } from '../../../../types/redux/assessments';

const dateCreationRanges: Array<IAssessmentsDateOfCreationItem["duration"]> = [
  "Last 30 Days",
  "Last 90 Days",
  "Last 3 months",
  "Last 6 months",
  "Last 12 months",
  "This year",
  "Last year",
  "All time"
];

const getFiltersFunc = state => state.assessments.filters;
const getFilterDataFunc = state => state.assessments.filterInfo;

const FiltersAssessmentsAnalytics = ({ activeTab }) => {
  const filter = useTypedSelector(getFiltersFunc);
  const filterData = useTypedSelector(getFilterDataFunc);

  // Load filter options when component mounts
  useEffect(() => {
    // Load assessment types for the AsyncSelect
    const loadAssessmentTypes = async () => {
      try {
        // Static options as fallback (replace with API call if available)
        const assessmentTypes = [
          { id: 'take-home', label: 'Take-Home' },
          { id: 'live-task', label: 'Live-Coding' },
          { id: 'domain', label: 'Domain' }
        ];
        store.dispatch(setAssessmentTypeListFilterAssessments(assessmentTypes));
      } catch (error) {
        console.error('Error loading assessment types:', error);
      }
    };

    // Load status options
    const loadStatusOptions = () => {
      const statusOptions = [
        { id: 'ACTIVE', label: 'ACTIVE' },
        { id: 'INACTIVE', label: 'INACTIVE' }
      ];
      store.dispatch(setStatusListFilterAssessments(statusOptions));
    };

    loadAssessmentTypes();
    loadStatusOptions();
  }, []);

  return (
    <div className='table-filter-popup__right__content'>
      {activeTab === AssessmentsTab.TYPE && (
        <AsyncSelect
          components={{ Option }}
          isMulti
          cacheOptions
          loadOptions={(inputValue) => {
            // Filter assessment types based on input
            const filteredOptions = (filterData.assessmentTypeList || []).filter(option =>
              option?.label?.toLowerCase().includes(inputValue.toLowerCase())
            );
            return Promise.resolve(filteredOptions);
          }}
          defaultOptions={filterData.assessmentTypeList || []}
          value={filter.assessmentType || []}
          onChange={(option: any) => {
            store.dispatch(setAssessmentTypeFilterAssessments(option || []));
          }}
          closeMenuOnSelect={false}
          placeholder='Search by assessment type'
          styles={selectCustomStyle}
          id="assessmentType"
          instanceId="assessmentType"
        />
      )}
      {activeTab === AssessmentsTab.STATUS && (
        filterData.statusList ? (
          filterData.statusList.length > 0 ? (
            <ul className='filters-popup-mtm-buttons-list'>
              {filterData.statusList.map(item => (
                item && item.id ? (
                  <li
                    key={item.id} // Ensure unique key
                    className={`filters-popup-mtm-buttons-list__item ${
                      filter.status.some(x => x.id === item.id) ? 'active' : ''
                    }`}
                    onClick={() => 
                      store.dispatch(
                        setStatusFilterAssessments(
                          filter.status.some(x => x.id === item.id)
                            ? filter.status.filter(x => x.id !== item.id)
                            : [...filter.status, item]
                        )
                      )
                    }
                  >
                    {item.label}
                  </li>
                ) : null
              ))}
            </ul>
          ) : (
            <ul className='filters-popup-mtm-buttons-list'>
              <li className='filters-popup-mtm-buttons-list__item'>
                No Statuses Found.
                <br>Add assessments to filter by status.</br>
              </li>
            </ul>
          )
        ) : (
          <p>Loading statuses...</p>
        )
      )}
      {activeTab === AssessmentsTab.CREATED_AT && (
        <>
          <div className='filters-popup-mtm-datepicker'>
            <div className='filters-popup-mtm-datepicker__item'>
              <label className="filters-popup-mtm-datepicker__label">From</label>
              <div className='filters-popup-mtm-datepicker__date'>
                <DatePicker
                  selected={filter.dateOfCreation.from}
                  showYearDropdown={true}
                  scrollableYearDropdown={true}
                  yearDropdownItemNumber={70}
                  onChange={(date: any) => {
                    store.dispatch(
                      setDateOfCreationFilterAssessments({
                        from: date,
                        to: filter.dateOfCreation.to,
                        duration: filter.dateOfCreation.duration
                      })
                    );
                  }}
                  dateFormat="MMMM d, yyyy"
                  maxDate={new Date()}
                  placeholderText="From"
                  id={'creationFrom'}
                />
              </div>
            </div>
            <div className='filters-popup-mtm-datepicker__item'>
              <label className="filters-popup-mtm-datepicker__label">To</label>
              <div
                className={`filters-popup-mtm-datepicker__date ${
                  filter.dateOfCreation.from ? '' : 'filters-popup-mtm-datepicker__date--readonly'
                }`}
              >
                <DatePicker
                  selected={filter.dateOfCreation.to}
                  showYearDropdown={true}
                  scrollableYearDropdown={true}
                  yearDropdownItemNumber={70}
                  onChange={(date: any) => {
                    store.dispatch(
                      setDateOfCreationFilterAssessments({
                        from: filter.dateOfCreation.from,
                        to: date,
                        duration: filter.dateOfCreation.duration
                      })
                    );
                  }}
                  readOnly={!filter.dateOfCreation.from}
                  dateFormat="MMMM d, yyyy"
                  maxDate={new Date()}
                  minDate={filter.dateOfCreation.from}
                  placeholderText="To"
                />
              </div>
            </div>
          </div>
          <ul className='filters-popup-mtm-buttons-list'>
            {dateCreationRanges.map(item => (
              <li
                key={item} // Unique key for static array
                className={`filters-popup-mtm-buttons-list__item ${
                  filter.dateOfCreation.duration === item ? 'active' : ''
                }`}
                onClick={() =>
                  store.dispatch(
                    setDateOfCreationFilterAssessments({
                      from: filter.dateOfCreation.from,
                      to: filter.dateOfCreation.to,
                      duration: item
                    })
                  )
                }
              >
                {item}
              </li>
            ))}
          </ul>
        </>
      )}
    </div>
  );
};

export default memo(FiltersAssessmentsAnalytics);