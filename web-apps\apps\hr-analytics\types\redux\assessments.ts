import { ITabsFilter } from "../global/global";

export interface IAssessmentsTable {
  activeTab: string;
  isLoading: boolean;
  emptySearch: boolean;
  emptyTable: boolean;
  emptyFilter: boolean;
  filters: {
    searchValue: string;
    sortBy: string;
    sortType: string;
    assessmentType: Array<{ id: string; label: string }>;
    status: Array<{ id: string; label: string }>;
    dateOfCreation: IAssessmentsDateOfCreationItem;
  };
  fixedTab: ITabsFilter;
  tableItems: Array<IAssessmentsTableItem>;
  tabFilter: Array<ITabsFilter>;
  pagination: {
    currentPage: number;
    limit: number;
    totalCount: number;
    afterKeyForNextPage: any;
  };
  filterInfo: {
    assessmentTypeList: Array<{ id: string; label: string }>;
    statusList: Array<{ id: string; label: string }>;
    tabs: Array<string>;
  };
}

export interface IAssessmentsDateOfCreationItem {
  from: Date | null;
  to: Date | null;
  duration:
  | "Last 30 Days"
  | "Last 90 Days"
  | "Last 3 months"
  | "Last 6 months"
  | "Last 12 months"
  | "This year"
  | "Last year"
  | "All time"
  | null;
}

export interface IAssessmentsTableItem {
  id: number;
  name: string;
  companyId: number | null;
  assessmentType: string;
  description: string;
  status: "ACTIVE" | "INACTIVE" | "";
  languageId: number | null;
  packageId: number | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface IAssessmentsStatusPie {
  totalAssessments: number;
  assessmentsData: Array<{ key: string; doc_count: number }>;
}

export interface IAssessmentDetailAreaView {
  assessmentStatus: string;
  assessmentsData: Array<{ key: number; doc_count: number; key_as_string: string }>;
  duration: string;
}

export type IAssessmentAreaArray = Array<{ key: number; doc_count: number; key_as_string: string }>;
