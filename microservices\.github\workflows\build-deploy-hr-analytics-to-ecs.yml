name: Deploy HR Analytics API to Amazon ECS

on:
  push:
    branches:
      - development
    paths:
      - 'apps/hr-analytics-api/**'
      - 'dockerfiles/Dockerfile.hr-analytics'
      - 'libs/**'
      - 'tools/**'
      - '*.js'
      - '*.ts'
      - '*.json'
      - '*.env'
      - '*.yml'
      - '*.yaml'

env:
  AWS_REGION: ${{ vars.AWS_REGION }}
  AWS_ACCOUNT_ID: ${{ vars.AWS_ACCOUNT_ID}}
  DEV_HR_REPO: ${{ vars.DEVELOPMENT_HR_ANALYTICS_REPO }}
  ECS_CLUSTER: ${{ vars.DEVELOPMENT_ECS_CLUSTER }}
  ECS_SERVICE: ${{ vars.ECS_DEVELOPMENT_HR_ANALYTICS_SERVICE }}
  ECS_TASK_DEFINITION: ${{ vars.ECS_DEVELOPMENT_HR_ANALYTICS_TASK_DEFINITION }}
  CONTAINER_NAME: ${{ vars.ECS_DEVELOPMENT_HR_ANALYTICS_CONTAINER_NAME }}

jobs:
  build-and-deploy:
    runs-on: dev-runner-1
    name: Build & Deploy HR Analytics API
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Build and Push Docker Image to ECR
        id: build-image
        run: |
          GIT_COMMIT=${GITHUB_SHA::7}
          RAND_NUM=$(shuf -i 1000000-9999999 -n 1)
          IMAGE_TAG="$GIT_COMMIT-$RAND_NUM"
          IMAGE=${{ env.AWS_ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/${{ env.DEV_HR_REPO }}:$IMAGE_TAG

          echo "Building Docker image..."
          docker build -t hr-analytics-api:latest -f dockerfiles/Dockerfile.hr-analytics .

          echo "Tagging Docker image as $IMAGE"
          docker tag hr-analytics-api:latest $IMAGE

          echo "Logging in to ECR..."
          aws ecr get-login-password --region ${{ env.AWS_REGION }} | docker login --username AWS --password-stdin ${{ env.AWS_ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com

          echo "Pushing image to ECR..."
          docker push $IMAGE

          echo "image=$IMAGE" >> $GITHUB_OUTPUT

      - name: Download and clean ECS task definition
        id: download-task-def
        run: |
          echo "Fetching current ECS task definition..."
          aws ecs describe-task-definition \
            --task-definition ${{ env.ECS_TASK_DEFINITION }} \
            --query "taskDefinition" \
            --output json > raw-task-def.json

          echo "Cleaning unsupported fields..."
          jq 'del(
            .revision,
            .status,
            .requiresAttributes,
            .compatibilities,
            .registeredAt,
            .registeredBy,
            .taskDefinitionArn,
            .enableFaultInjection
          )' raw-task-def.json > task-def.json

      - name: Render task definition with new image
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-def.json
          container-name: ${{ env.CONTAINER_NAME }}
          image: ${{ steps.build-image.outputs.image }}

      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ${{ env.ECS_SERVICE }}
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true

      - name: Cleanup Docker, Workspace, and System
        if: always()
        run: |
          echo "🧹 Cleaning Docker (images, containers, volumes)..."
          docker system prune -a -f --volumes
