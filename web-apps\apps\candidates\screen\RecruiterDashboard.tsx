import PostedJobTable from "../components/Dashboard/PostedJobTable";
import DashboardDropdown from "../components/Dashboard/DashboardDropdown";
import {Link} from "react-router-dom";
import tempUser from "../image/temp-user.png";
import tempNews from "../image/temp_help-centr.png";
import ProgressCircle from "../components/Dashboard/ProgressCircle";
import {useState} from "react";

const offersAcceptedData1 = {
	minValue: 35,
	maxValue: 125,
};

const offersAcceptedData2 = {
	minValue: 50,
	maxValue: 100,
};

const AdminDashboard = () => {

	return (
		<section className="dashboard">
			<h2 className="dashboard__headline">Home</h2>
			<div className="dashboard__inner recruiter">
				<div className="dashboard__top">
					<LatestEmail/>
					<div className="dashboard__top__middle">
						<ProgressCircle
							data={offersAcceptedData1}
							title="Offers of accepted"
						/>
						<ProgressCircle
							data={offersAcceptedData2}
							title="Offers of accepted"
						/>
						<PostedJobTable styles={{margin: "0 0 28px 0"}}/>
					</div>
					<DashboardDropdown/>
				</div>
				<div className="dashboard__bottom">
					<OngoingProcess/>
					<PendingTasks/>
					<Blog/>
				</div>
			</div>
		</section>
	);
};

export default AdminDashboard;

const LatestEmail = () => {
	return (
		<div className="latest-email">
			<p className="latest-email__headline">
				Latest email
			</p>
			<div className="latest-email__head">
				<p className="latest-email__head__name">Inbox</p>
				<Link to="/" className="latest-email__head__link">more</Link>
			</div>
			<ul className="latest-email__list">
				<li className="latest-email__item">
					<div className="latest-email__item__head">
						<div className="latest-email__item__info">
							<img src={tempUser} alt="" className="latest-email__item__avatar"/>
							<p className="latest-email__item__name">Jon Snow</p>
						</div>
						<p className="latest-email__item__time">18:23</p>
					</div>
					<p className="latest-email__item__description">
						Commodo tristique massa amet urna, egestas maecenas. Commodo tristique massa amet urna, egestas maecenas. Commodo
						tristique massa amet urna, egestas maecenas. Commodo tristique massa amet urna, egestas maecenas.
					</p>
				</li>
				<li className="latest-email__item">
					<div className="latest-email__item__head">
						<div className="latest-email__item__info">
							<img src={tempUser} alt="" className="latest-email__item__avatar"/>
							<p className="latest-email__item__name">Jon Snow</p>
						</div>
						<p className="latest-email__item__time">18:23</p>
					</div>
					<p className="latest-email__item__description">
						Commodo tristique massa amet urna, egestas maecenas. Commodo tristique massa amet urna, egestas maecenas. Commodo
						tristique massa amet urna, egestas maecenas. Commodo tristique massa amet urna, egestas maecenas.
					</p>
				</li>
				<li className="latest-email__item">
					<div className="latest-email__item__head">
						<div className="latest-email__item__info">
							<img src={tempUser} alt="" className="latest-email__item__avatar"/>
							<p className="latest-email__item__name">Jon Snow</p>
						</div>
						<p className="latest-email__item__time">18:23</p>
					</div>
					<p className="latest-email__item__description">
						Commodo tristique massa amet urna, egestas maecenas. Commodo tristique massa amet urna, egestas maecenas. Commodo
						tristique massa amet urna, egestas maecenas. Commodo tristique massa amet urna, egestas maecenas.
					</p>
				</li>
			</ul>
		</div>
	);
};

const OngoingProcess = () => {
	return (
		<div className="ongoing-process">
			<p className="ongoing-process__headline">
				Ongoing process
			</p>
			<ul className="ongoing-process__list">
				<li className="ongoing-process__item">
					<div className="ongoing-process__user">
						<img src={tempUser} alt="" className="ongoing-process__user__avatar"/>
						<div className="ongoing-process__user__info">
							<p className="ongoing-process__user__name">Alex Walling</p>
							<p className="ongoing-process__user__profession">UI/UX Designer</p>
						</div>
					</div>
					<div className="ongoing-process__status hold">Hold</div>
				</li>
				<li className="ongoing-process__item">
					<div className="ongoing-process__user">
						<img src={tempUser} alt="" className="ongoing-process__user__avatar"/>
						<div className="ongoing-process__user__info">
							<p className="ongoing-process__user__name">Alex Walling</p>
							<p className="ongoing-process__user__profession">UI/UX Designer</p>
						</div>
					</div>
					<div className="ongoing-process__status onboarded">Onboarded</div>
				</li>
				<li className="ongoing-process__item">
					<div className="ongoing-process__user">
						<img src={tempUser} alt="" className="ongoing-process__user__avatar"/>
						<div className="ongoing-process__user__info">
							<p className="ongoing-process__user__name">Alex Walling</p>
							<p className="ongoing-process__user__profession">UI/UX Designer</p>
						</div>
					</div>
					<div className="ongoing-process__status hold">Hold</div>
				</li>
				<li className="ongoing-process__item">
					<div className="ongoing-process__user">
						<img src={tempUser} alt="" className="ongoing-process__user__avatar"/>
						<div className="ongoing-process__user__info">
							<p className="ongoing-process__user__name">Alex Walling</p>
							<p className="ongoing-process__user__profession">UI/UX Designer</p>
						</div>
					</div>
					<div className="ongoing-process__status onboarded">Onboarded</div>
				</li>
				<li className="ongoing-process__item">
					<div className="ongoing-process__user">
						<img src={tempUser} alt="" className="ongoing-process__user__avatar"/>
						<div className="ongoing-process__user__info">
							<p className="ongoing-process__user__name">Alex Walling</p>
							<p className="ongoing-process__user__profession">UI/UX Designer</p>
						</div>
					</div>
					<div className="ongoing-process__status hold">Hold</div>
				</li>
				<li className="ongoing-process__item">
					<div className="ongoing-process__user">
						<img src={tempUser} alt="" className="ongoing-process__user__avatar"/>
						<div className="ongoing-process__user__info">
							<p className="ongoing-process__user__name">Alex Walling</p>
							<p className="ongoing-process__user__profession">UI/UX Designer</p>
						</div>
					</div>
					<div className="ongoing-process__status onboarded">Onboarded</div>
				</li>
				<li className="ongoing-process__item">
					<div className="ongoing-process__user">
						<img src={tempUser} alt="" className="ongoing-process__user__avatar"/>
						<div className="ongoing-process__user__info">
							<p className="ongoing-process__user__name">Alex Walling</p>
							<p className="ongoing-process__user__profession">UI/UX Designer</p>
						</div>
					</div>
					<div className="ongoing-process__status hold">Hold</div>
				</li>
				<li className="ongoing-process__item">
					<div className="ongoing-process__user">
						<img src={tempUser} alt="" className="ongoing-process__user__avatar"/>
						<div className="ongoing-process__user__info">
							<p className="ongoing-process__user__name">Alex Walling</p>
							<p className="ongoing-process__user__profession">UI/UX Designer</p>
						</div>
					</div>
					<div className="ongoing-process__status onboarded">Onboarded</div>
				</li>
			</ul>
		</div>
	);
};

const PendingTasks = () => {
	return (
		<div className="pending-tasks">
			<div className="pending-tasks__head">
				<p className="pending-tasks__head__headline">
					Pending tasks
				</p>
				<Link to="/" className="pending-tasks__head__link">See all</Link>
			</div>
			<ul className="pending-tasks__list">
				<li className="pending-tasks__item">
					<p className="pending-tasks__item__name">
						Task Title lorem ipsum set
					</p>
					<p className="pending-tasks__item__date">Deadline: <span>Nov 17</span></p>
					<div className="pending-tasks__item__bottom">
						<div className="pending-tasks__item__status rejected">
							Rejected
						</div>
						<div className="pending-tasks__user">
							<p className="pending-tasks__user__name">Approver's name</p>
							<img src={tempUser} alt="" className="pending-tasks__user__avatar"/>
						</div>
					</div>
				</li>
				<li className="pending-tasks__item">
					<p className="pending-tasks__item__name">
						Task Title lorem ipsum set
					</p>
					<p className="pending-tasks__item__date">Deadline: <span>Nov 17</span></p>
					<div className="pending-tasks__item__bottom">
						<div className="pending-tasks__item__status in-progress">
							In progress
						</div>
						<div className="pending-tasks__user">
							<p className="pending-tasks__user__name">Approver's name</p>
							<img src={tempUser} alt="" className="pending-tasks__user__avatar"/>
						</div>
					</div>
				</li>
				<li className="pending-tasks__item">
					<p className="pending-tasks__item__name">
						Task Title lorem ipsum set
					</p>
					<p className="pending-tasks__item__date">Deadline: <span>Nov 17</span></p>
					<div className="pending-tasks__item__bottom">
						<div className="pending-tasks__item__status approved">
							Approved
						</div>
						<div className="pending-tasks__user">
							<p className="pending-tasks__user__name">Approver's name</p>
							<img src={tempUser} alt="" className="pending-tasks__user__avatar"/>
						</div>
					</div>
				</li>
			</ul>
		</div>
	);
};

const Blog = () => {
	const [activeTab, setActiveTab] = useState('Announcements')

	return (
		<div className="blog">
			<div className="blog__head">
				<p className="blogs__head__headline">
					Blog
				</p>
				<Link to="/" className="blog__head__link">View all</Link>
			</div>
			<div className="blog__actions">
				<div
					className={`blog__actions__item ${activeTab === 'Announcements'? 'active': ''}`}
					onClick={() => setActiveTab('Announcements')}
				>
					Announcements
				</div>
				<div
					className={`blog__actions__item ${activeTab === 'News'? 'active': ''}`}
					onClick={() => setActiveTab('News')}
				>
					News
				</div>
			</div>
			<ul className="blog__list">
				<li className="blog__item">
					<img src={tempNews} alt="" className="blog__img"/>
					<div className="blog__info">
						<p className="blog__info__date">
							In 23 days
						</p>
						<p className="blog__info__descriptions">
							Commodo tristique massa amet urna, egestas maecenas... Varius at ...
						</p>
					</div>
				</li>
				<li className="blog__item">
					<img src={tempNews} alt="" className="blog__img"/>
					<div className="blog__info">
						<p className="blog__info__date">
							In 23 days
						</p>
						<p className="blog__info__descriptions">
							Commodo tristique massa amet urna, egestas maecenas... Varius at ...
						</p>
					</div>
				</li>
				<li className="blog__item">
					<img src={tempNews} alt="" className="blog__img"/>
					<div className="blog__info">
						<p className="blog__info__date">
							In 23 days
						</p>
						<p className="blog__info__descriptions">
							Commodo tristique massa amet urna, egestas maecenas... Varius at ...
						</p>
					</div>
				</li>
				<li className="blog__item">
					<img src={tempNews} alt="" className="blog__img"/>
					<div className="blog__info">
						<p className="blog__info__date">
							In 23 days
						</p>
						<p className="blog__info__descriptions">
							Commodo tristique massa amet urna, egestas maecenas... Varius at ...
						</p>
					</div>
				</li>
				<li className="blog__item">
					<img src={tempNews} alt="" className="blog__img"/>
					<div className="blog__info">
						<p className="blog__info__date">
							In 23 days
						</p>
						<p className="blog__info__descriptions">
							Commodo tristique massa amet urna, egestas maecenas... Varius at ...
						</p>
					</div>
				</li>
				<li className="blog__item">
					<img src={tempNews} alt="" className="blog__img"/>
					<div className="blog__info">
						<p className="blog__info__date">
							In 23 days
						</p>
						<p className="blog__info__descriptions">
							Commodo tristique massa amet urna, egestas maecenas... Varius at ...
						</p>
					</div>
				</li>
				<li className="blog__item">
					<img src={tempNews} alt="" className="blog__img"/>
					<div className="blog__info">
						<p className="blog__info__date">
							In 23 days
						</p>
						<p className="blog__info__descriptions">
							Commodo tristique massa amet urna, egestas maecenas... Varius at ...
						</p>
					</div>
				</li>
				<li className="blog__item">
					<img src={tempNews} alt="" className="blog__img"/>
					<div className="blog__info">
						<p className="blog__info__date">
							In 23 days
						</p>
						<p className="blog__info__descriptions">
							Commodo tristique massa amet urna, egestas maecenas... Varius at ...
						</p>
					</div>
				</li>
				<li className="blog__item">
					<img src={tempNews} alt="" className="blog__img"/>
					<div className="blog__info">
						<p className="blog__info__date">
							In 23 days
						</p>
						<p className="blog__info__descriptions">
							Commodo tristique massa amet urna, egestas maecenas... Varius at ...
						</p>
					</div>
				</li>
				<li className="blog__item">
					<img src={tempNews} alt="" className="blog__img"/>
					<div className="blog__info">
						<p className="blog__info__date">
							In 23 days
						</p>
						<p className="blog__info__descriptions">
							Commodo tristique massa amet urna, egestas maecenas... Varius at ...
						</p>
					</div>
				</li>
			</ul>
		</div>
	);
};