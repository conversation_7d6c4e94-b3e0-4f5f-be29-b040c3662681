import { ITabsFilter } from "../global/global";

export interface IMembersTable {
    activeTab: string,
    isLoading: boolean,
    emptySearch: boolean,
    emptyTable: boolean,
    emptyFilter: boolean,
    filters: {
        searchValue: string,
        sortBy: string,
        sortType: string,
        role: number[],
        department: any,
        jobLocation: any[],
        education: any[],
        dateOfJoining: IMembersDateOfJoiningItem,
        dateOfBirth: {
            from: Date,
            to: Date,
        }
    },
    fixedTab:ITabsFilter,
    tableItems: Array<IMembersTableItem>,
    tabFilter: Array<ITabsFilter>,
    pagination: {
        currentPage: number,
        limit: number,
        totalCount: number,
        afterKeyForNextPage:any,
    },
    filterInfo: {
        roleList:any[],
        tabs: Array<string>
    }
}

export interface IMembersDateOfJoiningItem {
    from: Date,
    to: Date,
    duration: "Last 30 Days" | "Last 90 Days" | "Last 3 months" | "Last 6 months" | "Last 12 months" | "This year" | "Last year" | "All time" | null
}

export interface IMembersTableItem {
    userId: number,
    user_type: "recruiter" | '',
    recruiterId: number,
    companyId: number,
    email: string,
    phone: string,
    dateJoining: Date,
    birthDate: Date,
    jobLocation: {
        id: number,
        city: string,
        state: string,
        st: string
    },
    position: {
        id: number,
        name: string
    },
    name: string,
    avatar: string,
    department: string,
    role: {
        id: number,
        name: string
    }[],
    gender: string,
    degree: string,
    personalEmail: string,
    profileStatus: "Active" | "Pending" | "",
    createdAt: Date,
}