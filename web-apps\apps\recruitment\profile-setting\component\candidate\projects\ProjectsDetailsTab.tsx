import { memo, useEffect, useRef, useState } from 'react';
import ProjectsDetailsTabItem from './ProjectsDetailsTabItem';
import axios from 'axios';
import { getEnv } from "@urecruits/api";
import patchData from "../../../hook/patchData";
import { getConfig } from '@ucrecruits/globalstyle/src/ucrecruits-globalstyle';

const editIc = require('../../../image/icon/edit_ic.svg')
const plusIc = require('../../../image/icon/plus_ic.svg')

const ProjectsDetailsTab = ({ data, setRefetch, setData }) => {
  const token: string = localStorage.getItem('token')
  const [validateStatus, setValidateStatus] = useState(false)
  const [success, setSuccess] = useState(false)
  const [editMode, setEditMode] = useState(false)
  const [displayButton, setDisplayButton] = useState(false)
  const [addedMoreClicked, setAddedMoreClicked] = useState(false)
  const addedMoreEducationButtonRef = useRef(null)
  const submitButtonRef = useRef(null)

  //form state
  const [dataDetails, setDataDetails] = useState([])

  useEffect(() => {
    if (data) {
      setDataDetails(data.projects)
      setAddedMoreClicked(() => false)
      const isEditMode = data.projects.some(item => item?.isResumeParsed);
      setEditMode(editMode ? editMode : isEditMode)
      setDisplayButton(editMode? editMode : isEditMode)
    }
  }, [data])

  const onFormChange = () => {
    setDisplayButton(true)
  }

  const onClearHandler = (e) => {
    e.preventDefault()
    setEditMode(false)
    setDisplayButton(false)
    setDataDetails(data.projects)

    const projects = data.projects.filter(item => !item.isResumeParsed);
    setDataDetails(projects);
    setData(oldData => {
      return {...oldData,projects}
    })
  }


  function setDataOnSuccess(res,itemId) {
    if (res.status === 200 || res.status === 201) {
      setSuccess(true)
      setData(oldData => {
        let projects = [...oldData?.projects];
        oldData.projects = projects?.map(i => i.id === itemId ? { ...res.data } : i)
        return { ...oldData }
      });
    } else {
      setSuccess(false)
    }
  }

  const {API_RECRUITMENT}=getEnv()
  const onFormSubmit = async (e) => {
    e.preventDefault()
    setValidateStatus(false)

    if (validateStatus) {
      setSuccess(true)

      //Need to added post
      for (const itemMain of dataDetails) {
        const item = {...itemMain}
        if(!item?.isResumeParsed) {
          await patchData(`${API_RECRUITMENT}/api/candidate/project`, item).then((res) => {
            setDataOnSuccess(res,item.id);
          })
        } else {
          const itemId = item.id
          delete item.id;
          await axios.post(`${API_RECRUITMENT}/api/candidate/project`, item, getConfig()).then((res) => {
            setDataOnSuccess(res,itemId);
          }).catch(err => {
            console.log(err)
          })
        }
      }

      setTimeout(() => {
        setSuccess(false)
        setEditMode(false)
        setDisplayButton(false)
      }, 2000)
    }
  }

  const onAddEducationHandler = async () => {
    if (editMode) {
      // empty object
      const obg = {
        projectName: '',
        dateStart: null,
        dateEnd: null,
        awardDate: null,
        notes: '',
        candidateId: data.id,
      }
      setValidateStatus(false)
      if (validateStatus && !addedMoreClicked || !dataDetails?.length) {
        setAddedMoreClicked(() => true)
        for (const item of dataDetails) {
          await patchData(`${API_RECRUITMENT}/api/candidate/project`, item).then((res) => {})
        }
        //Need to added post
        await axios.post(`${API_RECRUITMENT}/api/candidate/project`, obg, getConfig()).then((res) => {
          //res.status === 201 ? setDataDetails([...dataDetails, res.data]) : ''
        }).catch(err => {
          console.log(err)
        })
        setRefetch(new Date())
      }
    }
  }

  return (
    <div className="profile__right__inner">
      <div className="profile__head">
        <div className="profile__head__inner">
          <p className="profile__head__title">
            Projects {editMode && (<span> - Editing Mode</span>)}
          </p>
          {
            !editMode && (
              <p className="profile__head__edit" onClick={() => setEditMode(true)}>
                <span>Edit</span>
                <img src={editIc} alt="edit icon"/>
              </p>
            )
          }
        </div>
      </div>
      <form
        className={`profile__form ${editMode ? '' : 'readonly'}`}
        onSubmit={(e) => onFormSubmit(e)}
        onChange={() => onFormChange()}
      >
        {
          dataDetails.map((item, index) => {
            return (
              <ProjectsDetailsTabItem
                dataDetails={dataDetails}
                currentItem={item}
                setDataDetails={setDataDetails}
                editMode={editMode}
                setData={setData}
                key={index}
                validateStatus={validateStatus}
                setValidateStatus={setValidateStatus}
                displayButton={displayButton}
                addedMoreEducationButtonRef={addedMoreEducationButtonRef}
                submitButtonRef={submitButtonRef}
                setRefetch={setRefetch}
                index={index}
                setDisplayButton={setDisplayButton}
              />
            )
          })
        }
        {
          editMode ?
            <div className="profile__form__group">
              <div className={`profile__form__more`}
                   id="educationsMoreButton"
                   ref={addedMoreEducationButtonRef}
                   onClick={() => onAddEducationHandler()}
              >
                <img src={plusIc} alt="plust icon" className="profile__form__more--icon"/>
                <p className="profile__form__more--text">Add one more project</p>
              </div>
            </div>
            : null
        }
        {
          displayButton && editMode ?
            <div className="profile__form__group">
              <div className="profile__form__item buttons">
                <button className="profile__form__cancel button--empty" onClick={(e) => onClearHandler(e)}>Cancel
                </button>
                <button className="profile__form__submit button--filled" type="submit" ref={submitButtonRef}>Save
                </button>
              </div>
              {success ? <p className="success-message">All changes made</p> : null}
            </div>
            : null
        }
      </form>
    </div>
  )
}

export default memo(ProjectsDetailsTab)