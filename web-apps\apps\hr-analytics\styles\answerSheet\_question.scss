@use "../config" as *;
@use "../mixins" as *;
.domain-question {
    width: 100%;
    color: #2A2C33;
    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center; 
        font-size: 16px;
        font-weight: 600;
        margin: 12px 40px;
    }

    &__container{
        width: 100%;
        border: 1px solid #DFE2E6;
        border-radius: 12px;
        padding: 2rem;
        @media (max-width: 768px) {
            border: none;
            border: 1px solid #DFE2E6;
            border-radius: 12px;
            padding: 1rem;
        }

        .body{
            margin-top: 1.5rem;
            margin-left: 1rem;
            font-size: 16px;
        }
    }
}

.text{
    &__textarea{
    border: 1px solid black;
    position: relative;
    overflow: auto;
    max-width: 100%;
    width: 100%;
    min-width: 100%;
    height: 100px;
    min-height: 50px;
    max-height: 200px;
    border-radius: 7px;
    padding: 1rem;
    }
}
.multiple{
    &__options{
        display: flex;
        flex-direction: row;
        margin-top: 10px;
        padding: 1px 0px 1px 4px;
        border-radius: 4px;
    }

    .selected{
       background: #ACD8D14D;
    }

    .non-selected{
       background: #FE467226;
    }
}

.single{
    &__options{
        margin-top: 10px;
        padding: 1px 0px 1px 4px;
        border-radius: 4px;
    }

    .selected{
       background: #ACD8D14D;
      }
    

    .non-selected{
       background: #FE467226;
    }
      
}

.feedback-container{
    display: flex;
    flex-direction: row;
    justify-content: space-between;

   
    &__left{
        display: flex;
        flex-direction: row;
        font-size: 14px;
        
        .show-feedback{
            display:flex;
            flex-direction:row;
            cursor: pointer;
            word-wrap: break-word;
            margin-left: 1rem;
            
            &__data{
                max-width: 70%;
            }

            img{
                height: 20px;
                margin: 3px;
            }

            &__action{
                display:flex;
                flex-direction:row;
                justify-content: space-between;
            }
    
        }

        .add-feedback{
            display:flex;
            flex-direction:row;
            cursor: pointer;
            
            img{
                height: 50px;
            }
    
           p{
            min-width: max-content;
            padding: 15px 10px;
           } 
        }

    }

    &__right {
        display: flex;
        flex-direction: row;
        width: 100%;
        gap: 10px;
        justify-content: flex-end;

        .action {
            display: flex;
            flex-direction: row;
            max-width: 65%;

            img {
                height: 32px;
                width: 32px;
                padding: 6px;
                margin-left: 3px;
                border: 1px solid black;
            }
        }

        .marks {
            width: 100px;
            display: flex;
            justify-content: flex-end;
        }

        .marks input {
            max-width: 30px;
            max-height: 20px; 
            margin-top: 4px;
        }


        @media (max-width: 1000px) {
            flex-direction: column-reverse;
            justify-content: center;
            
            .marks{
                justify-content: center; 
            }
        }
    }

    @media (max-width: 1000px) {
        flex-direction: column;
        &__right {
            max-width: 100%;
            align-items: center;
        }
        &__left {
            margin-top: 1rem;
            max-width: 100%;
            align-items: center;
        }

        .show-feedback{
            justify-content: center;
        }
    }

}
.question-wrapper{
    display: flex;
    flex-direction: row;

    &__question{
        flex:7;
    }

    &__action{
        flex:2;
        color: #099c73;
        font-weight: bold;
    }

}
