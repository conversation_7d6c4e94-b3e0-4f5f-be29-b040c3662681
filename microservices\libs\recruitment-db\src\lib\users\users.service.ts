import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { User } from "./users.model";
import * as bcrypt from "bcrypt";
import { CreateUserDto } from "./dto/create-user.dto";
import { Auth0Service } from "@microservices/auth";
import { UpdateUserDto } from "./dto/update-user.dto";
import { ChangePasswordDto } from "./dto/change-password.dto";
import { Role } from "../roles/roles.model";
import { IAMUsersDto } from "../companies/dto/IAMUsers.dto";
import { Recruiter } from "../recruiters/recruiters.model";
import { Op } from "sequelize";
import { UserRoles } from "../roles/user-roles.model";
import { ChangeRolesDto } from "./dto/change-roles.dto";
import { RecruiterPositions } from "../recruiter-positions/recruiter-positions.model";
import { EmailService } from "@microservices/email";
import { Company } from "../companies/companies.model";
import { SubscriptionService } from "../subscription/subscription.service";
@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User) private userRepository: typeof User,
    private auth0Service: Auth0Service,
    private emailService: EmailService,
  ) {}

  async createUser(dto: CreateUserDto) {
    try{
      const password = await bcrypt.hash(dto.password, 10);
      const user = await this.userRepository.create({ ...dto, password });
      await this.emailService.sendRecruitment(
      "welcome.html",
      { userId: user?.id },
      user.email
    );
    return this.getUserByUserId(user.id);
    }catch(error){
      console.log(error);
      throw new HttpException("Failed to create user", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async updateUser(dto: UpdateUserDto) {
    let user = await this.getUserByAuthId(dto.authId);
    Object.assign(user, {
      ...dto,
      email: user.email,
      password: user.password,
      authId: user.authId,
      companyId: user.companyId,
    });
    await this.auth0Service.updateUser(user);
    return user.save();
  }

  async isExistUser(email: string) {
    const user = await this.userRepository.findOne({
      where: { email },
      attributes: ["id", "email"],
    });
    await this.auth0Service.isExistUser(email);
    if (user) {
      throw new HttpException("This email is exist!", HttpStatus.CONFLICT);
    }
  }

  async changePassword(dto: ChangePasswordDto) {
    const user = await this.getUserByAuthId(dto.authId);
    const isMatch = await bcrypt.compare(dto.currentPassword, user.password);
    if (isMatch) {
      user.password = await bcrypt.hash(dto.newPassword, 10);
      await this.auth0Service.changePassword(dto.newPassword, dto.authId);
      return user.save();
    }
    throw new HttpException("Current password is wrong!", HttpStatus.FORBIDDEN);
  }

  async getUserByUserId(userId: number) {
    return await this.userRepository.findOne({ where: { id: userId },include:[Company,Role]});
  }

  async getUserByAuthId(authId: string) {
    return await this.userRepository.findOne({ where: { authId } });
  }

  async getUserByEmail(email:string){
    return await this.userRepository.findOne({where:{email}});
  }

  async countUserOnRole(companyId: number, roleId: number) {
    return await this.userRepository.findAndCountAll({
      distinct: true,
      where: { companyId },
      include: [
        {
          model: Role,
          where: { id: roleId },
        },
      ],
    });
  }

  async AIMUsers(companyId: number, dto: IAMUsersDto) {
    let order: any[];
    let where = {};
    if (dto.sortBy.toLocaleLowerCase() === "positionId") {
      order = [
        [{ model: Recruiter, as: "recruiter" }, "positionId", dto.sortType],
      ];
    } else if (dto.sortBy && dto.sortType) {
      order = [
        [dto.sortBy.toLocaleLowerCase(), dto.sortType.toLocaleLowerCase()],
      ];
    } else {
      order = [["id", "DESC"]];
    }
    const reg = new RegExp("^[0-9]*$");
    if (dto.search && reg.test(dto.search)) {
      where = {
        [Op.or]: [
          {
            id: {
              [Op.eq]: dto.search,
            },
          },
          {
            firstname: {
              [Op.iLike]: `%${dto.search}%`,
            },
          },
          {
            lastname: {
              [Op.iLike]: `%${dto.search}%`,
            },
          },
        ],
      };
    } else if (dto.search) {
      where = {
        [Op.or]: [
          {
            firstname: {
              [Op.iLike]: `%${dto.search}%`,
            },
          },
          {
            lastname: {
              [Op.iLike]: `%${dto.search}%`,
            },
          },
        ],
      };
    }
    const include = [
      {
        model: UserRoles,
        where: {
          roleId: dto.roleId,
        },
        required: true,
      },
      {
        model: Role,
        attributes: ["label", "value", "id"],
      },
      {
        model: Recruiter,
        where: { companyId },
        required: false,
        include: [
          {
            model: RecruiterPositions,
          },
        ],
      },
    ];
    const limit = dto.limit;
    const offset = dto.offset;
    const args = {
      include,
      attributes: { exclude: ["password"] },
      distinct: true,
      where,
      offset,
      order,
      limit,
    };

    return await this.userRepository.findAndCountAll(args);
  }

  async getLeadUsers(companyId: number, userId: number,query:any=null) {
    let id:any='';
    if(query.excludedId){
      id = { [Op.ne]: +query.excludedId }
    }
    
    return await this.userRepository.findAll({
      attributes: ["id", "email", "firstname", "middlename", "lastname"],
      where: {
        [Op.and]: {
          companyId: { [Op.eq]: companyId },
          ...(id && { id }),
        },
      },
      include: [
        {
          required: false,
          model: Role,
          attributes: ["label", "value", "id"],
          where: {
            value: {
              [Op.or]: [process.env.AUTH0_COMPANY_ADMIN_ROLE], // Get from the env
            },
          },
        },
        {
          required: false,
          model: Recruiter,
          attributes: ["id"],
        },
      ],
      order: [["id", "DESC"]],
    });
  }

  async getWorkFlowReviewers(companyId: number) {
    return await this.userRepository.findAll({
      attributes: ["id", "email", "firstname", "middlename", "lastname"],
      where: {
        companyId,
      },
      include: [
        {
          model: Role,
          attributes: ["label", "value", "id"],
          where: {
            value: {
              [Op.or]: [
              process.env.AUTH0_COMPANY_ADMIN_ROLE, // Get from the env
              process.env.AUTH0_COMPANY_OWNER_ROLE, // Get from the env
              ],
            },
          },
        },
      ],
      order: [["id", "DESC"]],
    });
  }

  async deleteMember(userId: number, companyId: number) {
    const user = await this.userRepository.findOne({
      where: {
        id: userId,
        companyId: companyId,
      },
    });
    if (user) {
      if (user) {
        const status = await this.auth0Service.deleteUser(user.authId);
        if (status) {
          await user.destroy();
          return "You have successfully deleted the user";
        } else {
          throw new HttpException("Not Found", HttpStatus.NOT_FOUND);
        }
      } else {
        throw new HttpException("Not Found", HttpStatus.NOT_FOUND);
      }
    }
  }

  async changeCompanyMemberRoles(dto: ChangeRolesDto, companyId: number) {
    const user = await this.userRepository.findOne({
      where: {
        authId: dto.authId,
        companyId,
      },
      include: ["roles"],
    });
    if (!user) {
      throw new HttpException("USER Not Found", HttpStatus.NOT_FOUND);
    }
    const removeRolesAuth0 = [];
    const addRolesAuth0 = [];
    const removeRoles = [];
    const addRoles = [];
    user.roles?.forEach((item) => {
      if (item.value != process.env.AUTH0_COMPANY_OWNER_ROLE) { // Get from the env
        const existRole = dto.roles.find((x) => x.value === item.value);
        if (!existRole) {
          removeRolesAuth0.push(item.value);
          removeRoles.push(item.id);
        }
      }
    });
    for (const item of dto.roles) {
      if (item.value != process.env.AUTH0_COMPANY_OWNER_ROLE) { // Get from the env
        const existRole = user.roles.find((x) => x.value === item.value);
        if (!existRole) {
          addRolesAuth0.push(item.value);
          addRoles.push(item.id);
        }
      }
    }
    await this.auth0Service.assignRoles(dto.authId, addRolesAuth0);
    await this.auth0Service.removeRoles(dto.authId, removeRolesAuth0);
    await user.$add("roles", addRoles);
    await user.$remove("roles", removeRoles);
    return await this.userRepository.findOne({
      where: {
        authId: dto.authId,
        companyId,
      },
      include: ["roles"],
    });
  }

  async getCompanyUser(id: number, companyId: number) {
    const user = await this.userRepository.findOne({
      where: { id, companyId },
      attributes: ["id", "companyId", "authId"],
    });
    if (!user) {
      throw new HttpException("User not found", HttpStatus.NOT_FOUND);
    }
    return user;
  }

  async deleteAllUsers() {
    const users = await this.userRepository.findAll({
      attributes: ["id", "authId"],
    });
    for (const item of users) {
      const status = await this.auth0Service.deleteUser(item.authId);
      if (status) {
        await this.userRepository.destroy({ where: { id: item.id } });
      }
    }
    return "Done!";
  }
}
