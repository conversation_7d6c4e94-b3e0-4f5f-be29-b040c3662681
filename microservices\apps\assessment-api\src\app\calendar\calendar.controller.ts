import { <PERSON>, <PERSON>s, Get, Post, Delete, Patch, Body, Param, UseGuards, HttpStatus, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse} from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { AuthUser, Permissions, PermissionsGuard } from '@microservices/auth';
import { CalendarService, CreateEventDto, DeleteEventDto, UpdateEventDto, UpdateEventStatusDto } from '@microservices/db';

@Controller('calendar')
export class CalendarController {
  constructor(private readonly calendarService: CalendarService) {}

  @ApiOperation({ summary: 'Create event' })
  @ApiResponse({ status: 201 })
  @Post('/create-event')
  async createEvent(@Res() response, @Body() createEventDto) {
    const event = await this.calendarService.createEvent(createEventDto);
    return response.status(HttpStatus.CREATED).json(event);
  }

  @ApiOperation({ summary: 'Get all events' })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth("access-token")
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get('/all-event')
  async getAppEventById(@Res() response,@AuthUser() user,@Query() date) {
    const event = await this.calendarService.getAllEventByUser(user,date);
    return response.status(HttpStatus.OK).json(event);
  }
  
  @ApiOperation({ summary: 'Get all events' })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth("access-token")
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get('/candidate/all-event')
  async getCandidateEvents(@Res() response,@AuthUser() user,@Query() date) {
    const event = await this.calendarService.getEvents(user,date);
    return response.status(HttpStatus.OK).json(event);
  }

  @ApiOperation({ summary: 'Get event by room Id' })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth("access-token")
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get('/room-event/:id')
  async getEventByRoomId(@Res() response, @Param('id') roomId: string, @AuthUser() user) {
    const event = await this.calendarService.getEventByRoomId(roomId, user);

    return response.status(HttpStatus.OK).json(event);
  }

  @ApiOperation({ summary: 'Get event by eventId' })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth("access-token")
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get('/event/:id')
  async getEventById(@Res() response, @Param('id') eventId: number, @AuthUser() user) {
    const event = await this.calendarService.getEventById(eventId, user);

    return response.status(HttpStatus.OK).json(event);
  }

  @ApiOperation({ summary: 'Get event by job Id' })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth("access-token")
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get('/get')
  async getEventByJobId(@Res() response, @Query() data: any, @AuthUser() user) {
    const event = await this.calendarService.getEventByJobId(data, user);
    return response.status(HttpStatus.OK).json(event);
  }

  @ApiOperation({ summary: 'Get all user\'s events by userId' })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth("access-token")
  // @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get('/user-events/:id')
  async getEventsByUserId(@Res() response, @Param('id') userId: number) {
    const userEvents = await this.calendarService.getEventsByUserId(userId);

    return response.status(HttpStatus.OK).json(userEvents);
  }

  @ApiOperation({ summary: 'Delete event by eventId' })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth("access-token")
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Delete('/event')
  @Permissions('recruiter')
  async deleteEventById(@Res() response, @Body() deleteEventDto: DeleteEventDto) {
    const event = await this.calendarService.deleteEventById(deleteEventDto);
    return response.status(HttpStatus.OK).json(event);
  }

  @ApiOperation({ summary: 'Update event' })
  @ApiResponse({ status: 200 }) 
  @ApiBearerAuth("access-token")
  // @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch('/event')
  async updateEvent(@Res() response, @Body() updateEventDto: UpdateEventDto) {
    const event = await this.calendarService.updateEvent(updateEventDto);
    return response.status(HttpStatus.OK).json(event);
  }

  @ApiOperation({ summary: 'Update event By room Id' })
  @ApiResponse({ status: 200 }) 
  @ApiBearerAuth("access-token")
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch('/event/:roomId')
  async updateEventStatusByRoomId(@Res() response,@Param('roomId') roomId:string,  @Body() body:UpdateEventStatusDto) {
    const event = await this.calendarService.updateEventStatusByRoomId(roomId, body.status);
    return response.status(HttpStatus.OK).json(event);
  }


  @ApiOperation({ summary: 'Get User Details by eventId' })
  // @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200 }) 
  // @ApiBearerAuth("access-token")
  @Get('/get-users/:eventId')
  async getAllUsersByEvent(@Param('eventId') eventId:number ) {
    return await this.calendarService.getAllUsersByEvent(eventId);
  }
}
