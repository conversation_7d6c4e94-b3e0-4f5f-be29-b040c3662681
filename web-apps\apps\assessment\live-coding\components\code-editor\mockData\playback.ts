export const someJSCodeExample = `
  // The source (has been changed) is https://github.com/facebook/react/issues/5465#issuecomment-157888325

  const CANCELATION_MESSAGE = {
    type: 'cancelation',
    msg: 'operation is manually canceled',
  };

  function makeCancelable(promise) {
    let hasCanceled_ = false;

    const wrappedPromise = new Promise((resolve, reject) => {
      promise.then(val => hasCanceled_ ? reject(CANCELATION_MESSAGE) : resolve(val));
      promise.catch(reject);
    });

    return (wrappedPromise.cancel = () => (hasCanceled_ = true), wrappedPromise);
  }

  export default makeCancelable;
`;

export const exampleArray2 = [
  {

    "timestamp": 1643017428294,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "initial",
    "data": {
      "userId": "test",
    }
  },
  {

    "timestamp": 1643017431551,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 369,
      "text": "l"
    }
  },
  {

    "timestamp": 1643017431642,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 370,
      "text": "e"
    }
  },
  {

    "timestamp": 1643017431851,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 371,
      "text": "t"
    }
  },
  {

    "timestamp": 1643017431922,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 372,
      "text": " "
    }
  },
  {

    "timestamp": 1643017432130,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet ",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 373,
      "selection": null
    }
  },
  {

    "timestamp": 1643017432344,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 373,
      "text": "x"
    }
  },
  {

    "timestamp": 1643017432457,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 374,
      "text": " "
    }
  },
  {

    "timestamp": 1643017432667,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x ",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 375,
      "selection": null
    }
  },
  {

    "timestamp": 1643017432839,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 375,
      "text": "="
    }
  },
  {

    "timestamp": 1643017432959,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 376,
      "text": " "
    }
  },
  {

    "timestamp": 1643017433145,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 377,
      "text": "2"
    }
  },
  {

    "timestamp": 1643017433360,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 378,
      "selection": null
    }
  },
  {

    "timestamp": 1643017434062,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 378,
      "text": "l"
    }
  },
  {

    "timestamp": 1643017434274,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2l",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 379,
      "selection": null
    }
  },
  {

    "timestamp": 1643017434638,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "delete",
    "data": {
      "userId": "test",
      "index": 378,
      "length": 1
    }
  },
  {

    "timestamp": 1643017434847,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 378,
      "selection": null
    }
  },
  {

    "timestamp": 1643017434871,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 378,
      "text": ";"
    }
  },
  {

    "timestamp": 1643017435084,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 379,
      "selection": null
    }
  },
  {

    "timestamp": 1643017435250,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 379,
      "text": "\n"
    }
  },
  {

    "timestamp": 1643017435462,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\n",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 380,
      "selection": null
    }
  },
  {

    "timestamp": 1643017436166,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 380,
      "text": "l"
    }
  },
  {

    "timestamp": 1643017436250,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 381,
      "text": "e"
    }
  },
  {

    "timestamp": 1643017436425,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 382,
      "text": "t"
    }
  },
  {

    "timestamp": 1643017436488,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 383,
      "text": " "
    }
  },
  {

    "timestamp": 1643017436695,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet ",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 384,
      "selection": null
    }
  },
  {

    "timestamp": 1643017437427,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 384,
      "text": "y"
    }
  },
  {

    "timestamp": 1643017437539,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 385,
      "text": " "
    }
  },
  {

    "timestamp": 1643017437747,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y ",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 386,
      "selection": null
    }
  },
  {

    "timestamp": 1643017437760,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 386,
      "text": "="
    }
  },
  {

    "timestamp": 1643017437935,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 387,
      "text": " "
    }
  },
  {

    "timestamp": 1643017438113,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 388,
      "text": "3"
    }
  },
  {

    "timestamp": 1643017438324,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 389,
      "selection": null
    }
  },
  {

    "timestamp": 1643017438549,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 389,
      "text": ";"
    }
  },
  {

    "timestamp": 1643017438757,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 390,
      "selection": null
    }
  },
  {

    "timestamp": 1643017439204,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 390,
      "text": "\n"
    }
  },
  {

    "timestamp": 1643017439421,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 391,
      "selection": null
    }
  },
  {

    "timestamp": 1643017439622,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 391,
      "text": "\n"
    }
  },
  {

    "timestamp": 1643017439833,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\n",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 392,
      "selection": null
    }
  },
  {

    "timestamp": 1643017440949,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 392,
      "text": "c"
    }
  },
  {

    "timestamp": 1643017441092,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 393,
      "text": "o"
    }
  },
  {

    "timestamp": 1643017441302,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 394,
      "text": "n"
    }
  },
  {

    "timestamp": 1643017441377,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 395,
      "text": "s"
    }
  },
  {

    "timestamp": 1643017441452,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 396,
      "text": "t"
    }
  },
  {

    "timestamp": 1643017441538,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 397,
      "text": " "
    }
  },
  {

    "timestamp": 1643017441744,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst ",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 398,
      "selection": null
    }
  },
  {

    "timestamp": 1643017442166,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 398,
      "text": "f"
    }
  },
  {

    "timestamp": 1643017442367,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 399,
      "text": "i"
    }
  },
  {

    "timestamp": 1643017442581,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst fi",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 400,
      "selection": null
    }
  },
  {

    "timestamp": 1643017442619,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 400,
      "text": "n"
    }
  },
  {

    "timestamp": 1643017442825,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst fin",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 401,
      "selection": null
    }
  },
  {

    "timestamp": 1643017445064,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst fin",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 380,
      "selection": {
        "start": 380,
        "end": 401
      }
    }
  },
  {

    "timestamp": 1643017445309,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst fin",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 369,
      "selection": {
        "start": 369,
        "end": 401
      }
    }
  },
  {

    "timestamp": 1643017446460,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "delete",
    "data": {
      "userId": "test",
      "index": 369,
      "length": 32
    }
  },
  {

    "timestamp": 1643017446681,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 369,
      "selection": null
    }
  },
  {

    "timestamp": 1643017447453,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 369,
      "text": "l"
    }
  },
  {

    "timestamp": 1643017447529,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 370,
      "text": "e"
    }
  },
  {

    "timestamp": 1643017447638,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 371,
      "text": "t"
    }
  },
  {

    "timestamp": 1643017447705,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 372,
      "text": " "
    }
  },
  {

    "timestamp": 1643017447909,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet ",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 373,
      "selection": null
    }
  },
  {

    "timestamp": 1643017448167,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 373,
      "text": "x"
    }
  },
  {

    "timestamp": 1643017448273,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 374,
      "text": " "
    }
  },
  {

    "timestamp": 1643017448484,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x ",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 375,
      "selection": null
    }
  },
  {

    "timestamp": 1643017448658,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 375,
      "text": "="
    }
  },
  {

    "timestamp": 1643017448747,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 376,
      "text": " "
    }
  },
  {

    "timestamp": 1643017448947,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 377,
      "text": "2"
    }
  },
  {

    "timestamp": 1643017449155,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 378,
      "selection": null
    }
  },
  {

    "timestamp": 1643017449388,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 378,
      "text": ";"
    }
  },
  {

    "timestamp": 1643017449605,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 379,
      "selection": null
    }
  },
  {

    "timestamp": 1643017449854,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 379,
      "text": "\n"
    }
  },
  {

    "timestamp": 1643017450067,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\n",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 380,
      "selection": null
    }
  },
  {

    "timestamp": 1643017450507,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 380,
      "text": "l"
    }
  },
  {

    "timestamp": 1643017450569,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 381,
      "text": "e"
    }
  },
  {

    "timestamp": 1643017450657,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 382,
      "text": "t"
    }
  },
  {

    "timestamp": 1643017450736,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 383,
      "text": " "
    }
  },
  {

    "timestamp": 1643017450940,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet ",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 384,
      "selection": null
    }
  },
  {

    "timestamp": 1643017451786,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 384,
      "text": "y"
    }
  },
  {

    "timestamp": 1643017451909,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 385,
      "text": " "
    }
  },
  {

    "timestamp": 1643017452116,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y ",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 386,
      "selection": null
    }
  },
  {

    "timestamp": 1643017452152,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 386,
      "text": "="
    }
  },
  {

    "timestamp": 1643017452310,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 387,
      "text": " "
    }
  },
  {

    "timestamp": 1643017452520,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 388,
      "text": "3"
    }
  },
  {

    "timestamp": 1643017452726,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 389,
      "selection": null
    }
  },
  {

    "timestamp": 1643017453069,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 389,
      "text": ";"
    }
  },
  {

    "timestamp": 1643017453281,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 390,
      "selection": null
    }
  },
  {

    "timestamp": 1643017453426,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 390,
      "text": "\n"
    }
  },
  {

    "timestamp": 1643017453635,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 391,
      "selection": null
    }
  },
  {

    "timestamp": 1643017454163,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 391,
      "text": "\n"
    }
  },
  {

    "timestamp": 1643017454373,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\n",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 392,
      "selection": null
    }
  },
  {

    "timestamp": 1643017454473,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 392,
      "text": "c"
    }
  },
  {

    "timestamp": 1643017454615,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 393,
      "text": "o"
    }
  },
  {

    "timestamp": 1643017454821,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nco",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 394,
      "selection": null
    }
  },
  {

    "timestamp": 1643017454834,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 394,
      "text": "n"
    }
  },
  {

    "timestamp": 1643017454903,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 395,
      "text": "s"
    }
  },
  {

    "timestamp": 1643017454985,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 396,
      "text": "t"
    }
  },
  {

    "timestamp": 1643017455058,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 397,
      "text": " "
    }
  },
  {

    "timestamp": 1643017455264,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst ",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 398,
      "selection": null
    }
  },
  {

    "timestamp": 1643017455906,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 398,
      "text": "f"
    }
  },
  {

    "timestamp": 1643017456117,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst f",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 399,
      "selection": null
    }
  },
  {

    "timestamp": 1643017456271,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 399,
      "text": "u"
    }
  },
  {

    "timestamp": 1643017456475,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst fu",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 400,
      "selection": null
    }
  },
  {

    "timestamp": 1643017456494,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 400,
      "text": "n"
    }
  },
  {

    "timestamp": 1643017456640,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 401,
      "text": "c"
    }
  },
  {

    "timestamp": 1643017456845,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst func",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 402,
      "selection": null
    }
  },
  {

    "timestamp": 1643017457880,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "delete",
    "data": {
      "userId": "test",
      "index": 401,
      "length": 1
    }
  },
  {

    "timestamp": 1643017458064,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "delete",
    "data": {
      "userId": "test",
      "index": 400,
      "length": 1
    }
  },
  {

    "timestamp": 1643017458238,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "delete",
    "data": {
      "userId": "test",
      "index": 399,
      "length": 1
    }
  },
  {

    "timestamp": 1643017458449,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst f",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 399,
      "selection": null
    }
  },
  {

    "timestamp": 1643017458563,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "delete",
    "data": {
      "userId": "test",
      "index": 398,
      "length": 1
    }
  },
  {

    "timestamp": 1643017458744,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 398,
      "text": "g"
    }
  },
  {

    "timestamp": 1643017458806,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 399,
      "text": "e"
    }
  },
  {

    "timestamp": 1643017459015,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 400,
      "text": "t"
    }
  },
  {

    "timestamp": 1643017459221,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst get",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 401,
      "selection": null
    }
  },
  {

    "timestamp": 1643017459661,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 401,
      "text": "V"
    }
  },
  {

    "timestamp": 1643017459809,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 402,
      "text": "a"
    }
  },
  {

    "timestamp": 1643017459986,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 403,
      "text": "l"
    }
  },
  {

    "timestamp": 1643017460197,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getVal",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 404,
      "selection": null
    }
  },
  {

    "timestamp": 1643017460224,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 404,
      "text": "u"
    }
  },
  {

    "timestamp": 1643017460302,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 405,
      "text": "e"
    }
  },
  {

    "timestamp": 1643017460510,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 406,
      "selection": null
    }
  },
  {

    "timestamp": 1643017460536,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 406,
      "text": " "
    }
  },
  {

    "timestamp": 1643017460752,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue ",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 407,
      "selection": null
    }
  },
  {

    "timestamp": 1643017461166,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 407,
      "text": "="
    }
  },
  {

    "timestamp": 1643017461274,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 408,
      "text": " "
    }
  },
  {

    "timestamp": 1643017461482,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 409,
      "selection": null
    }
  },
  {

    "timestamp": 1643017461695,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 409,
      "text": "()"
    }
  },
  {

    "timestamp": 1643017461903,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ()",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 410,
      "selection": null
    }
  },
  {

    "timestamp": 1643017461966,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 410,
      "text": " "
    }
  },
  {

    "timestamp": 1643017462173,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( )",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 411,
      "selection": null
    }
  },
  {

    "timestamp": 1643017462239,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 411,
      "text": "x"
    }
  },
  {

    "timestamp": 1643017462439,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 412,
      "text": " "
    }
  },
  {

    "timestamp": 1643017462646,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x )",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 413,
      "selection": null
    }
  },
  {

    "timestamp": 1643017463258,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "delete",
    "data": {
      "userId": "test",
      "index": 412,
      "length": 1
    }
  },
  {

    "timestamp": 1643017463468,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x)",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 412,
      "selection": null
    }
  },
  {

    "timestamp": 1643017463589,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 412,
      "text": ","
    }
  },
  {

    "timestamp": 1643017463761,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 413,
      "text": " "
    }
  },
  {

    "timestamp": 1643017464036,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x, )",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 414,
      "selection": null
    }
  },
  {

    "timestamp": 1643017464256,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 414,
      "text": "y"
    }
  },
  {

    "timestamp": 1643017464499,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x, y)",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 415,
      "selection": null
    }
  },
  {

    "timestamp": 1643017465002,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 415,
      "text": " "
    }
  },
  {

    "timestamp": 1643017465235,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x, y )",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 416,
      "selection": null
    }
  },
  {

    "timestamp": 1643017466120,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 417,
      "text": " "
    }
  },
  {

    "timestamp": 1643017466333,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x, y ) ",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 418,
      "selection": null
    }
  },
  {

    "timestamp": 1643017466379,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 418,
      "text": "="
    }
  },
  {

    "timestamp": 1643017466585,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x, y ) =",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 419,
      "selection": null
    }
  },
  {

    "timestamp": 1643017466803,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 419,
      "text": ">"
    }
  },
  {

    "timestamp": 1643017466914,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 420,
      "text": " "
    }
  },
  {

    "timestamp": 1643017467120,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x, y ) => ",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 421,
      "selection": null
    }
  },
  {

    "timestamp": 1643017467319,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 421,
      "text": "{}"
    }
  },
  {

    "timestamp": 1643017467533,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x, y ) => {}",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 422,
      "selection": null
    }
  },
  {

    "timestamp": 1643017468051,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 422,
      "text": "\n  \n"
    }
  },
  {

    "timestamp": 1643017468265,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x, y ) => {\n  \n}",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 425,
      "selection": null
    }
  },
  {

    "timestamp": 1643017468406,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 425,
      "text": "r"
    }
  },
  {

    "timestamp": 1643017468539,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 426,
      "text": "e"
    }
  },
  {

    "timestamp": 1643017468623,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 427,
      "text": "t"
    }
  },
  {

    "timestamp": 1643017468830,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x, y ) => {\n  ret\n}",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 428,
      "selection": null
    }
  },
  {

    "timestamp": 1643017469181,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 428,
      "text": "u"
    }
  },
  {

    "timestamp": 1643017469316,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 429,
      "text": "r"
    }
  },
  {

    "timestamp": 1643017469511,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 430,
      "text": "n"
    }
  },
  {

    "timestamp": 1643017469632,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 431,
      "text": " "
    }
  },
  {

    "timestamp": 1643017469839,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x, y ) => {\n  return \n}",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 432,
      "selection": null
    }
  },
  {

    "timestamp": 1643017470264,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 432,
      "text": "x"
    }
  },
  {

    "timestamp": 1643017470389,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 433,
      "text": " "
    }
  },
  {

    "timestamp": 1643017470595,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x, y ) => {\n  return x \n}",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 434,
      "selection": null
    }
  },
  {

    "timestamp": 1643017470943,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 434,
      "text": "+"
    }
  },
  {

    "timestamp": 1643017471016,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 435,
      "text": " "
    }
  },
  {

    "timestamp": 1643017471227,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x, y ) => {\n  return x + \n}",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 436,
      "selection": null
    }
  },
  {

    "timestamp": 1643017472141,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 436,
      "text": "y"
    }
  },
  {

    "timestamp": 1643017472353,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x, y ) => {\n  return x + y\n}",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 437,
      "selection": null
    }
  },
  {

    "timestamp": 1643017475172,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x, y ) => {\n  return x + y\n}",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 69,
      "selection": {
        "start": 69,
        "end": 368
      }
    }
  },
  {

    "timestamp": 1643017476841,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "delete",
    "data": {
      "userId": "test",
      "index": 69,
      "length": 299
    }
  },
  {

    "timestamp": 1643017477068,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x, y ) => {\n  return x + y\n}",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 69,
      "selection": null
    }
  },
  {

    "timestamp": 1643017477808,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\n",
    "actionType": "insert",
    "data": {
      "userId": "test",
      "index": 69,
      "text": "  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }"
    }
  },
  {

    "timestamp": 1643017478008,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x, y ) => {\n  return x + y\n}",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 69,
      "selection": {
        "start": 69,
        "end": 368
      }
    }
  },
  {

    "timestamp": 1643017479008,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x, y ) => {\n  return x + y\n}",
    "actionType": "selection",
    "data": {
      "userId": "test",
      "cursorPosition": 126,
      "selection": null
    }
  },
  {

    "timestamp": 1643017479040,
    "editorState": "var observableProto;\n/**\n * Represents a push-style collection.\n */\n\n  function Observable() {\n    if (Rx.config.longStackSupport && hasStacks) {\n      var oldSubscribe = this._subscribe;\n      var e = tryCatch(thrower)(new Error()).e;\n      this.stack = e.stack.substring(e.stack.indexOf('t') + 1);\n      this._subscribe = makeSubscribe(this, oldSubscribe);\n    }\n  }\nlet x = 2;\nlet y = 3;\n\nconst getValue = ( x, y ) => {\n  return x + y\n}",
    "actionType": "dispose",
    'data': {
      "userId": 'test'
    }
  }
]