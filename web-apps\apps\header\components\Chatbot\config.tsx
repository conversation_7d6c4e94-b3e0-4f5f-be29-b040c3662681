import IConfig from 'react-chatbot-kit/build/src/interfaces/IConfig';
import ChatBotImg from '../../image/icon/UR_Agent.svg';

// Session Handling
const SESSION_EXPIRY_TIME = 5 * 60 * 1000;
const SESSION_KEY = 'chatbot_session';

interface SessionData {
  id: string;
  expiryTime: number;
}

function isSessionValid(sessionData: SessionData | null): boolean {
  if (!sessionData) return false;
  return Date.now() < sessionData.expiryTime;
}

function getSessionId(): string {
  try {
    const sessionDataStr = sessionStorage.getItem(SESSION_KEY);
    const sessionData: SessionData | null = sessionDataStr ? JSON.parse(sessionDataStr) : null;

    if (isSessionValid(sessionData)) {
      const updatedSessionData: SessionData = {
        id: sessionData!.id,
        expiryTime: Date.now() + SESSION_EXPIRY_TIME,
      };
      sessionStorage.setItem(SESSION_KEY, JSON.stringify(updatedSessionData));
      return sessionData!.id;
    }

    const newSessionData: SessionData = {
      id: crypto.randomUUID(),
      expiryTime: Date.now() + SESSION_EXPIRY_TIME,
    };
    sessionStorage.setItem(SESSION_KEY, JSON.stringify(newSessionData));
    return newSessionData.id;
  } catch (error) {
    console.error('Error managing session:', error);
    return crypto.randomUUID();
  }
}

function clearExpiredSession(): void {
  const sessionDataStr = sessionStorage.getItem(SESSION_KEY);
  if (!sessionDataStr) return;

  try {
    const sessionData: SessionData = JSON.parse(sessionDataStr);
    if (!isSessionValid(sessionData)) {
      sessionStorage.removeItem(SESSION_KEY);
      sessionStorage.removeItem('chatbot_messages');
    }
  } catch (error) {
    console.error('Error clearing expired session:', error);
    sessionStorage.removeItem(SESSION_KEY);
    sessionStorage.removeItem('chatbot_messages');
  }
}

// Clear expired session on load
clearExpiredSession();

// Simplified Config
const config: IConfig = {
  initialMessages: [],
  botName: "uRecruits Agent",
  customComponents: {
    botAvatar: (props) => {
      return (
        <div
          className={`chatbot-avatar ${props?.className || ''}`}
          style={{
            width: '50px',
            height: '50px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: 'transparent'
          }}
        >
          <img
            src={ChatBotImg}
            alt='uR Agent'
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain',
              display: 'block'
            }}
            {...props}
          />
        </div>
      );
    },
  },
  customStyles: {
    botMessageBox: {
      backgroundColor: "#099C73",
    },
    chatButton: {
      backgroundColor: "#099C73",
    },
  },
  state: {
    sessionId: getSessionId(),
  },
};

export default config;