import React, { useEffect } from 'react';
import { createClientMessage } from 'react-chatbot-kit';
import axios from 'axios';
import { getConfig } from '@ucrecruits/globalstyle/src/ucrecruits-globalstyle';
import { getEnv } from '@urecruits/api';

const { API_RECRUITMENT } = getEnv();

const ActionProvider = ({ createChatBotMessage, setState, children, state }: any) => {
  
  useEffect(() => {
    if (state.messages.length > 0) {
      sessionStorage.setItem('chatbot_messages', JSON.stringify(state.messages));
    }
  }, [state?.messages]);

  // Add initial message with proper formatting
  useEffect(() => {
    if (state.messages.length === 0) {
      const initialMessageText = "Hello! How can I help you today? I can do these things for you:\n\n1. Create Job\n2. Create Workflow\n3. Create Assessment";
      const formattedMessage = formatMessage(initialMessageText);
      const message = createChatBotMessage(formattedMessage, { delay: 500 });
      setState((prevState: any) => ({ ...prevState, messages: [message] }));
    }
  }, []);

  // API call function
  const sendMessageToAgent = async (message: string, sessionId: string) => {
    try {
      const response = await axios.post(
        `${API_RECRUITMENT}/api/agent`,
        { userInput: message, sessionId }, // Remove JSON.stringify - axios handles this
        getConfig()
      );
      return response.data;
    } catch (error) {
      throw new Error("Failed to communicate with agent.");
    }
  };

  // Format message with rich text support (keeping the original formatting logic)
  const formatMessage = (text: any) => {
    if (!text) return null;
    
    // Ensure text is a string
    if (typeof text !== 'string') {
      text = String(text);
    }

    // Handle markdown-style links first
    const markdownLinkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
    if (text.match(markdownLinkRegex)) {
      return (
        <span>
          {text.split(markdownLinkRegex).map((part: any, i: any) => {
            if (i % 3 === 1) { // This is the link text
              const url = text.split(markdownLinkRegex)[i + 1];
              return (
                <a
                  key={i}
                  href={url}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{ color: '#0066cc', textDecoration: 'underline' }}
                >
                  {part}
                </a>
              );
            }
            if (i % 3 === 2) return null;
            return formatRichText(part);
          })}
        </span>
      );
    }

    // Handle plain URLs
    const urlRegex = /(https?:\/\/[^\s]+(?![^)]*\)))/g;
    if (!text.match(urlRegex)) {
      return formatRichText(text);
    }

    return (
      <span>
        {text.split(urlRegex).map((part: any, i: any) =>
          part.match(urlRegex) ? (
            <a
              key={i}
              href={part}
              target="_blank"
              rel="noopener noreferrer"
              style={{ color: '#0066cc', textDecoration: 'underline', wordBreak: 'break-all' }}
            >
              {part}
            </a>
          ) : formatRichText(part)
        )}
      </span>
    );
  };

  const formatRichText = (text: string) => {
    if (!text) return null;

    // Handle comma-separated lists (like the industries list)
    if (text.includes(',') && text.split(',').length > 5) {
      const parts = text.split(':');
      if (parts.length === 2) {
        const header = parts[0].trim();
        const items = parts[1].split(',').map(item => item.trim()).filter(item => item);

        return (
          <div>
            <strong>{header}:</strong>
            <br /><br />
            {items.map((item, index) => (
              <span key={index}>
                • {item}
                {index < items.length - 1 && <br />}
              </span>
            ))}
          </div>
        );
      }
    }

    return text.split(/\n\n+/).map((paragraph, pIndex) => {
      if (!paragraph.trim()) return <br key={`br-${pIndex}`} />;

      return (
        <span key={`para-${pIndex}`}>
          {paragraph.split(/\n/).map((line, lIndex) => {
            if (!line.trim()) return <br key={`line-${pIndex}-${lIndex}`} />;

            const parts = line.split(/(\*\*|__)(.*?)\1/);
            return (
              <span key={`line-${pIndex}-${lIndex}`}>
                {parts.map((part, partIndex) => {
                  if (part === '**' || part === '__') return null;
                  if (parts[partIndex - 1] === '**' || parts[partIndex - 1] === '__') {
                    return <strong key={`bold-${pIndex}-${lIndex}-${partIndex}`}>{part}</strong>;
                  }
                  return part;
                })}
                {lIndex < paragraph.split(/\n/).length - 1 && <br />}
              </span>
            );
          })}
          {pIndex < text.split(/\n\n+/).length - 1 && <><br /><br /></>}
        </span>
      );
    });
  };

  // Handle chat response
  const handleChatResponse = async (messageStr: string, loadingMessage = "...") => {
    const loadingMsg = createChatBotMessage(loadingMessage, { loading: true, delay: 1000 });
    setState((prev: any) => ({ ...prev, messages: [...prev.messages, loadingMsg] }));

    try {
      const data = await sendMessageToAgent(messageStr, state.sessionId);
      
      // Try multiple possible response properties and extract the message text
      let responseText: string | null = null;
      if (data.chatResponse && data.chatResponse.message) {
        responseText = data.chatResponse.message;
      } else if (data.chatResponse && typeof data.chatResponse === 'string') {
        responseText = data.chatResponse;
      } else if (data.response && data.response.message) {
        responseText = data.response.message;
      } else if (data.response && typeof data.response === 'string') {
        responseText = data.response;
      } else if (data.message) {
        responseText = data.message;
      } else if (data.text) {
        responseText = data.text;
      } else if (typeof data === 'string') {
        responseText = data;
      } else if (data.data && data.data.chatResponse && data.data.chatResponse.message) {
        responseText = data.data.chatResponse.message;
      } else if (data.data && data.data.chatResponse && typeof data.data.chatResponse === 'string') {
        responseText = data.data.chatResponse;
      } else if (data.data && typeof data.data === 'string') {
        responseText = data.data;
      }
      
      if (responseText) {
        const formattedResponse = formatMessage(responseText);
        const message = createChatBotMessage(formattedResponse, { delay: 1000 });
        setState((prev: any) => ({ ...prev, messages: [...prev.messages.slice(0, -1), message] }));
      } else {
        setState((prev: any) => ({
          ...prev,
          messages: [...prev.messages.slice(0, -1), createChatBotMessage("I received your message but couldn't generate a response. Please try again.")]
        }));
      }
    } catch (error) {
      console.error("Error in conversation:", error);
      setState((prev: any) => ({
        ...prev,
        messages: [...prev.messages.slice(0, -1), createChatBotMessage("Sorry, I encountered an error. Please try again.")]
      }));
    }
  };

  const actions = {
    greet: () => {
      const messageText = "Hello! How can I help you today? I can do these things for you:\n\n1. Create Job\n2. Create Workflow\n3. Create Assessment";
      const formattedMessage = formatMessage(messageText);
      const message = createChatBotMessage(formattedMessage, { delay: 500 });
      setState((state: any) => ({ ...state, messages: [...state.messages, message] }));
    },
    sendClientMessage: (messageStr: string) => {
      const message = createClientMessage(messageStr, { delay: 1000 });
      setState((prev: any) => ({ ...prev, messages: [...prev.messages, message] }));
    },
    resetState: () => {
      setState((state: any) => ({ ...state, messages: [] }));
      sessionStorage.removeItem('chatbot_messages');
    },
    chatConversation: async (messageStr: string) => {
      if (messageStr) {
        await handleChatResponse(messageStr);
      }
    }
  };

  return (
    <div>
      {React.Children.map(children, child => React.cloneElement(child, { actions }))}
    </div>
  );
};

export default ActionProvider;
