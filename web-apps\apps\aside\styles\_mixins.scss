@use 'sass:map';
@use "config" as *;
@mixin media($breakpoint) {
  @if map.get($breakpoints, $breakpoint) != null {
    @media (max-width: #{map.get($breakpoints, $breakpoint)}) {
      @content;
    }
  } @else {
    @warn "Unfortunately, no value could be retrieved from `#{$breakpoint}`. "
        + "Please make sure it is defined in `$breakpoints` map.";
  }
}

@mixin media_min($breakpoint) {
  @if map.get($breakpoints, $breakpoint) != null {
    @media (min-width: #{map.get($breakpoints, $breakpoint)}) {
      @content;
    }
  } @else {
    @warn "Unfortunately, no value could be retrieved from `#{$breakpoint}`. "
        + "Please make sure it is defined in `$breakpoints` map.";
  }
}


@mixin media_range($breakpoint, $breakpoint2) {
  @if map.get($breakpoints, $breakpoint) != null {
    @media (max-width: #{map.get($breakpoints, $breakpoint2)}) and (min-width: #{map.get($breakpoints, $breakpoint)}) {
      @content;
    }
  } @else {
    @warn "Unfortunately, no value could be retrieved from `#{$breakpoint}`. "
        + "Please make sure it is defined in `$breakpoints` map.";
  }
}

@mixin container {
  width: 100%;
  padding-right: 32px;
  padding-left: 32px;
  margin-right: auto;
  margin-left: auto;


  @include media_min(xs) {
    max-width: 540px;
    padding-right: 16px;
    padding-left: 16px;
  }
  @include media_min(sm) {
    max-width: 720px;
  }
  @include media_min(md) {
    max-width: 960px;
  }
  @include media_min(lg) {
    max-width: 1200px;
  }
  @include media_min(xl) {
    max-width: 1300px;
  }
}

@mixin authContainer {
  width: 100%;
  padding-right: 32px;
  padding-left: 32px;
  margin-right: auto;
  margin-left: auto;


  @include media_min(xs) {
    max-width: 540px;
    padding-right: 16px;
    padding-left: 16px;
  }
  @include media_min(sm) {
    max-width: 720px;
  }
  @include media_min(md) {
    max-width: 960px;
  }
  @include media_min(lg) {
    max-width: 1200px;
  }
  @include media_min(xl) {
    max-width: 1400px;
  }
}

@mixin label {
  font-family: "Inter", "Avenir LT Std", sans-serif;
  font-style: normal;
  font-weight: 800;
  font-size: 14px;
  color: $grayTone7;
  line-height: 1.4;
  margin-bottom: 6px;
  display: block;

  span {
    color: $mainGreen;
  }
}

@mixin authSection {
  padding: 0;
  width: 100%;
  min-height: inherit;
  display: flex;
  flex-direction: column;
  justify-content: center;
  @include media(xs) {
    justify-content: flex-start;
  }
}

@mixin input {
  border: 1px solid $grayTone2;
  box-sizing: border-box;
  border-radius: 4px;
  width: 100%;
  line-height: 1;
  padding: 13px 16px;
  font-size: 14px;
  color: $black;

  &:hover {
    border-color: $grayTone3;

    &::placeholder {
      color: $grayTone5;
    }
  }

  &::placeholder {
    color: $grayTone4;
    font-size: 14px;
    font-family: "Inter", "Avenir LT Std", sans-serif;
  }
}

@mixin customCheckbox {
  label {
    background: $white;
    border: 1px solid $grayTone3;
    box-sizing: border-box;
    border-radius: 2px;
    width: 16px;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  input {
    display: none;
  }

  input:checked + label {
    background: $white;
    border-color: $mainGreen;
  }

  input:checked ~ label span {
    background: url("../public/images/icon/done_ic.svg") no-repeat;
    width: 10px;
    min-width: 10px;
    height: 7px;
    background-size: contain;
    display: block;
    @include media(xs) {
      width: 13px;
      height: 10px;
    }
  }
}

@mixin error-message {
  display: none;
  font-size: 12px;
  line-height: 1;
  color: $red;
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  transform: translateY(4px);
}