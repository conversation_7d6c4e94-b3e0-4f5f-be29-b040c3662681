@use "../../config" as *;
@use "../../mixins" as *;
@use "../../color-palette" as *;

.customSelect {
  display: flex;
  align-items: center;
  // border: 1px solid #dfe2e6;
  border-radius: 4px;

  &.active{
    border: 1px solid $lightGreen;
    @include theme-aware("color", "modal-title-color");
    z-index: 15;
  }
  &__select_box__options_container__option {
          padding: 12px 15px;
        }

  &__select_box__options_container.scroll.active {
    background: $white;
  }

  &__selected {
    @include theme-aware("border-color", "border-color");
    @include theme-aware("color", "text-color2");
    border-radius: 4px;
    font-size: 14px;
    width: 100%;
    height: 44px;
    padding: 12px 16px 12px 16px;
    cursor: pointer;
    background: transparent;
    position: relative;
    order: 0;
    background-position: bottom 48% right 15px;
    background-size: 10px 8px;
    background-repeat: no-repeat;
    transition: 0.2s ease-out;

    &.noBorder {
      border: none;
      padding: 12px 26px 12px 0;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  &__optionsWrapper {
    position: absolute;
    width: 100%;
    top: 100%;
    left: 50%;
    max-height: 240px;
    border: 1px solid transparent;
    @include theme-aware('background', 'modal-bg');
    border-radius: 4px;
    @include theme-aware('border-color', 'border-color');
    padding: 8px 3px 8px 3px;
    transform: translateX(-50%);
    box-shadow: 0 6px 12px rgba(7, 7, 12, 0.08);
    z-index: 2;
  }

  &__label {
    font-size: 14px;
    font-weight: 700;
    @include theme-aware('color', 'modal-title-text');
  }

  &__select_box {
    display: flex;
    width: 100%;
    flex-direction: column;
    position: relative;

    &__options_container {
      margin-top: -1px;
      @include theme-aware('background', 'modal-bg');
      order: 1;

      &__option {
        //padding: 10px 24px;
        cursor: pointer;
        @include theme-aware('color', 'title-color');
        font-weight: 400;
        font-size: 14px;

        &__radio {
          display: none;
        }
      }

      &__option:hover {
        @include theme-aware('background', 'border-color');
      }
    }

    &__options_container.scroll.active {
      max-height: 220px;
      opacity: 1;
      overflow-y: auto;
      width: 100%;
      z-index: 3;
    }

    &__options_container.filter-scroll.active {
      height: 100%;
      opacity: 1;
      overflow-y: unset !important;
      width: 100%;
      z-index: 3;
    }
  }
}

.filter-options {
  font-family: "Inter", "Avenir LT Std" !important;
  font-style: normal !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  color: #099C73 !important;
}

.filter-options-background {
  background: #E7F4F2 !important;
  overflow: unset;
}
.customSelect__selected {
  @include font-control(14px, 19px, 800);
  border: none;
  padding: 0 16px 0 0;
  height: auto;
  @include theme-aware("color", "text-color2");
}

.customSelect__select_box {
  margin: 0;
}

.customSelect__selected {
  border: 1px solid #dfe2e6;
  border-radius: 4px;
  font-size: 14px;
  width: 100%;
  height: 44px !important;
  padding: 12px 16px 12px 16px;
  cursor: pointer;
  background: transparent;
  position: relative;
  order: 0;
  background-position: bottom 48% right 15px;
  background-size: 10px 8px;
  background-repeat: no-repeat;
  transition: 0.2s ease-out;
  font-weight: 400;
}

.error {
  border: 1px solid $red !important;
}

.error-data-select {
  // position: absolute;
  font-size: 12px;
  color: $red;
  // margin-top: 48px;
}
