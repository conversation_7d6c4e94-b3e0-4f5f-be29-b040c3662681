@use './config' as *;
@use './mixins' as *;
.table-card-list-container {

  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  min-height: 650px;
    max-height: 100%;
    position: relative;

    @include media(xl) {
      min-height: 600px;
    }

    @include media(md) {
      min-height: 600px;
    }

    @include media(sm) {
      border: 0;
      min-height: 800px;
    }

    @include media(xs) {
      border: 0;
      min-height: 300px;
    }

  .table-card-filter {
    display: flex;
    // max-width: 864px;
    // width: 100%;
    // margin: 0 auto;
    padding-bottom: 24px;
  }

  .table-card-list {
    display: flex;
    flex-direction: column;
    // max-width: 864px;
    // width: 100%;
    // margin: 0 auto;
    height: calc(100vh - 255px);

    &__inner {
      overflow-y: auto;
      padding-right: 16px;

      &::-webkit-scrollbar {
        width: 3px;
        height: 3px;
        cursor: pointer;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      &::-webkit-scrollbar-thumb {
        background: #888;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #555;
      }
    }

    &__card {
      width: 100%;
      margin-bottom: 28px;
      background: $white;
      border-radius: 12px;
      border: 1px solid $grayTone2;
      padding: 24px;
      display: flex;
      flex-direction: column;
      position: relative;

      @include media(xs) {
        padding: 16px;
      }

      .table_disable_row {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 9;
        background: #ffffff66;
        width: 100%;
        height: 100%;
        border-radius: 12px;
        .show{
          z-index: 20;
        }
  
        &__chip {
          background: #DFF0F2;
          color: #2A2C33;
          padding: 4px 18px;
          border-radius: 20px;
          text-align: center;
          font-size: 16px;
          font-weight: 700;
          width: fit-content;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }
    .job-list {
      &__action {
        position: relative;
        z-index: 9;
      }
    }
  }

  .table__bottom {
    padding: 15px 15px;
  }
}