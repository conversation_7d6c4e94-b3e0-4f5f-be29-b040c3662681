@use "config" as *;
@use "mixins" as *;
.popup {
  background: rgba(0, 0, 0, 0.5411764705882353);
  width: 100%;
  height: 100%;
  z-index: 700;
  left: 0;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  & .send-approval-button{
    min-width: 180px;
  }

  &.apply-popup {
    .popup__body__text {
      color: $grayTone7;
    }
  }

  &.return-popup {
    .popup__step {
      max-width: 384px;
    }
  }

  &__step {
    background: $white;
    border-radius: 12px;
    padding: 32px;
    max-width: 458px;
    width: 100%;
    margin: 0 16px;
  }

  &__head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    min-height: 20px;

    &__close {
      width: 24px;
      min-width: 24px;
      height: 24px;
      object-fit: contain;
      cursor: pointer;
      margin-left: auto;

      path {
        transition: 0.3s ease-in, 0.3s ease-in-out;
      }

      &:hover {
        path {
          stroke: $red;
        }
      }
    }

    &__headline {
      font-family: "Inter", "Poppins", sans-serif;
      font-size: 20px;
      font-weight: 600;
      line-height: 1;
      color: $grayTone7;
    }
  }

  &__body {
    display: flex;
    flex-direction: column;
    position: relative;
    margin-bottom: 35px;

    &__text {
      font-size: 14px;

      a {
        color: $mainGreen;
      }
      &.center {
        text-align: center;
      }
    }

    &__image {
      width: 110px;
      min-width: 110px;
      height: 110px;
      object-fit: cover;
      align-self: center;
      margin-bottom: 28px;

      &.alarm {
        margin-top: 24px;
      }
    }

    &__headline {
      font-family: "Inter", "Poppins", sans-serif;
      color: $grayTone7;
      font-weight: 600;
      font-size: 20px;
      line-height: 1;
      text-align: center;
    }

    &__item {
      margin-top: 28px;
      position: relative;

      .error-message {
        @include error-message;
      }

      &.error {
        .error-message {
          display: block;
        }
      }
    }

    &__label {
      @include label;
    }

    &__link {
      color: $mainGreen;
      font-size: 14px;
      font-weight: 500;
      display: block;
      text-align: center;
      text-decoration: underline;
      margin-top: 16px;
    }
  }

  &__bottom {
    display: flex;
    align-items: center;
    position: relative;

    &.end {
      justify-content: flex-end;
    }

    &.center {
      justify-content: center;
    }

    &__cancel {
      margin-right: 20px;
    }

    .error-message {
      @include error-message;
      display: block;
      transform: translateY(8px);
    }

    &__publish {
      &:disabled {
        cursor: not-allowed;
      }
      cursor: pointer;
    }
  }

  &__cancel {
    font-weight: 800;
    font-size: 14px;
    color: $grayTone4;
    margin-right: 25px;
    transition: 0.3s ease-in, 0.3s ease-in-out;

    &:hover {
      color: $black;
    }

    @include media(xs) {
      display: none;
    }
  }

  &__approval {
    display: flex;
    align-items: center;
    margin-right: 18px;
    &:disabled {
      cursor: not-allowed;
    }

    &:hover {
      .popup__approval__text {
        color: $mainGreen;
      }
    }

    &__icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }

    &__text {
      font-size: 14px;
      font-weight: 800;
      transition: 0.3s ease-in, 0.3s ease-in-out;
      color: $grayTone7;

      @include media(xs) {
        flex-shrink: 0;
      }
    }
  }

  &__text {
    font-weight: 400;
    font-size: 14px;
    line-height: 1.3;
    word-break: break-word;
    display: flex;
    &__button {
      margin-left: 12px;
    }
    &--green {
      font-weight: 500;
      color: $mainGreen;
    }

    &--gray5 {
      color: $grayTone5;
    }

    &--gray7 {
      font-weight: 500;
      color: $grayTone7;
    }

    &--black {
      color: $black;
    }

    &--link {
      font-weight: 500;
      color: $mainGreen;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.popup-inside-modal {
  display: flex;
  flex-direction: column;
  padding: 5px 0 8px;

  &__item {
    margin-bottom: 25px;
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;

    &.nomb {
      margin-bottom: 0;
    }

    &:last-child {
      margin-bottom: 10px;
    }

    &__text {
      font-weight: 500;
      font-size: 14px;
      line-height: 1;
      color: $grayTone4;
      margin: 16px auto;
      &.empty{
        margin: 16px 0;
      }
    }

  }

  &__more {
    border: 1px solid $greenBlue1;
    border-radius: 4px;
    width: 100%;
    padding: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(2, 156, 165, 0.1);
    cursor: pointer;
    margin-bottom: 20px;

    &.readonly {
      background: $grayTone1;
      border-color: $grayTone2;
      cursor: default;
    }

    &--icon {
      width: 16px;
      min-width: 16px;
      height: 16px;
      object-fit: contain;
      margin-right: 12px;
    }

    &--icon-invisible {
      visibility: hidden;
    }

    &--text {
      font-size: 14px;
      line-height: 1;
      color: $greenBlue2;
      margin-top: 2px;
    }
  }
  input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
               -webkit-appearance: none;
                margin: 0;
        }
 
        input[type=number] {
            -moz-appearance: textfield;
        }
}