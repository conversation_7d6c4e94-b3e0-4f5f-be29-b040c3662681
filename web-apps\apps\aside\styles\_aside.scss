@use "./mixins" as *;
@use "./config" as *;
@use "sass:color";

.aside {
  position: relative;

  @include media(md) {
    display: none;
  }

  &__inner {
    width: 68px;
    height: calc(100vh - 20px);
    background: $black;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: fixed;
    left: 0;
    top: 30px;
    z-index: 3;
    font-size: 14px;

    @include media(xs) {
      transform: translateX(-68px);
    }

    @include media(sm) {
      z-index: 5;
    }

    &.active {
      width: 228px;
      // width: 100%;
      transform: translateX(0);

      .aside__top__link,
      .aside__bottom__link {
        justify-content: flex-start;
      }

      .aside__top__text,
      .aside__bottom__text {
        opacity: 1;
        display: block;
      }

      .aside__toggle {
        @include media(xs) {
          transform: translate(7px, -16px);
        }
      }

      .aside__toggle__icon {
        transform: rotate(180deg);
      }
    }
  }

  &__toggle {
    cursor: pointer;
    position: absolute;
    width: 13px;
    height: 24px;
    background: $lightGreen;
    border-radius: 2px;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 40px;
    z-index: 10;
    right: 0;
    transform: translateX(7px);

    @include media(xs) {
      transform: translate(14px, -16px);
      height: 48px;
    }

    &__icon {
      min-width: 5px;
      width: 5px;
      height: 10px;
      object-fit: contain;
    }
  }

  &__top,
  &__bottom {
    .css-h147x1-MuiButtonBase-root-MuiButton-root {
      width: 100%;
    }

    &__part {
      padding: 16px 0;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: $black;

      &:after {
        content: "";
        position: absolute;
        bottom: 0;
        width: 24px;
        height: 1px;
        background: $grayTone6;
      }

      &.bottom {
        background: #099c73;
        padding-top: 0;

        .aside__bottom__link {
          &:hover {
            background: #068662;
          }
        }
      }
    }

    &__list {
      width: 100%;
    }

    &__link {
      padding: 6px 12px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: 0.3s ease-in, 0.3s ease-in-out;

      &:hover {
        background: color.adjust($mainGreen, $alpha: -0.70);
        border-radius: 8px;

      }

      &.active {
        background: color.adjust($mainGreen, $alpha: -0.30);
        border-radius: 8px;
        position: relative;
        overflow: hidden;

        // &:after {
        //   content: "";
        //   position: absolute;
        //   height: 100%;
        //   width: 3px;
        //   background: $greenBlue2;
        //   left: 0;
        //   top: 0;
        //   border-top-right-radius: 30px;
        //   border-bottom-right-radius: 30px;
        // }
      }
    }

    &__icon {
      min-width: 18px;
      width: 18px;
      height: 18px;
    }

    &__text {
      display: none;
      opacity: 0;
      font-size: 14px;
      padding-left: 12px;
      color: $white;
    }
  }

  &__top {
    padding: 20px 10px;
    height: calc(100% - 84px);
    overflow-y: auto; // or scroll if you want scrollbar always visible

    scrollbar-width: auto; // Firefox - shows the default scrollbar

    &::-webkit-scrollbar {
      width: 8px; // typical scrollbar width
    }

    &::-webkit-scrollbar-thumb {
      background-color: #099c73;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent; // optional
    }
  }

  &__bottom {
    &__part {
      padding-bottom: 0;

      &:after {
        display: none;
      }
    }
  }
}