@use "config" as *;
@use "mixins" as *;
.analytics-card {
    background: $white;
    padding: 16px;
    border-radius: 12px;
    border: 1px solid $grayTone2;
    width: 100%;

    .analytics-card__head {
      .analytics-card__head__icon__date {
        display: flex;
        justify-content: space-between;

        .analytics-card__head__icon__card {
          width: 60px;
          height: 60px;
          background: #F8F9FB;
          border: 1px solid #DFE2E6;
          border-radius: 8px;
            
          .analytics-card__icon {
            padding: 10px;
         } 
        }
         .analytics-card__head__last__view {
          font-size: 12px;
          font-weight: 400;
          line-height: 18px;
          color: #999EA5;
  
          .head__date {
            color: #464E57;
          }
        
      }
    }

    .analytics-card__head__title {
      font-size: 16px;
      font-weight: 900;
      line-height: 16px;
      color: #2A2C33;
      padding: 16px 0 8px 0;
    }
  }


    .analytics-card__body {
      
      .analytics-card__body__description {
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        color: #464E57;
        max-height: 83px;
        min-height: 83px;
        white-space: wrap;
        overflow: auto;
        text-overflow: ellipsis;
        -ms-overflow-style: none;
        scrollbar-width: none; 
      }
    }
    .btn-set-up-report {
      width: 132px;
      height: 32px;
      border: 1px solid #099C73;
      border-radius: 4px;
      padding: 16px, 12px, 16px, 12px;
      color: #099C73;
      font-size: 12px;
      font-weight: 900;
    }
  }