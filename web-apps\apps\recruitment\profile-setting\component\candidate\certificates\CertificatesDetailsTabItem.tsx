import DatePicker from 'react-datepicker'
import { memo, useEffect, useState } from 'react'
import SmallLoader from '../../SmallLoader'
import deleteData from '../../../hook/deleteData'
import axios from 'axios'
import { getEnv } from "@urecruits/api"
import { Box, Chip } from "@mui/material"
import { getConfig } from '@ucrecruits/globalstyle/src/ucrecruits-globalstyle'

const paperClipIc = require('../../../image/icon/paper_clip_ic.svg')
const downloadIc = require('../../../image/icon/download_ic.svg')
const removeIc = require('../../../image/icon/delete_ic.svg')

const CertificatesDetailsTabItem =
  ({
    editMode,
    currentItem,
    setDataDetails,
    dataDetails,
    setValidateStatus,
    validateStatus,
    displayButton,
    setDisplayButton,
    setEditMode,
    addedMoreEducationButtonRef,
    submitButtonRef,
    index,
    setRefetch,
    setData,
  }) => {
    const token: string = localStorage.getItem('token')
    const [certificateNameError, setCertificateNameError] = useState(editMode ? !currentItem?.certificateName : false)
    const [instituteNameError, setInstituteNameError] = useState(editMode ? !currentItem?.instituteName : false)
    const [periodDateStartError, setPeriodDateStartError] = useState(editMode ? !currentItem?.validityStart : false)
    const [periodDateEndError, setPeriodDateEndError] = useState(editMode ? !currentItem?.validityEnd : false)
    const [certificateFileError, setCertificateFileError] = useState(false)

    const [urlLoader, setUrlLoader] = useState(false)
    const [url, setUrl] = useState('')
    const [loader, setLoader] = useState(false)

    const [fileTypeError, setFileTypeError] = useState(false)
    const [fileSizeError, setFileSizeError] = useState(false)

    const fieldsValidate = () => {
      let error = 0
      dataDetails.map((object) => {
        !object.certificateName || object.certificateName.length <= 0 ? setCertificateNameError(true) : setCertificateNameError(false)
        !object.instituteName || object.instituteName.length <= 0 ? setInstituteNameError(true) : setInstituteNameError(false)
        !object.fileName || object.fileName.length <= 0 ? setCertificateFileError(true) : setCertificateFileError(false)
        !object.validityStart ? setPeriodDateStartError(true) : setPeriodDateStartError(false)
        !object.validityEnd ? setPeriodDateEndError(true) : setPeriodDateEndError(false)

        if (!object.certificateName || !object.instituteName || object.certificateName.length <= 0 || object.instituteName.length <= 0 ||
          !object.validityStart ||
          !object.validityEnd) {
          error++
        }
      })

      if (error === 0) {
        setValidateStatus(true)
      }
    }

    useEffect(() => {
      if (!validateStatus && editMode) {
        //added more click validate
        addedMoreEducationButtonRef.current.onclick = function () {
          fieldsValidate()
        }

        //on submit click validate
        if (displayButton) {
          submitButtonRef.current.onclick = function () {
            fieldsValidate()
          }
        }
      }
    }, [validateStatus, dataDetails, currentItem, editMode])

    const onRemoveCertificateHandler = () => {
      if (editMode) {
        setDataDetails([...dataDetails].map(object => {
          if (object.id === currentItem.id) {
            return {
              ...object,
              fileKey: '',
              fileName: '',
            }
          } else return object
        }))
        setDisplayButton(true)
        setEditMode(true)
      }
    }

    const onDownloadHandler = async () => {
      const formData = {
        'key': currentItem.fileKey,
      }
      setUrlLoader(true)
      const { data } = await axios.post(`${API_RECRUITMENT}/api/file/get-private`, formData, getConfig())
      setUrl(data)
      setUrlLoader(false)
    }

    const handleNewFileUpload = async (e) => {
      //validate
      e.target.files[0].size / 1024 > 5000 ? setFileSizeError(true) : setFileSizeError(false)
      e.target.files[0].type === 'application/pdf' || e.target.files[0].type === 'image/jpeg' || e.target.files[0].type === 'image/png'
        ? setFileTypeError(false)
        : setFileTypeError(true)

      if (fileValidate(e.target.files[0])) {
        const file = e.target.files[0]
        const formData = new FormData()
        formData.append('upload', file)
        setLoader(true)
        const { data } = await axios.post(`${API_RECRUITMENT}/api/file/private`, formData, getConfig())
        setDataDetails([...dataDetails].map(object => {
          if (object.id === currentItem.id) {
            return {
              ...object,
              fileKey: data?.key ?? '',
              fileName: file ? file.name : '',
            }
          } else return object
        }))
        setLoader(false)
        setDisplayButton(true)
        setEditMode(true)
      }
    }
    const { API_RECRUITMENT } = getEnv()

    const onRemoveItemHandler = async (e) => {
      e.preventDefault
      if (editMode) {
        if (currentItem?.isResumeParsed) {
          setData(oldData => {
            let certificates = [...oldData?.certificates];
            certificates = certificates?.filter(i => i.id !== currentItem.id)
            oldData.certificates = certificates;
            return { ...oldData }
          });
        } else {

          const res = await deleteData(`${API_RECRUITMENT}/api/candidate/certificate`, currentItem);
          if (res.status == 200) {
            setData(oldData => {
              const _oldData = JSON.parse(JSON.stringify(oldData));
              let certificates = _oldData.certificates;
              certificates = certificates.filter(i => i.id !== currentItem.id);
              return { ..._oldData, certificates }
            })
          }

        }
      }
    }

    const fileValidate = (value) => {
      return value.size / 1024 < 5000 && (value.type === 'application/pdf' || value.type === 'image/jpeg' || value.type === 'image/png')
    }

    return (
      <>
        <div className="profile__form__part">
          <div className={`flex`}>
            <Box>
              {currentItem?.isResumeParsed && <Chip variant='outlined' color='error' label="Unsaved" />}
            </Box>
            <div className={`profile__form__delete ${!editMode ? 'hide' : ''}`}>
              <div className="profile__form__delete--inner" onClick={(e) => onRemoveItemHandler(e)}>
                <span className="profile__form__delete--text">Delete</span>
                <img src={removeIc} alt="delete icon" className="profile__form__delete--icon" />
              </div>
            </div>
          </div>
          <div className="profile__form__group">
            <div className={`profile__form__item ${certificateNameError ? 'error' : ''}`}>
              <label className="profile__form__label">Certificate Name<span> *</span></label>
              <input
                type="text"
                className="profile__form__input"
                placeholder={`${editMode ? 'Enter certificate name' : 'Not indicated'}`}
                readOnly={!editMode}
                value={currentItem.certificateName ? currentItem.certificateName : ''}
                onChange={(e) => {
                  setDataDetails([...dataDetails].map(object => {
                    if (object.id === currentItem.id) {
                      return {
                        ...object,
                        certificateName: e.target.value,
                      }
                    } else return object
                  }))
                  e.target.value.length <= 0 ? setCertificateNameError(true) : setCertificateNameError(false)
                }}
              />
              <p className="error-message">This is required field</p>
            </div>
            <div className={`profile__form__item ${instituteNameError ? 'error' : ''}`}>
              <label className="profile__form__label">Institute Name<span> *</span></label>
              <input
                type="text"
                className="profile__form__input"
                placeholder={`${editMode ? 'Enter institute name' : 'Not indicated'}`}
                readOnly={!editMode}
                value={currentItem.instituteName ? currentItem.instituteName : ''}
                onChange={(e) => {
                  setDataDetails([...dataDetails].map(object => {
                    if (object.id === currentItem.id) {
                      return {
                        ...object,
                        instituteName: e.target.value,
                      }
                    } else return object
                  }))
                  e.target.value.length <= 0 ? setInstituteNameError(true) : setInstituteNameError(false)
                }}
              />
              <p className="error-message">This is required field</p>
            </div>
          </div>
          <div className="profile__form__group">
            <div className={`profile__form__item short after-line ${periodDateStartError ? 'error' : ''}`}>
              <label className="profile__form__label">Certificate Periods<span> *</span></label>
              <div className="profile__form__date">
                <DatePicker
                  showYearDropdown={true}
                  scrollableYearDropdown={true}
                  yearDropdownItemNumber={70}
                  selected={currentItem.validityStart ? new Date(currentItem.validityStart) : null}
                  onChange={(date: any) => {
                    setDisplayButton(true)
                    setDataDetails([...dataDetails].map(object => {
                      if (object.id === currentItem.id) {
                        return {
                          ...object,
                          validityStart: date,
                        }
                      } else return object
                    }))
                    !date ? setPeriodDateStartError(true) : setPeriodDateStartError(false)
                  }}
                  dateFormat="MMMM d, yyyy"
                  maxDate={new Date()}
                  readOnly={!editMode}
                  placeholderText="Certificate validity start"
                />
              </div>
              <p className="error-message">This is required field</p>
            </div>
            <div className={`profile__form__item short ${periodDateEndError ? 'error' : ''}`}>
              <div className={`profile__form__date ${!currentItem.validityStart && 'profile__form__date--readonly'}`}>
                <DatePicker
                  showYearDropdown={true}
                  scrollableYearDropdown={true}
                  yearDropdownItemNumber={70}
                  selected={currentItem.validityEnd ? new Date(currentItem.validityEnd) : null}
                  onChange={(date: any) => {
                    setDisplayButton(true)
                    setDataDetails([...dataDetails].map(object => {
                      if (object.id === currentItem.id) {
                        return {
                          ...object,
                          validityEnd: date,
                        }
                      } else return object
                    }))
                    !date ? setPeriodDateEndError(true) : setPeriodDateEndError(false)
                  }}
                  dateFormat="MMMM d, yyyy"
                  minDate={currentItem.validityStart ? new Date(currentItem.validityStart) : null}
                  readOnly={!editMode || !currentItem.validityStart}
                  placeholderText="Certificate validity end"
                />
              </div>
              <p className="error-message">This is required field</p>
            </div>
            <div className={`profile__form__item short `}>
              <label className="profile__form__label">Expire Details</label>
              <div className={`profile__form__date ${!currentItem.validityStart && 'profile__form__date--readonly'}`}>
                <DatePicker
                  showYearDropdown={true}
                  scrollableYearDropdown={true}
                  yearDropdownItemNumber={70}
                  selected={currentItem.expireDate ? new Date(currentItem.expireDate) : null}
                  onChange={(date: any) => {
                    setDisplayButton(true)
                    setDataDetails([...dataDetails].map(object => {
                      if (object.id === currentItem.id) {
                        return {
                          ...object,
                          expireDate: date,
                        }
                      } else return object
                    }))
                  }}
                  dateFormat="MMMM d, yyyy"
                  minDate={currentItem.validityStart ? new Date(currentItem.validityStart) : null}
                  readOnly={!editMode || !currentItem.validityStart}
                  placeholderText="Expire date"
                />
              </div>
              <p className="error-message">This is required field</p>
            </div>
          </div>
          <div className="profile__form__group">
            <div className={`profile__form__item long`}>
              <label className="profile__form__label">Notes</label>
              <textarea
                className="profile__form__textarea"
                placeholder={`${editMode ? 'Write notes' : 'Not indicated'}`}
                readOnly={!editMode}
                value={currentItem.notes ? currentItem.notes : ''}
                onChange={(e) => {
                  setDataDetails([...dataDetails].map(object => {
                    if (object.id === currentItem.id) {
                      return {
                        ...object,
                        notes: e.target.value,
                      }
                    } else return object
                  }))
                }}
              />
              <p className="error-message">This is required field</p>
            </div>
          </div>
          <div className="profile__form__group">
            <div className="profile__form__item long">
              {
                currentItem.fileKey ?
                  <div className={`profile__upload ${editMode ? '' : 'readonly'}`}>
                    <p className="profile__upload__type">
                      Certificate File
                    </p>
                    <div className="profile__upload__body">
                      <div className="profile__upload__left">
                        <div className="profile__upload__info">
                          <div className="profile__upload__button">
                            <img src={paperClipIc} alt="upload icon" className="profile__upload__button--icon" />
                            <p className="profile__upload__button--name">
                              {currentItem.fileName}
                            </p>
                          </div>
                        </div>
                      </div>
                      <div className="profile__upload__right">
                        {!url && (
                          <img src={downloadIc} alt="download icon" className="profile__upload__download"
                            onClick={onDownloadHandler} />
                        )}
                        {urlLoader && <p>Loading...</p>}
                        {url && <a href={url} target={'_blank'}>Download</a>}
                        <img
                          src={removeIc}
                          alt="remove icon"
                          className="profile__upload__remove"
                          onClick={onRemoveCertificateHandler}
                        />
                      </div>
                    </div>
                  </div>
                  :
                  <div className="profile__file last">
                    {loader && (<SmallLoader />)}
                    <input
                      type="file"
                      name="uploadCv"
                      id="uploadCv"
                      disabled={!editMode}
                      accept="application/pdf, image/*"
                      className="profile__file__input"
                      onChange={handleNewFileUpload}
                    />
                    <label
                      htmlFor="uploadCv"
                      className={`profile__file__button  ${editMode ? 'button--empty' : 'button--empty-disable'}`}
                    >
                      Upload file
                    </label>
                    <p className="profile__file__info">
                      <span className="profile__file__info--text">Max size:<span
                        className="profile__file__info--value"> 1Mb</span>
                      </span>
                      <span className="profile__file__info--text">File type:<span
                        className="profile__file__info--value"> .images, .pdf</span></span>
                    </p>
                    <p className="error-message">
                      {fileSizeError ? 'File size must be less than 5 MB.' : ''} {fileTypeError ? ' Incorrect file type.' : ''}
                    </p>
                  </div>
              }
            </div>
          </div>
        </div>
      </>
    )
  }

export default memo(CertificatesDetailsTabItem)