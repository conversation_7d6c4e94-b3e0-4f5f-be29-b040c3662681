import { getConfig } from "@ucrecruits/globalstyle/src/ucrecruits-globalstyle";
import { getEnv } from "@urecruits/api";
import axios from "axios";
import { useState } from "react";

const { API_RECRUITMENT } = getEnv();

const BoardItem = ({ item, integrations, setIntegrations, setIsModalOpen }) => {
	const [isLoading, setIsLoading] = useState(false);

	const isJobTarget = item.name?.trim() === "JobTarget"; // Replace with actual condition, e.g., item.id === JOBTARGET_ID

	const changeHandler = async () => {

		if (item.id !== 1) {
			if (integrations.find((x) => x.id === item.id)) {
				// Deselect: Remove item from integrations
				setIntegrations((prev) => prev.filter((x) => x.id !== item.id));
			} else {
				// Select: Check JobTarget integration if applicable
				if (isJobTarget) {
					setIsLoading(true);
					try {
						const response = await axios(
							`${API_RECRUITMENT}/api/jobtarget/account/get`,
							getConfig()
						);
						console.log(response.data, "jobtarget response");
						if (response.data && response.data.token) {
							// Record found, allow selection
							setIntegrations((prev) => [
								...prev,
								{ name: item.name, id: item.id, uniqueIntegrationId: item.unique_id },
							]);
						} else {
							// No record found, open modal
							setIsModalOpen(true);
						}
					} catch (error) {
						console.error("Error checking JobTarget integration:", error);
						setIsModalOpen(true); // Open modal on error as a fallback
					} finally {
						setIsLoading(false);
					}
				} else {
					// Non-JobTarget item, add to integrations
					setIntegrations((prev) => [
						...prev,
						{ name: item.name, id: item.id, uniqueIntegrationId: item.unique_id },
					]);
				}
			}
		}
	};

	return (
		<button
			className={`boards__item ${integrations.find((x) => x.id === item.id) ? "active" : ""}`}
			onClick={changeHandler}
			disabled={isLoading}
		>
			<div className="boards__item__image">
				<img
					src={item.image.url}
					alt="board name"
					className="boards__item__image"
					style={isJobTarget ? { height: '50px' } : {}}
				/>
			</div>
			<p className="boards__item__name">{item.name}</p>
			{isLoading && <span>Loading...</span>}
		</button>
	);
};

export default BoardItem;