import { memo, useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, Pie, Cell } from "recharts";

const pieColors= ['#029CA5','#021CA5','#737980','#E9C715','#FF8541','#7D97F4','#FE4672']
type CircleChartMembersType = {
  title: string;
  pieChartData: {
    membersData: {
      doc_count: number;
      key: string;
    }[];
    totalMembers: number;
  };
  countText: string;
};

const CircleChartMembersAge = ({
  title,
  pieChartData,
  countText,
}: CircleChartMembersType) => {
  const { totalMembers: totalCount, membersData: chartData=[] } = pieChartData;
  const generateRandomColor = (num: number) => {
    const hue = (num * 133) % 360;
    const saturation = "45%";
    const lightness = "50%";
    return `hsl(${hue}, ${saturation}, ${lightness})`;
  };

  const [colors, setColors] = useState<string[]>([]);
  useEffect(() => {
    chartData.length &&
      chartData.forEach((entry, index) => {
        const color = pieColors[index];
        setColors((preColor) => {
          return [...(preColor ?? []), color];
        });
      });
  }, [chartData?.length]);

  return (
    <div className="circle-chart">
      <div className="circle-chart__header">
        <p className="circle-chart__headline">{title.toUpperCase()}</p>
      </div>
      <div className="circle-chart__inner">
        <div className="circle-chart__left">
          <PieChart width={192} height={192}>
            <Pie
              data={chartData}
              cx={90}
              cy={90}
              innerRadius={74}
              outerRadius={90}
              dataKey="doc_count"
            >
              {chartData?.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={colors[index]} />
              ))}
            </Pie>
          </PieChart>
          <div className="circle-chart__info">
            <p className="circle-chart__info__label">{countText}</p>
          </div>
        </div>
        <div className="circle-chart__right">
          <ul className="circle-chart__list">
            {chartData.map((item, index) => {
              return (
                <li className="circle-chart__item" key={index}>
                  <span
                    className="circle-chart__point"
                    style={{ backgroundColor: colors[index] }}
                  ></span>
                  <p className="circle-chart__name">
                    {item.key} <span>{totalCount ? Math.round((item.doc_count/totalCount) *100) : 0} %</span>
                  </p>
                </li>
              );
            })}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default memo(CircleChartMembersAge);
