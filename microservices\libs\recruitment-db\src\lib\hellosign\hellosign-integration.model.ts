import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from "sequelize-typescript";
import { ApiProperty } from "@nestjs/swagger";
import { Company } from "../companies/companies.model";

@Table({ tableName: "hellosign-integration", createdAt: true, updatedAt: true })
export class HellosignIntegration extends Model<HellosignIntegration> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: "1", description: "Company  ID" })
  @ForeignKey(() => Company)
  @Column({ type: DataType.INTEGER, allowNull: false })
  companyId: number;

  @ApiProperty({ example: "1", description: "Client  ID" })
  @Column({ type: DataType.STRING, allowNull: false })
  clientId: string;

  @ApiProperty({ example: "1", description: "API Key" })
  @Column({ type: DataType.STRING, allowNull: false })
  key: string;

  @BelongsTo(() => Company)
  company: Company;
}
