@use "../config" as *;
@use "../mixins" as *;

.create-home-task {
  // width: calc(100vw - 131px);

  @include media(xs) {
    width: 100%;
  }

  &__container {
    width: 100%;
    margin-top: 12px;
    height: calc(85vh - 131px);
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 0 33px 0;

    @include media(md) {
      flex-direction: column;
      align-items: start;
      gap: 15px;
    }

    @include media(xs) {
      flex-direction: column;
    }
  }
}

.task-container {
  padding: 36px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #dfe2e6;
  margin: 9rem 0 0 87px;
}

//----------------status-bar-start

*,
*:before,
*:after {
  box-sizing: border-box;
}

.bar-home-container {
  position: relative;

  &:after {
    content: "";
    position: absolute;
    width: 100px;
    height: 92px;
    top: 0;
    left: -100px;
    background: white;
    z-index: 2;
    border-bottom: 1px solid #dfe2e6;
    border-top: 1px solid #dfe2e6;
  }

  &:before {
    content: "";
    position: absolute;
    width: 100px;
    height: 92px;
    top: 0;
    right: -30px;
    background: white;
    z-index: 2;
    border-bottom: 1px solid #dfe2e6;
    border-top: 1px solid #dfe2e6;

    @include media(md) {
      width: 30px;
    }

    @include media(xs) {
      width: 15px;
      right: -16px;
    }
  }
}

.details-home-content-bar {
  position: inherit;
  display: flex;
  width: 100%;
  max-width: 1200px;
  height: 92px;
  background: #ffffff;
  border-top: 1px solid #dfe2e6;
  border-bottom: 1px solid #dfe2e6;
  margin: 0 auto 10px auto;
  justify-content: center;
  flex-wrap: wrap;
  flex-direction: column;
  align-items: center;

  @include media(xs) {
    width: 100%;
    flex-wrap: nowrap;
  }
}

.home-timeline {
  list-style-type: none;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
}

.li-home {
  transition: all 200ms ease-in;
  &:nth-child(2) .status-home:before {
    left: 47%;

    @include media(xs) {
      left: 39%;
    }
  }
  &:last-child .status-home:before {
    left: 94%;

    @include media(xs) {
      left: 79%;
    }
  }
}

.status-home {
  width: 300px;
  padding: 10px 0;
  display: flex;
  justify-content: center;
  border-top: 2px solid #d6dce0;
  position: relative;
  transition: all 200ms ease-in;
  margin-top: 40px;

  &.three {
    @include media(xs) {
      width: 110px;
      margin-top: 20px;

      &-mid {
        width: 110px;
        margin-top: 20px;
      }
    }
  }
}

.text-home-test-timeline {
  display: flex;
  justify-content: space-between;
  width: 910px;
  margin: 0 auto;

  div {
    &:nth-child(2) {
      padding-left: 6%;

      @include media(xs) {
        padding: 0;
      }
    }
    &:nth-child(3) {
      @include media(xs) {
        text-align: right;
      }
    }
  }

  @include media(xs) {
    width: 100%;
  }
}

.text-home-test-timeline h4 {
  font-weight: 400;
  font-size: 14px;
  color: $grayTone4;

  @include media(xs) {
    max-width: 114px;
  }
}

.status-home:before {
  content: "";
  width: 25px;
  height: 25px;
  background-color: $grayTone3;
  border-radius: 25px;
  border: 1px solid $grayTone3;
  position: absolute;
  top: -15px;
  left: 0;
  transition: all 200ms ease-in;
}

//2rd step

.status-home-next:before {
  content: "";
  width: 25px;
  height: 25px;
  background-color: $grayTone3;
  border-radius: 25px;
  border: 1px solid $mainGreen;
  position: absolute;
  top: -15px;
  left: 0;
  transition: all 200ms ease-in;
}

.home-complete-next::before {
  background-color: $white;
  border: 2px solid $mainGreen;
  transition: all 200ms ease-in;
  left: 94%;
}

.home-complete-next .status-home {
  border-top: 2px solid $mainGreen;
}

.li-home.home-complete .status-home {
  border-top: 2px solid $mainGreen;
}

.li-home.home-complete .status-home:before {
  background-color: $white;
  border: 3px solid $mainGreen;
  transition: all 200ms ease-in;
  // left: 0;
}

.li-home.home-complete-next .status-home:before {
  background-color: $white;
  border: 2px solid $mainGreen;
  transition: all 200ms ease-in;
  left: 94%;
}

.li-home.home-complete .status-home h4 {
  color: $mainGreen;
}

//3rd step

.status-home-next-three:before {
  content: "";
  width: 25px;
  height: 25px;
  background-color: $grayTone3;
  border-radius: 25px;
  border: 1px solid $mainGreen;
  position: absolute;
  top: -15px;
  left: 0;
  transition: all 200ms ease-in;
}

.home-complete-next-three::before {
  background-color: $mainGreen;
  border: 2px solid $mainGreen;
  transition: all 200ms ease-in;
  left: 94%;
}

.home-complete-next-three .status-home {
  border-top: 2px solid $mainGreen;
}

.li-home.home-complete-three .status-home {
  border-top: 2px solid $mainGreen;
}

.li-home.home-complete-three .status-home:before {
  background-color: $mainGreen;
  border: 2px solid $mainGreen;
  transition: all 200ms ease-in;
  // left: 0;
}

.li-home.home-complete-next-three .status-home:before {
  background-color: $mainGreen;
  border: 2px solid $mainGreen;
  transition: all 200ms ease-in;
  left: 94%;
}

.li-home.home-complete-three .status-home h4 {
  color: $mainGreen;
}

// 4-step completion states
.home-complete-next-four::before {
  background-color: $mainGreen;
  border: 2px solid $mainGreen;
  transition: all 200ms ease-in;
  left: 96%;
}

.home-complete-next-four .status-home {
  border-top: 2px solid $mainGreen;
}

.li-home.home-complete-four .status-home {
  border-top: 2px solid $mainGreen;
}

.li-home.home-complete-four .status-home:before {
  background-color: $mainGreen;
  border: 2px solid $mainGreen;
  transition: all 200ms ease-in;
}

.li-home.home-complete-next-four .status-home:before {
  background-color: $mainGreen;
  border: 2px solid $mainGreen;
  transition: all 200ms ease-in;
  left: 96%;
}

.li-home.home-complete-four .status-home h4 {
  color: $mainGreen;
}
//----------------status-bar-end

.task-home {
  z-index: -1;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;

  @include media(md) {
    align-items: initial;
  }

  &__container {
    padding: 36px;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #dfe2e6;
    margin: 1rem 0 5rem 2rem;
    width: 910px;
    color: #343b43;
    position: relative;

    @include media(md) {
      width: 100%;
      margin-left: 0;
    }

    @include media(xs) {
      width: 100%;
      margin: 0;
      padding: 0;
      border: 0;
    }

    &__title {
      color: $mainGreen;
      font-weight: 600;
      font-size: 20px;
    }

    &__hint {
      font-size: 12px;
      color: $grayTone4;
      margin-top: 12px;
    }

    &__hint span {
      color: $mainGreen;
    }

    &__inout {
      font-size: 16px;
      margin-top: 25px;
      font-weight: 800;
    }

    &__inout-hint {
      font-size: 14px;
      margin-top: 16px;
    }

    &__group-input {
      display: flex;
      width: 100%;
      justify-content: space-between;

      @include media(xs) {
        flex-direction: column;
      }

      &__basket {
        position: relative;
        width: 48%;

        @include media(xs) {
          width: 100%;
          &:first-child {
            width: calc(100% - 30px);
          }
        }

        &__img {
          position: absolute;
          top: 53px;
          left: -28px;

          svg {
            path {
              transition: 0.2s;
            }
          }

          &:hover {
            svg {
              path {
                stroke: #ad0930;
              }
            }
          }

          @include media(xs) {
            left: initial;
            right: -28px;
          }
        }
      }
    }
    &__group-test-case {
      display: flex;
      width: 100%;
      justify-content: space-between;

      &__basket {
        position: relative;
        width: 100%;
        min-width: 400px;

        @include media(xs) {
          min-width: initial;
        }

        &__img {
          position: absolute;
          top: 53px;
          left: -28px;

          svg {
            path {
              transition: 0.2s;
            }
          }

          &:hover {
            svg {
              path {
                stroke: #ad0930;
              }
            }
          }

          @include media(xs) {
            left: initial;
            right: 0;
          }
        }
      }
    }
  }
  &__test-case {
    display: flex;
    column-gap: 25px;
    flex-wrap: wrap;
    width: 100%;

    @include media(xs) {
      flex-direction: column;
      column-gap: 0;
      flex-wrap: initial;
    }

    &__input:last-child {
      width: 100%;
      min-width: 400px;

      @include media(xs) {
        width: 100%;
        min-width: initial;
      }
    }
    &__input:nth-child(even) {
      width: 48.5%;
      min-width: 400px;

      @include media(xs) {
        width: 100%;
        min-width: initial;
      }
    }
    &__input:nth-child(odd) {
      min-width: 48.5%;

      @include media(xs) {
        min-width: initial;
        width: 100%;
      }
    }

    &__input:first-child {
      @include media(xs) {
        width: calc(100% - 30px);
      }
    }
  }

  &__starter-code {
    &__note {
      color: red;
      font-weight: 600;
      font-style: italic;
    }
  }
}

//----------------top-buttons

.take-home-tasks {
  display: flex;
  margin-bottom: 20px;
  overflow-x: visible;
}

.go-back-container {
  display: flex;
  margin: 0 15px 0 -30px;

  &__back-button {
    display: flex;
    align-items: center;
  }

  &__button-white {
    display: flex;
    flex-direction: row;
    align-items: center;
    min-width: 162px;
    padding: 12px 16px 12px 24px;
    border-right: 1px solid $grayTone2;
    margin-right: 0;
  }

  &__back-img {
    height: 20px;
    width: 12px;
  }
  &__content {
    color: $grayTone4;
    font-size: 14px;
    margin-left: 12px;
    min-width: 102px;
  }
}

.active-class {
  background: $lightGreen !important;
  color: $greenBlue2 !important;
}

.add-question {
  display: flex;
  position: relative;

  &__status {
    height: 19px;
    width: 18px;
    border-radius: 50%;
    margin-right: 10px;
  }
  @include media(xs) {
    width: 100%;
  }

  &__back-button {
    display: flex;
  }
  &__button-white {
    display: flex;
    min-width: 140px;
    flex-direction: row;
    align-items: center;
    padding: 12px 20px;
    color: $grayTone2;
    border-right: 1px solid $grayTone2;
    margin-right: 0;
  }

  &__back-img {
    height: 16px;
  }

  &__title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 110px;

    @include media(xs) {
      max-width: 212px;
    }
  }

  &__button {
    font-size: 14px;
    font-weight: 400;
    background: $grayTone1;
    color: $grayTone7;
    border: 1px solid $grayTone2;
    width: 100%;
    display: flex;
    min-width: 197px;
    flex-direction: row;
    align-items: center;
    padding: 12px 20px;
    border-radius: 4px;
    margin-right: 8px;
    position: relative;
    justify-content: space-between;

    @include media(xs) {
      margin-right: 0;
    }

    &__ol-decimal {
      list-style-type: decimal;
      display: flex;
      align-items: center;
    }

    &__cross-img {
      margin-left: 10px;
      min-width: 10px;
    }

    &__sharp {
      margin-right: 10px;
    }
  }

  &__dropdown-wrapper {
    position: absolute;
    width: 100%;
    display: flex;
    flex-direction: column;
    background: #fff;
    z-index: 2;
    top: 50px;
    padding: 20px;
    border-radius: 4px;
    border: 1px solid $grayTone2;
    box-shadow: 0px 6px 12px rgba(7, 7, 12, 0.08);

    .add-question__button {
      margin-bottom: 25px;
    }
  }
}

.gradient-white:before {
  content: "";
  width: 51px;
  height: 44px;
  position: absolute;
  right: 36px;
  background: linear-gradient(
    -90deg,
    rgba(248, 249, 251, 1) 0%,
    rgba(248, 249, 251, 0.2) 70%,
    rgba(248, 249, 251, 0) 100%
  );
}

.gradient-blue:before {
  content: "";
  width: 51px;
  height: 44px;
  position: absolute;
  right: 36px;
  background: linear-gradient(
    -90deg,
    rgba(172, 216, 209, 1) 0%,
    rgba(172, 216, 209, 0.2) 70%,
    rgba(172, 216, 209, 0) 100%
  );
}

.home-general {
  // width: calc(100vw - 105px);

  @include media(xs) {
    width: 100%;
  }
}

.cross-add-new-question-wrapper {
  display: flex;
  align-items: center;
  min-width: 16px;
  margin-left: 10px;

  .cross-add-new-question {
    width: 16px;
    height: 16px;
  }
}

.questions-limit {
  align-self: center;
  font-size: 14px;
  font-weight: 400;
  min-width: 82px;
}

.cross-add-new-input-case {
  width: 12px;
  margin: 15px 15px 15px 0;
  cursor: pointer;
}

.cross-add-new-input-text {
  font-size: 14px;
  font-weight: 400;
  color: $greenBlue2;
  cursor: pointer;

  &:hover {
    color: $greenBlue1;
  }
}

.delete-input-task {
  width: 20px;
}

.asterisk-sign {
  color: $mainGreen;
}

.two-input-task {
  display: flex;
  width: 100%;
  justify-content: space-between;

  @include media(xs) {
    flex-direction: column;
    align-items: center;
  }
}

.input-button-database {
  display: flex;
  width: 100%;
  justify-content: flex-start;
  gap: 30px;
  align-items: flex-end;

  @include media(xs) {
    flex-direction: column;
    justify-content: space-between;
    gap: 0;
    align-items: center;
  }
}

.input-database {
  width: 48%;

  @include media(xs) {
    width: 100%;
  }
}

//.two-input-task-select-bar {
//  .customSelect {
//    border: 1px solid #DFE2E6 !important;
//
//    &__selected {
//    }
//
//    &__optionsWrapper {
//      &:after {
//      }
//    }
//
//    &__select_box__options_container__option {
//      padding: 12px 15px;
//    }
//    &__select_box__options_container.scroll.active {
//      background: $white;
//    }
//  }
//}

.two-input-task > div {
  width: 48%;

  @include media(xs) {
    width: 100%;
  }
}

.horizontal-line {
  width: 100%;
  background: $grayTone2;
  color: $grayTone2;
  height: 1px;
  border-width: 0;
}

//----------------transition-group

.my-node-enter {
  display: none;
}
.my-node-enter-active {
  opacity: 1;
  transition: opacity 300ms ease-in-out;
}
.my-node-exit {
  opacity: 1;
}

.my-node-exit-active {
  opacity: 0;
  transition: opacity 300ms ease-out;
}

.error-data-one {
  // position: absolute;
  font-size: 12px;
  color: #fe4672;
  margin-top: 4px;
}

.wrapper-class {
  &.error {
    border: 1px solid $red;
  }
}

.button-create-database-wrapper {
  @include media(xs) {
    width: 100%;
  }

  .button-create-database {
    font-size: 14px;
    font-weight: 900;
    line-height: 100%;
    background: $white;
    color: $mainGreen;
    padding: 14px 28px;
    cursor: pointer;
    border-radius: 4px;
    border: 2px solid $mainGreen;
    margin-top: 23px;
    transition: 0.3s;

    &:hover {
      color: $greenBlue2;
      border: 2px solid $greenBlue2;
    }

    @include media(xs) {
      width: 100%;
    }
  }
}

.array-inputs {
  &__wrapper {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    margin-top: 5px;
  }

  &__element {
    display: inline-flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: baseline;
    width: min-content;
    gap: 5px;

    &__lable {
      font-family: "Inter", "Avenir LT Std", sans-serif;
      font-style: normal;
      font-weight: 700;
      font-size: 14px;
      line-height: 19px;
      color: grey;
      display: flex;
      align-items: center;
      gap: 10px;

      & img {
        width: 13px;
        height: auto;
        cursor: pointer;
      }
    }

    &__value {
      resize: vertical;
      border: 1px solid #dfe2e6;
      box-sizing: border-box;
      border-radius: 4px;
      width: 50%;
      line-height: 1;
      padding: 13px 16px;
      font-size: 14px;
      color: #2a2c33;
      width: 100px;
    }
  }
}

// Test Cases Step Two Styles
.ai-generated-banner {
  background-color: #f0f9f6;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #099C73;
}

.ai-banner-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-banner-text {
  color: #099C73;
  font-weight: bold;
  margin-bottom: 4px;
}

.ai-banner-description {
  font-size: 14px;
  color: #333;
}

.regenerate-button {
  background-color: #099C73;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;

  &:hover {
    background-color: #087a5e;
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
}

.test-cases-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;

  th, td {
    padding: 10px;
    border: 1px solid #ddd;
    text-align: left;
  }

  th {
    background-color: #f8f9fa;
    font-weight: bold;
  }
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.icon-button {
  background-color: transparent;
  border: none;
  padding: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;

  &:hover {
    background-color: #f5f5f5;
  }
}

.add-test-case-button {
  background-color: #099C73;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
  min-width: auto;
  width: auto;

  &:hover {
    background-color: #087a5e;
  }
}

.add-test-case-container {
  margin-top: 15px;
  text-align: right;

  &.center {
    text-align: center;
  }
}

.input-field {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.test-case-input-container {
  margin-bottom: 8px;
}

.test-case-input-label {
  font-weight: bold;
  margin-right: 8px;
}
