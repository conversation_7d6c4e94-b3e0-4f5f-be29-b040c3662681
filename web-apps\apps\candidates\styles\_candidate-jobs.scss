@use "config" as *;
@use "mixins" as *;
.candidate-jobs {
  padding: 28px 32px;
  @include media(xs) {
    padding: 24px 16px;
  }

  &__head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28px;

    @include media(sm) {
      display: block;
    }

    &__left {
      display: flex;
      @include media(md) {
        flex-direction: column;
      }
    }

    &__headline {
      line-height: 1;
      margin-right: 40px;
    }

    &__right {
      display: flex;
    }
  }

  &__list {
    display: flex;
    max-width: 864px;
    width: 100%;
    margin: 0 auto;
    padding-bottom: 24px;
  }

  &__buttons {
    // margin-left: 40px;
    display: flex;
    align-items: center;
    @include media(md) {
      // display: none;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 16px;
    }

    &__item {
      padding: 12px 20px;
      border-radius: 4px;
      font-size: 14px;
      line-height: 1;
      border: 1px solid #DFE2E6;
      background: $white;
      transition: .3s ease-in, .3s ease-in-out;
      margin-right: 24px;

      @include media(md) {
        width: 49%;
        margin-right: 0;
      }

      @include media(sm) {
        width: 48%;
        margin-right: 0;
        padding: 12px 10px;
      }


      &:last-child {
        margin-right: 0;
      }

      &.active {
        color: $greenBlue2;
        background: #ACD8D1;
        border: 1px solid #ACD8D1;
      }
    }
  }

  &__action {
    display: flex;
    @include media(md) {
      display: none;
    }

    &__item {
      cursor: pointer;

      &:first-child {
        margin-right: 18px;
      }

      &:hover {
        svg {
          path {
            stroke: $mainGreen;
          }
        }
      }

      svg {
        path {
          transition: .3s ease-in, .3s ease-in-out;
        }
      }

      &.active {
        svg {
          path {
            stroke: $mainGreen;
          }
        }
      }
    }
  }

  .candidate-table {
    &__column {
      &__small {
        width: 200px;
      }

      &__default {
        width: 250px;
      }

      &__middle {
        width: 280px;
      }
    }

    &__link {
      font-size: 12px;
      color: $mainGreen;
      font-weight: 900;

      &:hover {
        text-decoration: underline;
      }
    }

    &__text {
      color: $grayTone7;
      font-size: 14px;
      font-weight: 500;


      &.gray {
        color: $grayTone5;
      }

      &.green {
        color: $mainGreen;
      }

      &.with-dotted {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        display: block;
      }
    }

    &__company {
      width: 100%;
      display: flex;
      align-items: center;

      &__avatar {
        width: 28px;
        height: 28px;
        object-fit: cover;
        border-radius: 50%;
        margin-right: 6px;
      }

      &__text {
        font-size: 14px;
        color: $grayTone7;
        line-height: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        display: flex;
        align-self: center;
      }
    }

    &__location {
      width: 100%;

      &__value {
        font-size: 14px;
        color: $grayTone7;
        line-height: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        display: block;
      }

      &__remove {
        color: $grayTone5;
        font-size: 14px;
        font-weight: 500;
      }
    }

    &__actions {
      display: flex;
      align-items: center;

      &__item {
        width: 20px;
        height: 20px;
        margin-right: 28px;
        cursor: pointer;
        display: flex;

        &.save {
          transition: .3s ease-in, .3s ease-in-out;

          &:hover {
            path {
              stroke: $mainGreen;
            }
          }
        }

        &.active {
          path {
            stroke: $mainGreen;
          }
        }

        &:last-child {
          margin-right: 0;
        }

        &.disabled {
          pointer-events: none;

          svg {
            path, circle {
              stroke: $grayTone5;
            }
          }
        }
      }
    }
  }
}