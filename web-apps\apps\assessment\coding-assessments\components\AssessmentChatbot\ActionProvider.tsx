import React, { useState, useEffect } from "react";
import { createClientMessage } from "react-chatbot-kit";
import { setIsLoading } from "../../store/chatbot/chatAssessmentReducer";
import { store } from "../../store";
import axios from "axios";
import { getEnv } from "@urecruits/api";
import Snackbar from "@mui/material/Snackbar";
import MuiAlert from "@mui/material/Alert";
import { useDispatch } from "react-redux";
import { setAiGeneratedAssessment } from "../../store/assessmentTask/assessmentTask.actions";
import { setAiGeneratedLiveTask } from "../../store/liveTask/liveTask.actions";

interface AssessmentInfo {
  name?: string;
  numQuestions?: number;
  language?: string;
  includeTestCases?: boolean;
  questions?: any[];
  jsonOutput?: any;
}

const ActionProvider = ({
  createChatBotMessage,
  setState,
  children,
  state,
}) => {
  const { API_ASSESSMENT } = getEnv();
  const [conversationHistory, setConversationHistory] = useState([]);
  const [assessmentInfo, setAssessmentInfo] = useState<AssessmentInfo>({
    name: null,
    numQuestions: null,
    language: null,
    includeTestCases: null,
    questions: null,
    jsonOutput: null,
  });
  const [taskType, setTaskType] = useState<
    "live-task" | "take-home-task" | null
  >(null);
  const dispatch = useDispatch();
  const [toastOpen, setToastOpen] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastSeverity, setToastSeverity] = useState<
    "success" | "error" | "info" | "warning"
  >("success");

  useEffect(() => {
    // Get current path
    const path = window.location.pathname;
    if (path.includes("live-task")) {
      setTaskType("live-task");
    } else if (path.includes("take-home-task")) {
      setTaskType("take-home-task");
    }
  }, []);

  const handleToastClose = () => {
    setToastOpen(false);
  };

  const showToast = (
    message: string,
    severity: "success" | "error" | "info" | "warning" = "success"
  ) => {
    setToastMessage(message);
    setToastSeverity(severity);
    setToastOpen(true);
  };

  React.useEffect(() => {
    if (state.messages.length > 0) {
      sessionStorage.setItem(
        "assessment_chatbot_messages",
        JSON.stringify(state.messages)
      );
    }
  }, [state?.messages]);

  const getLanguageJson = () => {
    const data = [
      {
        id: 95,
        name: "Go 1.18.5",
      },
      {
        id: 94,
        name: "TypeScript 5.0.3",
      },
      {
        id: 93,
        name: "JavaScript Node.js 18.15.0",
      },
      {
        id: 92,
        name: "Python 3.11.2",
      },
      {
        id: 91,
        name: "Java JDK 17.0.6",
      },
      {
        id: 90,
        name: "Dart 2.19.2",
      },
      {
        id: 89,
        name: "Multi-file program",
      },
      {
        id: 88,
        name: "Groovy 3.0.3",
      },
      {
        id: 87,
        name: "F# .NET Core SDK 3.1.202",
      },
      {
        id: 86,
        name: "Clojure 1.10.1",
      },
      {
        id: 85,
        name: "Perl 5.28.1",
      },
      {
        id: 84,
        name: "Visual Basic.Net vbnc 0.0.0.5943",
      },
      {
        id: 83,
        name: "Swift 5.2.3",
      },
      {
        id: 82,
        name: "SQL SQLite 3.27.2",
      },
      {
        id: 81,
        name: "Scala 2.13.2",
      },
      {
        id: 80,
        name: "R 4.0.0",
      },
      {
        id: 79,
        name: "Objective-C Clang 7.0.1",
      },
      {
        id: 78,
        name: "Kotlin 1.3.70",
      },
      {
        id: 77,
        name: "COBOL GnuCOBOL 2.2",
      },
      {
        id: 76,
        name: "C++ Clang 7.0.1",
      },
      {
        id: 75,
        name: "C Clang 7.0.1",
      },
      {
        id: 74,
        name: "TypeScript 3.7.4",
      },
      {
        id: 73,
        name: "Rust 1.40.0",
      },
      {
        id: 72,
        name: "Ruby 2.7.0",
      },
      {
        id: 71,
        name: "Python 3.8.1",
      },
      {
        id: 70,
        name: "Python 2.7.17",
      },
      {
        id: 69,
        name: "Prolog GNU Prolog 1.4.5",
      },
      {
        id: 68,
        name: "PHP 7.4.1",
      },
      {
        id: 67,
        name: "Pascal FPC 3.0.4",
      },
      {
        id: 66,
        name: "Octave 5.1.0",
      },
      {
        id: 65,
        name: "OCaml 4.09.0",
      },
      {
        id: 64,
        name: "Lua 5.3.5",
      },
      {
        id: 63,
        name: "JavaScript Node.js 12.14.0",
      },
      {
        id: 62,
        name: "Java OpenJDK 13.0.1",
      },
      {
        id: 61,
        name: "Haskell GHC 8.8.1",
      },
      {
        id: 60,
        name: "Go 1.13.5",
      },
      {
        id: 59,
        name: "Fortran GFortran 9.2.0",
      },
      {
        id: 58,
        name: "Erlang OTP 22.2",
      },
      {
        id: 57,
        name: "Elixir 1.9.4",
      },
      {
        id: 56,
        name: "D DMD 2.089.1",
      },
      {
        id: 55,
        name: "Common Lisp SBCL 2.0.0",
      },
      {
        id: 54,
        name: "C++ GCC 9.2.0",
      },
      {
        id: 53,
        name: "C++ GCC 8.3.0",
      },
      {
        id: 52,
        name: "C++ GCC 7.4.0",
      },
      {
        id: 51,
        name: "C# Mono *********",
      },
      {
        id: 50,
        name: "C GCC 9.2.0",
      },
      {
        id: 49,
        name: "C GCC 8.3.0",
      },
      {
        id: 48,
        name: "C GCC 7.4.0",
      },
      {
        id: 47,
        name: "Basic FBC 1.07.1",
      },
      {
        id: 46,
        name: "Bash 5.0.0",
      },
      {
        id: 45,
        name: "Assembly NASM 2.14.02",
      },
      {
        id: 44,
        name: "Executable",
      },
      {
        id: 43,
        name: "Plain Text",
      },
    ];
    return data;
  };

  const callOpenAI = async (message, history) => {
    try {
      const OPENAI_API_KEY =
        "***************************************************"; // process.env.OPEN_AI_API_KEY;

      if (!OPENAI_API_KEY) {
        return { error: "API key is missing" };
      }

      const languageJson = getLanguageJson();

      const systemMessageForTakeHomeTask = {
        role: "system",
        content: `You are a friendly and smart coding assessment assistant. Your job is to collect necessary details from the user in a conversational way to generate a coding assessment.

Here is the flow you must follow:

1. First collect the name of the assessment.

2. Then ask How many coding questions they want in total

3. Once you have all assessment details and the number of questions, generate that many coding questions.

4. Ask about the package to add 'sqlite jdbc packege' or not.

5. Show the questions to the user and ask if they want to regenerate them or not.

6. Which programming language should be used for ALL questions. Based on the language, use the languageId from the ${languageJson} array.

7. Whether test cases should be included for ALL questions (yes/no) if yes then use the useAIGeneratedTestCases field to true else false.

8. Summarize the complete assessment and ask the user if they want to submit it.

9. IMPORTANT: For ALL your responses, ALWAYS respond in this JSON format:
{
    "message": "Your conversational message to the user",
    "generatedResponse": false,
    "data": null
}
     Only move forward with the next step when you have the previous step's data.
     If you don't have the data, ask the user for the missing information.

10. ONLY when the user explicitly confirms they want to submit the assessment (by saying yes, submit, confirm, etc.), set "generatedResponse" to true and include the assessment data in the "data" field like this:

Example JSON response:
{
    "message": "Great! I've prepared your assessment. Here are the details...",
    "generatedResponse": true,
    "data": {
        "assessmentName": "Matrix Manipulation",
        "assessmentDescription": "Assessment on matrix manipulation with 1 question in Python",
        "questions": [
            {
                "nameQuestion": "matrix",
                "questionDescription": "Write a function to perform matrix operations",
                "languageId": 46,
                "languages":{
                    "id": 46,
                    "name": "Bash (5.0.0)",
                    "languageId": 46
                },
                "packageId": null,
                "packages": {
                    "id": 3,
                    "title": "<put package name if user has opted yes for package else the packageLiveTask will be null>",
                    "createdAt": "<current date and time>",
                    "updatedAt": "<current date and time>"
                },
                "databaseId": 0,
                "isIncludeTestCases": true,
                "useAIGeneratedTestCases": true,
                "candidateInstruction": "Write a function to perform matrix operations",
                "starterCode": "def matrix_operation(matrix):\\n    # Your code here\\n    pass",
                "outputType": "matrix",
                "outputDescription": "The resulting matrix after operations",
                "testCaseInputs": [
                    {
                        "name": "matrix",
                        "type": "Integer Array",
                        "description": "A matrix represented as a 2D array"
                    }
                ],
                "testCases": [
                    {
                        "inputs": [
                            {
                                "name": "matrix",
                                "value": [[1, 2, 3], [4, 5, 6], [7, 8, 9]]
                            }
                        ],
                        "output": [[10, 20, 30], [40, 50, 60], [70, 80, 90]]
                    },
                    {
                        "inputs": [
                            {
                                "name": "matrix",
                                "value": [[5, 2], [1, 3]]
                            }
                        ],
                        "output": [[7, 5], [3, 6]]
                    },
                    {
                        "inputs": [
                            {
                                "name": "matrix",
                                "value": [[0, 0], [0, 0]]
                            }
                        ],
                        "output": [[0, 0], [0, 0]]
                    },
                    {
                        "inputs": [
                            {
                                "name": "matrix",
                                "value": [[9]]
                            }
                        ],
                        "output": [[18]]
                    },
                    {
                        "inputs": [
                            {
                                "name": "matrix",
                                "value": [[-1, -2], [-3, -4]]
                            }
                        ],
                        "output": [[-2, -4], [-6, -8]]
                    }
                ]
            }
        ]
    }
}

IMPORTANT GUIDELINES:
- ALWAYS respond in the JSON format specified above for ALL your responses.
- Do not repeat the same step if already asked.
- Be extremely attentive to information the user has already provided in ANY message.
- Extract information from context aggressively. If the user says "I want to create an assessment on binary trees in Python with 3 questions", extract all that information at once.
- Be conversational but efficient - never ask for information that was already provided.
- Programming language and test case preferences apply to ALL questions in the assessment.
- When generating questions, ensure they match the topic of the assessment.
- Use appropriate languageId values: Python=71, JavaScript=63, Java=62, C++=61, C#=60, Ruby=72
- The "generatedResponse" field should ONLY be true when the user confirms they want to submit the assessment.
- Make sure all JSON is properly formatted and valid.
- For candidateInstruction, provide it as a simple string - it will be converted to EditorState in the application.
- The testCases array should ALWAYS have exactly 5 elements, and EACH ONE must be fully specified with inputs and output properties. DO NOT use empty objects like {} for any test cases!

IMPORTANT GUIDELINES FOR TEST CASES:
- Each question MUST have exactly 5 test cases - no empty objects are allowed
- ALL test cases must have the same format with proper "inputs" array containing objects with "name" and "value" properties
- EVERY test case must have "inputs" and "output" properties - do not leave any test cases as empty objects ({})
- The "output" property should contain the expected result of the test case
- For array/list inputs and outputs, format them as proper arrays ([1,2,3])
- For matrix inputs, format them as nested arrays ([[1,2],[3,4]])
- For string inputs, format them as simple strings (no need for quotes)
- For numeric inputs, format them as numbers
- Each test case must be meaningful and test different scenarios (normal case, edge case, empty input, etc.)
- Do not use the older format with "index" and "id" properties - use the exact format shown above

MOST IMPORTANT RULES:
- Always match the format of the response to the example provided, with no deviations.
- Always return the json data only.
`,
      };

      const systemMessageForLiveTask = {
        role: "system",
        content: `You are a friendly and smart coding assessment assistant. Your job is to collect necessary details from the user in a conversational way to generate a coding assessment.

      Here is the flow you must follow:

      1. First collect the name of the assessment.

      2. Which programming language should be used for Assessment. Based on the language, use the languageId from the ${languageJson} array.

      3. Ask about the package to add 'sqlite jdbc packege' or not.

      4. Summarize the complete assessment and ask the user if they want to submit it.

      5. IMPORTANT: For ALL your responses, ALWAYS respond in this JSON format:
      {
          "message": "Your conversational message to the user",
          "generatedResponse": false,
          "data": null
      }
          Only move forward with the next step when you have the previous step's data.
          If you don't have the data, ask the user for the missing information.

      6. ONLY when the user explicitly confirms they want to submit the assessment (by saying yes, submit, confirm, etc.), set "generatedResponse" to true and include the assessment data in the "data" field like this:

      Example JSON response:
      {
          "message": "Great! I've prepared your assessment. Here are the details...",
          "generatedResponse": true,
          "data": {
              "name": "Matrix Manipulation",
              "description": "Assessment on matrix manipulation with 1 question in Python",
              "languages": {
                          "id": 46,
                          "name": "Bash (5.0.0)",
                          "languageId": 46
              },
              "starterCode": "def matrix_operation(matrix):\\n    # Your code here\\n    pass",
              "instruction": "Write a function to perform matrix operations",
              "packageLiveTask": {
                  "id": 3,
                  "title": "<put package name if user has opted yes for package else the packageLiveTask will be null>",
                  "createdAt": "<current date and time>",
                  "updatedAt": "<current date and time>"
              },
              "database": {
                "id": 0,
                "title": "",
                "packages": null,
                "description": "",
                "script": "",
              },
          }
      }

      IMPORTANT GUIDELINES:
      - ALWAYS respond in the JSON format specified above for ALL your responses.
      - Do not repeat the same step if already asked.
      - Be extremely attentive to information the user has already provided in ANY message.
      - Extract information from context aggressively. If the user says "I want to create an assessment on binary trees in Python with 3 questions", extract all that information at once.
      - Be conversational but efficient - never ask for information that was already provided.
      - Programming language in the assessment.
      - When generating questions starter code and instruction, ensure they match the topic of the assessment.
      - Use appropriate languageId values: Python=71, JavaScript=63, Java=62, C++=61, C#=60, Ruby=72
      - The "generatedResponse" field should ONLY be true when the user confirms they want to submit the assessment.
      - Make sure all JSON is properly formatted and valid.
      - For instruction, provide it as a simple string - it will be converted to EditorState in the application.
     

      MOST IMPORTANT RULES:
      - Always match the format of the response to the example provided, with no deviations.
      - Always return the json data only.
      `,
      };

      let messages = [];
      if (taskType === "take-home-task") {
        messages = [systemMessageForTakeHomeTask, ...history];
      } else if (taskType === "live-task") {
        messages = [systemMessageForLiveTask, ...history];
      }

      const response = await axios.post(
        "https://api.openai.com/v1/chat/completions",
        {
          model: "gpt-4.1",
          messages: messages,
          temperature: 0.7,
          max_tokens: 3000,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${OPENAI_API_KEY}`,
          },
        }
      );

      const assistantMessage = response.data.choices[0].message.content;

      if (
        assistantMessage.trim().startsWith("{") &&
        assistantMessage.trim().endsWith("}")
      ) {
        try {
          const jsonResponse = JSON.parse(assistantMessage);

          if (jsonResponse.message) {
            if (jsonResponse.generatedResponse === true && jsonResponse.data) {
              if (taskType === "take-home-task") {
                const fixedData = {
                  ...jsonResponse.data,
                  questions: jsonResponse.data.questions.map((question) => {
                    if (question.testCases) {
                      question.testCases = question.testCases.map(
                        (testCase, index) => {
                          if (
                            !testCase ||
                            !testCase.inputs ||
                            !Array.isArray(testCase.inputs) ||
                            Object.keys(testCase).length === 0
                          ) {
                            const firstTestCase = question.testCases.find(
                              (tc) =>
                                tc && tc.inputs && Array.isArray(tc.inputs)
                            );

                            if (firstTestCase) {
                              return {
                                inputs: firstTestCase.inputs.map((input) => ({
                                  name: input.name,
                                  value: index === 0 ? input.value : null,
                                })),
                                output:
                                  index === 0 ? firstTestCase.output : null,
                              };
                            } else {
                              return {
                                inputs: [
                                  {
                                    name: "input",
                                    value: index === 0 ? "example" : null,
                                  },
                                ],
                                output: index === 0 ? "example_output" : null,
                              };
                            }
                          }

                          return testCase;
                        }
                      );
                    }

                    return question;
                  }),
                };

                dispatch(setAiGeneratedAssessment(fixedData));
                showToast("Assessment data received successfully!", "success");
                return {
                  message: jsonResponse.message,
                  assessmentInfo: null,
                  jsonData: fixedData,
                  isSubmission: true,
                };
              } else if (taskType === "live-task") {
                dispatch(setAiGeneratedLiveTask(jsonResponse.data));

                showToast("Assessment data received successfully!", "success");
                return {
                  message: jsonResponse.message,
                  jsonData: jsonResponse.data,
                };
              }
            }

            return {
              message: jsonResponse.message,
              assessmentInfo: jsonResponse.assessmentInfo || null,
            };
          }
        } catch (error) {}
      }

      const extractedInfo = extractAssessmentInfo("", assistantMessage);

      const isSubmissionMessage =
        assistantMessage.toLowerCase().includes("submit") &&
        (assistantMessage.toLowerCase().includes("assessment") ||
          assistantMessage.includes("question")) &&
        assistantMessage.toLowerCase().includes("successful");

      if (isSubmissionMessage && extractedInfo) {
        showToast("Assessment data received successfully!", "success");
        return {
          message: assistantMessage,
          assessmentInfo: extractedInfo,
          isSubmission: true,
        };
      }

      return {
        message: assistantMessage,
        assessmentInfo: extractedInfo,
      };
    } catch (error) {
      return { error: "Failed to communicate with OpenAI" };
    }
  };

  const extractAssessmentInfo = (
    aiResponse: string,
    userMessage: string
  ): AssessmentInfo | null => {
    const info: AssessmentInfo = {};

    if (
      userMessage.toLowerCase().includes("assessment") ||
      userMessage.toLowerCase().includes("create") ||
      userMessage.toLowerCase().includes("questions about") ||
      userMessage.toLowerCase().includes("questions on") ||
      aiResponse.toLowerCase().includes("name of your assessment")
    ) {
      const topicMatch =
        userMessage.match(/assessment (?:about|on|for) ([\w\s]+)/i) ||
        userMessage.match(/create (?:an?|the) ([\w\s]+) assessment/i) ||
        userMessage.match(/create assessment (?:about|on|for) ([\w\s]+)/i) ||
        userMessage.match(/questions (?:about|on|for) ([\w\s]+)/i) ||
        userMessage.match(
          /(?:about|on|for) ([\w\s]+) (?:questions|problems|tasks)/i
        );

      if (topicMatch && topicMatch[1].length > 2) {
        info.name = topicMatch[1].trim() + " Assessment";
      } else {
        const nameMatch =
          userMessage.match(/['"](.*?)['"]/) || userMessage.match(/([\w\s]+)/);
        if (nameMatch && nameMatch[1].length > 2) {
          info.name = nameMatch[1].trim();
        }
      }
    }

    const numQuestionsMatch =
      userMessage.match(/(\d+)\s*(?:question|problem|task|challenge)/i) ||
      userMessage.match(/(\d+)/);

    if (
      numQuestionsMatch &&
      (aiResponse.toLowerCase().includes("question") ||
        userMessage.toLowerCase().includes("question") ||
        userMessage.toLowerCase().includes("problem") ||
        userMessage.toLowerCase().includes("task") ||
        userMessage.toLowerCase().includes("challenge"))
    ) {
      info.numQuestions = parseInt(numQuestionsMatch[1]);
    }

    const languageRegex =
      /(javascript|typescript|python|java|c\+\+|c#|csharp|ruby|go|golang|php|swift|kotlin|rust|scala|r|perl|shell|bash|sql|html|css)/i;
    const languageMatch = userMessage.match(languageRegex);
    if (languageMatch) {
      let language = languageMatch[0].toLowerCase();

      if (language === "csharp") language = "c#";
      if (language === "golang") language = "go";

      info.language = language;
    }

    if (
      aiResponse.toLowerCase().includes("test case") ||
      userMessage.toLowerCase().includes("test case")
    ) {
      const positiveResponses = [
        "yes",
        "yeah",
        "yep",
        "sure",
        "ok",
        "okay",
        "include",
        "with test",
        "with tests",
      ];
      const negativeResponses = [
        "no",
        "nope",
        "don't",
        "dont",
        "without",
        "exclude",
      ];

      const hasPositive = positiveResponses.some((response) =>
        userMessage.toLowerCase().includes(response)
      );
      const hasNegative = negativeResponses.some((response) =>
        userMessage.toLowerCase().includes(response)
      );

      if (hasPositive && !hasNegative) {
        info.includeTestCases = true;
      } else if (hasNegative && !hasPositive) {
        info.includeTestCases = false;
      }
    }

    return Object.keys(info).length > 0 ? info : null;
  };

  const handleChatResponse = async (messageStr) => {
    const loadingMsg = createChatBotMessage("...", {
      loading: true,
      delay: 1000,
    });
    setState((prev) => ({ ...prev, messages: [...prev.messages, loadingMsg] }));

    try {
      const directInfo = extractAssessmentInfo("", messageStr);
      if (directInfo) {
        setAssessmentInfo({ ...assessmentInfo, ...directInfo });
      }

      const updatedHistory = [
        ...conversationHistory,
        { role: "user", content: messageStr },
      ];
      setConversationHistory(updatedHistory);

      const currentInfo = { ...assessmentInfo, ...directInfo };
      if (
        currentInfo.name &&
        currentInfo.numQuestions &&
        (messageStr.toLowerCase().includes("generate") ||
          messageStr.toLowerCase().includes("create") ||
          messageStr.toLowerCase().includes("make")) &&
        !currentInfo.questions
      ) {
        const questions = generateQuestions(
          currentInfo.name || "",
          "medium",
          currentInfo.numQuestions || 1
        );

        setAssessmentInfo({ ...currentInfo, questions });

        let questionsMessage =
          "Here are the generated questions for your assessment:\n\n";
        questions.forEach((q, i) => {
          questionsMessage += `${i + 1}. ${q.title}: ${q.description}\n\n`;
        });

        questionsMessage +=
          "Do you want to use these questions or regenerate them?";

        const aiMessage = createChatBotMessage(questionsMessage, {
          delay: 1000,
        });
        setState((prev) => ({
          ...prev,
          messages: [...prev.messages.slice(0, -1), aiMessage],
        }));

        setConversationHistory([
          ...updatedHistory,
          {
            role: "assistant",
            content: questionsMessage,
          },
        ]);

        return;
      }

      const response = await callOpenAI(messageStr, updatedHistory);

      if (response && response.message) {
        setConversationHistory([
          ...updatedHistory,
          { role: "assistant", content: response.message },
        ]);

        const aiMessage = createChatBotMessage(response.message, {
          delay: 1000,
        });
        setState((prev) => ({
          ...prev,
          messages: [...prev.messages.slice(0, -1), aiMessage],
        }));

        if (response.isSubmission && response.jsonData) {
          setAssessmentInfo({ ...currentInfo, jsonOutput: response.jsonData });
          showToast("Assessment data received successfully!", "success");
        } else if (response.assessmentInfo) {
          const newInfo = { ...assessmentInfo, ...response.assessmentInfo };
          setAssessmentInfo(newInfo);
        } else {
          const extractedInfo = extractAssessmentInfo(
            response.message,
            messageStr
          );

          if (extractedInfo) {
            const newInfo = { ...assessmentInfo, ...extractedInfo };
            setAssessmentInfo(newInfo);
          }
        }
      } else if (response && response.error) {
        setState((prev) => ({
          ...prev,
          messages: [
            ...prev.messages.slice(0, -1),
            createChatBotMessage(`Error: ${response.error}. Please try again.`),
          ],
        }));

        showToast(`Error: ${response.error}`, "error");
      } else {
        setState((prev) => ({
          ...prev,
          messages: [
            ...prev.messages.slice(0, -1),
            createChatBotMessage(
              "I'm not sure how to respond to that. Could you try rephrasing?"
            ),
          ],
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        messages: [
          ...prev.messages.slice(0, -1),
          createChatBotMessage(
            "Sorry, I encountered an error. Please try again."
          ),
        ],
      }));

      showToast("An error occurred while processing your request", "error");
    }
  };

  const handleLiveTaskChatResponse = async (messageStr) => {
    const loadingMsg = createChatBotMessage("...", {
      loading: true,
      delay: 1000,
    });
    setState((prev) => ({ ...prev, messages: [...prev.messages, loadingMsg] }));

    try {
      const directInfo = extractAssessmentInfo("", messageStr);
      if (directInfo) {
        setAssessmentInfo({ ...assessmentInfo, ...directInfo });
      }

      const updatedHistory = [
        ...conversationHistory,
        { role: "user", content: messageStr },
      ];
      setConversationHistory(updatedHistory);

      const currentInfo = { ...assessmentInfo, ...directInfo };
      if (
        currentInfo.name &&
        currentInfo.numQuestions &&
        (messageStr.toLowerCase().includes("generate") ||
          messageStr.toLowerCase().includes("create") ||
          messageStr.toLowerCase().includes("make")) &&
        !currentInfo.questions
      ) {
        const questions = generateQuestions(
          currentInfo.name || "",
          "medium",
          currentInfo.numQuestions || 1
        );

        setAssessmentInfo({ ...currentInfo, questions });

        return;
      }

      const response = await callOpenAI(messageStr, updatedHistory);

      if (response && response.message) {
        setConversationHistory([
          ...updatedHistory,
          { role: "assistant", content: response.message },
        ]);

        const aiMessage = createChatBotMessage(response.message, {
          delay: 1000,
        });
        setState((prev) => ({
          ...prev,
          messages: [...prev.messages.slice(0, -1), aiMessage],
        }));

        if (response.isSubmission && response.jsonData) {
          setAssessmentInfo({ ...currentInfo, jsonOutput: response.jsonData });
          showToast("Assessment data received successfully!", "success");
        } else if (response.assessmentInfo) {
          const newInfo = { ...assessmentInfo, ...response.assessmentInfo };
          setAssessmentInfo(newInfo);
        } else {
          const extractedInfo = extractAssessmentInfo(
            response.message,
            messageStr
          );

          if (extractedInfo) {
            const newInfo = { ...assessmentInfo, ...extractedInfo };
            setAssessmentInfo(newInfo);
          }
        }
      } else if (response && response.error) {
        setState((prev) => ({
          ...prev,
          messages: [
            ...prev.messages.slice(0, -1),
            createChatBotMessage(`Error: ${response.error}. Please try again.`),
          ],
        }));

        showToast(`Error: ${response.error}`, "error");
      } else {
        setState((prev) => ({
          ...prev,
          messages: [
            ...prev.messages.slice(0, -1),
            createChatBotMessage(
              "I'm not sure how to respond to that. Could you try rephrasing?"
            ),
          ],
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        messages: [
          ...prev.messages.slice(0, -1),
          createChatBotMessage(
            "Sorry, I encountered an error. Please try again."
          ),
        ],
      }));

      showToast("An error occurred while processing your request", "error");
    }
  };

  const resetConversation = () => {
    setConversationHistory([]);
    setAssessmentInfo({});

    setState((state) => ({ ...state, messages: [] }));

    const initialMessage = createChatBotMessage(
      "Hello! I'm your coding assessment assistant. I'll help you create a coding assessment. Just tell me what you need!",
      {}
    );
    setState((state) => ({
      ...state,
      messages: [...state.messages, initialMessage],
    }));
  };

  const generateQuestions = (
    topic: string,
    difficulty: string,
    count: number
  ): any[] => {
    // This is a simplified implementation - in a real application, you would use OpenAI or another service
    // to generate actual questions based on the topic, difficulty, and count

    const questions = [];
    const topics = {
      "binary search tree": [
        {
          title: "BST Insertion",
          description:
            "Implement a function to insert a node into a Binary Search Tree while maintaining the BST property.",
        },
        {
          title: "BST Validation",
          description:
            "Write a function to determine if a given binary tree is a valid Binary Search Tree.",
        },
        {
          title: "BST Traversal",
          description:
            "Implement in-order, pre-order, and post-order traversals of a Binary Search Tree.",
        },
        {
          title: "Find Closest Value in BST",
          description:
            "Write a function that finds the closest value to a given target value in a BST.",
        },
        {
          title: "BST Deletion",
          description:
            "Implement a function to delete a node from a Binary Search Tree while maintaining the BST property.",
        },
      ],
      "dynamic programming": [
        {
          title: "Fibonacci Sequence",
          description:
            "Implement the Fibonacci sequence using dynamic programming to optimize performance.",
        },
        {
          title: "Longest Common Subsequence",
          description:
            "Find the longest common subsequence between two strings using dynamic programming.",
        },
        {
          title: "Knapsack Problem",
          description:
            "Solve the 0/1 knapsack problem using dynamic programming.",
        },
        {
          title: "Coin Change Problem",
          description:
            "Find the minimum number of coins needed to make a specific amount using dynamic programming.",
        },
        {
          title: "Longest Increasing Subsequence",
          description:
            "Find the length of the longest increasing subsequence in an array using dynamic programming.",
        },
      ],
      array: [
        {
          title: "Two Sum",
          description:
            "Find two numbers in an array that add up to a specific target.",
        },
        {
          title: "Maximum Subarray",
          description: "Find the contiguous subarray with the largest sum.",
        },
        {
          title: "Merge Sorted Arrays",
          description: "Merge two sorted arrays into a single sorted array.",
        },
        {
          title: "Product of Array Except Self",
          description:
            "Calculate the product of all elements except the current one without using division.",
        },
        {
          title: "Container With Most Water",
          description:
            "Find two lines that together with the x-axis form a container that holds the most water.",
        },
      ],
      string: [
        {
          title: "Valid Anagram",
          description: "Determine if two strings are anagrams of each other.",
        },
        {
          title: "Longest Substring Without Repeating Characters",
          description:
            "Find the length of the longest substring without repeating characters.",
        },
        {
          title: "String to Integer (atoi)",
          description:
            "Implement a function that converts a string to an integer.",
        },
        {
          title: "Valid Palindrome",
          description:
            "Determine if a string is a valid palindrome considering only alphanumeric characters.",
        },
        {
          title: "Group Anagrams",
          description:
            "Group a list of strings such that all anagrams are in the same group.",
        },
      ],
      "linked list": [
        {
          title: "Reverse Linked List",
          description: "Reverse a singly linked list.",
        },
        {
          title: "Detect Cycle",
          description: "Determine if a linked list has a cycle.",
        },
        {
          title: "Merge Two Sorted Lists",
          description:
            "Merge two sorted linked lists into a single sorted linked list.",
        },
        {
          title: "Remove Nth Node From End",
          description: "Remove the nth node from the end of a linked list.",
        },
        {
          title: "Linked List Intersection",
          description: "Find the node where two linked lists intersect.",
        },
      ],
      graph: [
        {
          title: "Breadth-First Search",
          description:
            "Implement BFS to traverse a graph and find the shortest path between two nodes.",
        },
        {
          title: "Depth-First Search",
          description: "Implement DFS to traverse a graph and detect cycles.",
        },
        {
          title: "Dijkstra's Algorithm",
          description:
            "Implement Dijkstra's algorithm to find the shortest path in a weighted graph.",
        },
        {
          title: "Topological Sort",
          description:
            "Implement a topological sorting algorithm for a directed acyclic graph.",
        },
        {
          title: "Minimum Spanning Tree",
          description:
            "Implement Kruskal's or Prim's algorithm to find the minimum spanning tree of a graph.",
        },
      ],
      sorting: [
        {
          title: "Merge Sort",
          description: "Implement the merge sort algorithm to sort an array.",
        },
        {
          title: "Quick Sort",
          description: "Implement the quick sort algorithm to sort an array.",
        },
        {
          title: "Heap Sort",
          description: "Implement the heap sort algorithm to sort an array.",
        },
        {
          title: "Counting Sort",
          description:
            "Implement the counting sort algorithm for integers with a limited range.",
        },
        {
          title: "Bucket Sort",
          description:
            "Implement the bucket sort algorithm for floating-point numbers.",
        },
      ],
      searching: [
        {
          title: "Binary Search",
          description:
            "Implement binary search to find an element in a sorted array.",
        },
        {
          title: "Search in Rotated Sorted Array",
          description: "Search for a target value in a rotated sorted array.",
        },
        {
          title: "Search a 2D Matrix",
          description:
            "Search for a value in an m x n matrix with sorted rows and columns.",
        },
        {
          title: "Find Peak Element",
          description:
            "Find a peak element in an array where adjacent elements are different.",
        },
        {
          title: "Search Range",
          description:
            "Find the starting and ending position of a target value in a sorted array.",
        },
      ],
    };

    // Find the most relevant topic
    let bestMatch = "array"; // Default
    for (const key of Object.keys(topics)) {
      if (topic.toLowerCase().includes(key)) {
        bestMatch = key;
        break;
      }
    }

    // Get questions for the topic
    const availableQuestions = topics[bestMatch] || topics["array"];

    // Return the requested number of questions
    for (let i = 0; i < Math.min(count, availableQuestions.length); i++) {
      questions.push(availableQuestions[i]);
    }

    return questions;
  };

  const actions = {
    greet: () => {},
    sendClientMessage: (messageStr) => {
      const message = createClientMessage(messageStr, { delay: 1000 });
      setState((prev) => ({ ...prev, messages: [...prev.messages, message] }));
    },
    resetState: () => {
      resetConversation();
    },
    chatConversation: async (messageStr) => {
      if (messageStr) {
        store.dispatch(setIsLoading(true));
        if (taskType === "live-task") {
          await handleLiveTaskChatResponse(messageStr);
        } else if (taskType === "take-home-task") {
          await handleChatResponse(messageStr);
        }
        store.dispatch(setIsLoading(false));
      }
    },
  };

  return (
    <div>
      {React.Children.map(children, (child) =>
        React.cloneElement(child, { actions })
      )}
      <Snackbar
        open={toastOpen}
        autoHideDuration={6000}
        onClose={handleToastClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <MuiAlert
          elevation={6}
          variant="filled"
          onClose={handleToastClose}
          severity={toastSeverity}
          sx={{
            backgroundColor:
              toastSeverity === "success" ? "#099C73" : undefined,
          }}
        >
          {toastMessage}
        </MuiAlert>
      </Snackbar>
    </div>
  );
};

export default ActionProvider;
