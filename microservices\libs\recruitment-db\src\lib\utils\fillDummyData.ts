
export const getDummyJobs = (companyId: number, authorId: number, employer: string) => [
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 523,
        "authorId": authorId,
        "title": "Business Analyst",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": true,
        "salaryMonthMin": 4500,
        "salaryMonthMax": 8500,
        "salaryHourMin": 28.13,
        "salaryHourMax": 53.13,
        "salaryYearMin": 54000,
        "salaryYearMax": 102000,
        "negotiable": true,
        "numberOpenings": "2",
        "jobType": "Full-Time Employees",
        "description": "<p>We are looking for a detail-oriented Business Analyst to join our team. The ideal candidate will be responsible for analyzing business needs, identifying solutions, and collaborating with stakeholders to enhance operational efficiency.</p><p><strong>Responsibilities:</strong></p><ul><li>Gather and document business requirements through stakeholder meetings and data analysis.</li><li>Analyze existing processes and recommend improvements to optimize business operations.</li><li>Collaborate with technical teams to translate business requirements into functional solutions.</li><li>Create reports, dashboards, and visual presentations to communicate findings.</li><li>Support project teams by identifying risks, dependencies, and potential roadblocks.</li><li>Ensure business solutions align with company goals and industry standards.</li></ul>",
        "shortDescription": "Analyze business processes, gather requirements, and collaborate with stakeholders to optimize operations.",
        "preferableShift": "General Shift",
        "industryId": 1,
        "functionalArea": "Executive Management",
        "noticePeriod": "Immediately",
        "skills": ["Business Analysis", "Requirements Gathering", "Process Mapping", "Data Analysis", "Stakeholder Management"],
        "experienceMin": 2,
        "experienceMax": 5,
        "education": "Bachelor's degree",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733120, "value": "Comprehensive health, dental, and vision insurance with flexible plans and wellness programs." },
            { "id": 1739169733121, "value": "401(k) matching up to 6% with additional financial planning resources." },
            { "id": 1739169733126, "value": "Professional development programs, including certifications and tuition reimbursement." },
            { "id": 1739169733127, "value": "Generous paid time off (PTO) policy, including vacation, sick leave, and personal days." }
        ],
        "careerPage": true,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [
            {
              "id": 1,
              "name": "Application Question",
              "question": [
                {
                  "id": 1,
                  "name": "Are you 18 years of age or older?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 2,
                  "name": "Are you legally authorized to work in the United States?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 3,
                  "name": "Will you now, or in the future, require sponsorship for employment visa status (e.g., H1B or TN visa status)?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 4,
                  "name": "Have you ever been terminated or asked to resign, or resigned to avoid termination from any former employer? This includes military service or prior employment with Urecruits.",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 5,
                  "name": "Have you been a contract or temporary employee with xxx(Company Name) in the last year?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 6,
                  "name": "Do you have any friends or relatives currently employed by the Urecruits or any of our affiliates?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 7,
                  "name": "To identify potential conflicts of interest, are you employed by or do you serve on the board of, or hold an elected or appointed position with a government or government related agency?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 8,
                  "name": "Do you currently hold an active life/health insurance license (including annuities)?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 9,
                  "name": "Are you currently subject to any agreement (including, but not limited to, restrictive convenants) with an employer, vendor or government entity that would limit or restrict, in any way, your activities or functions as a Urecruits employee?\n",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 10,
                  "name": "Each time you apply to a position within Urecruits, or any of our affiliates, we ask that you enter your unique identifier, also referred to as an eSignature - which is the equivalent to your handwritten signature. This eSignature certifies that all information on this application is true, correct and complete. It also certifies that you have read and accept the Terms and Conditions outlined on this application. If you have any questions regarding this statement, contact us 1-877-968-7762. Your unique identifier is your month and day of birth followed by the zip code of your residence. For example, enter 010155555 if you were born on January 1 and live in zip code 55555.",
                  "type": "freetextfield",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 11,
                  "name": "Please provide reasons for leaving previous employers. If no previous employment, please state that this is your first employment opportunity.\n",
                  "type": "freetextfield",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 12,
                  "name": "Please provide your salary expectations for this position.",
                  "type": "freetextfield",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                }
              ],
              "activeTab": true
            },
            {
              "id": 2,
              "name": "Voluntary Disclosures",
              "question": [
                {
                  "id": 1,
                  "name": "Gender\n",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "Declined to self identify"
                    },
                    {
                      "id": 2,
                      "name": "Male"
                    },
                    {
                      "id": 3,
                      "name": "Female"
                    },
                    {
                      "id": 4,
                      "name": "Unknown"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 2,
                  "name": "Veteran Status\n",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "I am a protected Veteran"
                    },
                    {
                      "id": 2,
                      "name": "I am not a protected Veteran"
                    },
                    {
                      "id": 3,
                      "name": "I am a Veteran but not a protected veteran"
                    },
                    {
                      "id": 4,
                      "name": "I wish to not self identify"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 3,
                  "name": "Race\n",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "American Indian or Alaska Native (Not Hispanic or Latino) (United States of America)"
                    },
                    {
                      "id": 2,
                      "name": "Asian (Not Hispanic or Latino) (United States of America)"
                    },
                    {
                      "id": 3,
                      "name": "Black or African American (Not Hispanic or Latino) (United States of America)"
                    },
                    {
                      "id": 4,
                      "name": "Declined to Self Identify (United States of America)"
                    },
                    {
                      "id": 5,
                      "name": "Hispanic or Latino (United States of America)"
                    },
                    {
                      "id": 6,
                      "name": "Native Hawaiian or Other Pacific Islander (Not Hispanic or Latino) (United States of America)"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                }
              ],
              "activeTab": true
            },
            {
              "id": 3,
              "name": "Disability",
              "question": [
                {
                  "id": 1,
                  "name": "Voluntary Self-Identification of Disability",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "Yes, I Have A Disability, Or Have A History/Record Of Having A Disability"
                    },
                    {
                      "id": 2,
                      "name": "No, I Don't Have A Disability, Or A History/Record Of Having A Disability"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                }
              ],
              "activeTab": true
            }
          ],
        "status": "draft",
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 3601,
        "authorId": authorId,
        "title": "Project Coordinator",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": true,
        "salaryMonthMin": 3500,
        "salaryMonthMax": 6500,
        "salaryHourMin": 21.88,
        "salaryHourMax": 40.63,
        "salaryYearMin": 42000,
        "salaryYearMax": 78000,
        "negotiable": true,
        "numberOpenings": "1",
        "jobType": "Full-Time Employees",
        "description": "<p>We are seeking an organized and proactive Project Coordinator to support project teams in achieving successful outcomes. This role involves coordinating resources, tracking progress, and ensuring seamless communication across all project stakeholders.</p><p><strong>Responsibilities:</strong></p><ul><li>Assist in the development and execution of project plans, ensuring deadlines are met.</li><li>Coordinate resources, schedules, and deliverables with project stakeholders.</li><li>Monitor project progress and provide regular updates to project managers.</li><li>Maintain project documentation, reports, and risk logs.</li><li>Facilitate effective communication between teams and stakeholders.</li><li>Identify potential risks and propose mitigation strategies.</li></ul>",
        "shortDescription": "Support project teams by managing schedules, tracking progress, and ensuring effective communication.",
        "preferableShift": "General Shift",
        "industryId": 2,
        "functionalArea": "Strategic Planning",
        "noticePeriod": "1 Month",
        "skills": ["Project Coordination", "Resource Management", "Documentation", "Communication", "Time Management"],
        "experienceMin": 1,
        "experienceMax": 3,
        "education": "Bachelor's degree",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733122, "value": "Competitive health, dental, and vision insurance with family coverage options." },
            { "id": 1739169733123, "value": "401(k) retirement plan with company match and investment guidance." },
            { "id": 1739169733128, "value": "Annual performance bonuses and career growth opportunities." },
            { "id": 1739169733129, "value": "Flexible work arrangements, including remote work options." }
        ],
        "careerPage": true,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [],
        "status": "draft",
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 2161,
        "authorId": authorId,
        "title": "HR Generalist",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": false,
        "salaryMonthMin": 4000,
        "salaryMonthMax": 7000,
        "salaryHourMin": 25.00,
        "salaryHourMax": 43.75,
        "salaryYearMin": 48000,
        "salaryYearMax": 84000,
        "negotiable": true,
        "numberOpenings": "2",
        "jobType": "Full-Time Employees",
        "description": "<p>As an HR Generalist, you will be responsible for managing various human resources functions to ensure a productive and compliant workplace. Your role will involve recruitment, employee relations, performance management, training coordination, and HR compliance.</p><p><strong>Responsibilities:</strong></p><ul><li>Oversee end-to-end recruitment, onboarding, and employee lifecycle management.</li><li>Manage employee relations, address grievances, and ensure a positive work environment.</li><li>Administer compensation, benefits, and payroll-related queries.</li><li>Develop and implement HR policies in compliance with labor laws.</li><li>Coordinate training programs and career development initiatives.</li><li>Ensure HRIS (Human Resource Information System) is accurately maintained.</li></ul>",
        "shortDescription": "Manage recruitment, employee relations, compliance, and HR operations to support a productive workplace.",
        "preferableShift": "General Shift",
        "industryId": 3,
        "functionalArea": "Human Resources (HR)",
        "noticePeriod": "1 Month",
        "skills": ["HR Administration", "Recruitment", "Employee Relations", "HRIS", "Compliance"],
        "experienceMin": 2,
        "experienceMax": 4,
        "education": "Bachelor's degree",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733122, "value": "Competitive health, dental, and vision insurance with family coverage options." },
            { "id": 1739169733123, "value": "401(k) retirement plan with company match and investment guidance." },
            { "id": 1739169733128, "value": "Annual performance bonuses and career growth opportunities." },
            { "id": 1739169733129, "value": "Flexible work arrangements, including remote work options." }
        ],
        "careerPage": true,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [],
        "status": "draft",
        "approverId": authorId,
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 3121,
        "authorId": authorId,
        "title": "Operations Specialist",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": false,
        "salaryMonthMin": 3800,
        "salaryMonthMax": 6800,
        "salaryHourMin": 23.75,
        "salaryHourMax": 42.50,
        "salaryYearMin": 45600,
        "salaryYearMax": 81600,
        "negotiable": true,
        "numberOpenings": "1",
        "jobType": "Full-Time Employees",
        "description": "<p>As an Operations Specialist, you will play a key role in optimizing business processes and ensuring smooth daily operations. You will analyze workflows, implement process improvements, and coordinate with cross-functional teams.</p><p><strong>Responsibilities:</strong></p><ul><li>Analyze and improve operational processes for increased efficiency.</li><li>Develop standard operating procedures (SOPs) and workflows.</li><li>Coordinate with various departments to ensure smooth business operations.</li><li>Monitor key performance indicators (KPIs) and drive continuous improvement.</li><li>Identify operational risks and recommend mitigation strategies.</li></ul>",
        "shortDescription": "Drive operational excellence by analyzing workflows, optimizing processes, and ensuring seamless business operations.",
        "preferableShift": "General Shift",
        "industryId": 4,
        "functionalArea": "Facilities Management",
        "noticePeriod": "Immediately",
        "skills": ["Process Optimization", "Operations Management", "Team Coordination", "Problem Solving", "Analytics"],
        "experienceMin": 2,
        "experienceMax": 4,
        "education": "Bachelor's degree",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733122, "value": "Competitive health, dental, and vision insurance with family coverage options." },
            { "id": 1739169733123, "value": "401(k) retirement plan with company match and investment guidance." },
            { "id": 1739169733128, "value": "Annual performance bonuses and career growth opportunities." },
            { "id": 1739169733129, "value": "Flexible work arrangements, including remote work options." }
        ],
        "careerPage": true,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [],
        "status": "draft",
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 2740,
        "authorId": authorId,
        "title": "Marketing Coordinator",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": true,
        "salaryMonthMin": 3500,
        "salaryMonthMax": 6500,
        "salaryHourMin": 21.88,
        "salaryHourMax": 40.63,
        "salaryYearMin": 42000,
        "salaryYearMax": 78000,
        "negotiable": true,
        "numberOpenings": "1",
        "jobType": "Full-Time Employees",
        "description": "<p>As a Marketing Coordinator, you will support the execution of marketing strategies and campaigns. Your role involves content creation, campaign management, and analyzing marketing performance to enhance brand visibility.</p><p><strong>Responsibilities:</strong></p><ul><li>Assist in developing and executing marketing campaigns.</li><li>Manage content creation for social media, blogs, and promotional materials.</li><li>Track and analyze campaign performance to improve future marketing efforts.</li><li>Coordinate with design, content, and sales teams for marketing initiatives.</li><li>Maintain marketing databases and ensure brand consistency.</li></ul>",
        "shortDescription": "Support marketing efforts through campaign management, content creation, and performance analysis.",
        "preferableShift": "General Shift",
        "industryId": 5,
        "functionalArea": "Sales and Marketing",
        "noticePeriod": "Within 15 days",
        "skills": ["Marketing Coordination", "Content Management", "Campaign Management", "Digital Marketing", "Analytics"],
        "experienceMin": 1,
        "experienceMax": 3,
        "education": "Bachelor's degree",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733122, "value": "Competitive health, dental, and vision insurance with family coverage options." },
            { "id": 1739169733123, "value": "401(k) retirement plan with company match and investment guidance." },
            { "id": 1739169733128, "value": "Annual performance bonuses and career growth opportunities." },
            { "id": 1739169733129, "value": "Flexible work arrangements, including remote work options." }
        ],
        "careerPage": true,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [],
        "status": "draft",
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 1186,
        "authorId": authorId,
        "title": "Customer Service Representative",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": true,
        "salaryMonthMin": 3000,
        "salaryMonthMax": 5000,
        "salaryHourMin": 18.75,
        "salaryHourMax": 31.25,
        "salaryYearMin": 36000,
        "salaryYearMax": 60000,
        "negotiable": true,
        "numberOpenings": "2",
        "jobType": "Full-Time Employees",
        "description": "<p>As a Customer Service Representative, you will be the first point of contact for our customers. Your primary responsibility will be to provide timely and professional support, resolve inquiries, and ensure customer satisfaction. You will handle phone, email, and chat interactions while maintaining accurate records in CRM systems.</p><p><br></p><p><strong>Key Responsibilities:</strong></p><ul><li>Respond to customer inquiries via phone, email, and live chat.</li><li>Resolve customer issues efficiently while maintaining a positive attitude.</li><li>Maintain accurate records of customer interactions and transactions.</li><li>Collaborate with internal teams to escalate complex issues when necessary.</li><li>Stay up to date with company policies and product knowledge to provide accurate information.</li></ul>",
        "shortDescription": "Provide top-tier customer support through calls, emails, and chats while ensuring client satisfaction.",
        "preferableShift": "General Shift",
        "industryId": 19,
        "functionalArea": "Customer Service",
        "noticePeriod": "Immediately",
        "skills": ["Customer Service", "Communication", "Problem Solving", "CRM Systems", "Conflict Resolution"],
        "experienceMin": 1,
        "experienceMax": 2,
        "education": "High School",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733130, "value": "Comprehensive health, dental, and vision insurance for employees and dependents." },
            { "id": 1739169733131, "value": "401(k) retirement savings plan with company matching contributions." },
            { "id": 1739169733139, "value": "Paid training and continuous professional development programs." },
            { "id": 1739169733140, "value": "Generous paid time off (PTO), including vacation, sick leave, and paid holidays." }
        ],
        "careerPage": true,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [
            {
              "id": 1,
              "name": "Application Question",
              "question": [
                {
                  "id": 1,
                  "name": "Are you 18 years of age or older?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 2,
                  "name": "Are you legally authorized to work in the United States?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 3,
                  "name": "Will you now, or in the future, require sponsorship for employment visa status (e.g., H1B or TN visa status)?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 4,
                  "name": "Have you ever been terminated or asked to resign, or resigned to avoid termination from any former employer? This includes military service or prior employment with Urecruits.",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 5,
                  "name": "Have you been a contract or temporary employee with xxx(Company Name) in the last year?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 6,
                  "name": "Do you have any friends or relatives currently employed by the Urecruits or any of our affiliates?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 7,
                  "name": "To identify potential conflicts of interest, are you employed by or do you serve on the board of, or hold an elected or appointed position with a government or government related agency?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 8,
                  "name": "Do you currently hold an active life/health insurance license (including annuities)?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 9,
                  "name": "Are you currently subject to any agreement (including, but not limited to, restrictive convenants) with an employer, vendor or government entity that would limit or restrict, in any way, your activities or functions as a Urecruits employee?\n",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 10,
                  "name": "Each time you apply to a position within Urecruits, or any of our affiliates, we ask that you enter your unique identifier, also referred to as an eSignature - which is the equivalent to your handwritten signature. This eSignature certifies that all information on this application is true, correct and complete. It also certifies that you have read and accept the Terms and Conditions outlined on this application. If you have any questions regarding this statement, contact us 1-877-968-7762. Your unique identifier is your month and day of birth followed by the zip code of your residence. For example, enter 010155555 if you were born on January 1 and live in zip code 55555.",
                  "type": "freetextfield",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 11,
                  "name": "Please provide reasons for leaving previous employers. If no previous employment, please state that this is your first employment opportunity.\n",
                  "type": "freetextfield",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 12,
                  "name": "Please provide your salary expectations for this position.",
                  "type": "freetextfield",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                }
              ],
              "activeTab": true
            },
            {
              "id": 2,
              "name": "Voluntary Disclosures",
              "question": [
                {
                  "id": 1,
                  "name": "Gender\n",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "Declined to self identify"
                    },
                    {
                      "id": 2,
                      "name": "Male"
                    },
                    {
                      "id": 3,
                      "name": "Female"
                    },
                    {
                      "id": 4,
                      "name": "Unknown"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 2,
                  "name": "Veteran Status\n",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "I am a protected Veteran"
                    },
                    {
                      "id": 2,
                      "name": "I am not a protected Veteran"
                    },
                    {
                      "id": 3,
                      "name": "I am a Veteran but not a protected veteran"
                    },
                    {
                      "id": 4,
                      "name": "I wish to not self identify"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 3,
                  "name": "Race\n",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "American Indian or Alaska Native (Not Hispanic or Latino) (United States of America)"
                    },
                    {
                      "id": 2,
                      "name": "Asian (Not Hispanic or Latino) (United States of America)"
                    },
                    {
                      "id": 3,
                      "name": "Black or African American (Not Hispanic or Latino) (United States of America)"
                    },
                    {
                      "id": 4,
                      "name": "Declined to Self Identify (United States of America)"
                    },
                    {
                      "id": 5,
                      "name": "Hispanic or Latino (United States of America)"
                    },
                    {
                      "id": 6,
                      "name": "Native Hawaiian or Other Pacific Islander (Not Hispanic or Latino) (United States of America)"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                }
              ],
              "activeTab": true
            },
            {
              "id": 3,
              "name": "Disability",
              "question": [
                {
                  "id": 1,
                  "name": "Voluntary Self-Identification of Disability",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "Yes, I Have A Disability, Or Have A History/Record Of Having A Disability"
                    },
                    {
                      "id": 2,
                      "name": "No, I Don't Have A Disability, Or A History/Record Of Having A Disability"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                }
              ],
              "activeTab": true
            }
          ],
        "status": "draft",
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 74,
        "authorId": authorId,
        "title": "Administrative Assistant",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": false,
        "salaryMonthMin": 2800,
        "salaryMonthMax": 4500,
        "salaryHourMin": 17.50,
        "salaryHourMax": 28.13,
        "salaryYearMin": 33600,
        "salaryYearMax": 54000,
        "negotiable": true,
        "numberOpenings": "1",
        "jobType": "Full-Time Employees",
        "description": "<p>As an Administrative Assistant, you will play a key role in keeping daily operations running smoothly. Your responsibilities include scheduling meetings, managing correspondence, organizing documents, and supporting team members with administrative tasks.</p><p><br></p><p><strong>Key Responsibilities:</strong></p><ul><li>Manage calendars, schedule meetings, and organize office tasks.</li><li>Handle correspondence, emails, and documentation processing.</li><li>Assist in preparing reports, presentations, and office records.</li><li>Support various departments with administrative needs.</li><li>Ensure smooth day-to-day office operations with attention to detail.</li></ul>",
        "shortDescription": "Ensure smooth office operations with administrative support, scheduling, and document management.",
        "preferableShift": "General Shift",
        "industryId": 7,
        "functionalArea": "Executive Management",
        "noticePeriod": "Within 15 days",
        "skills": ["Administrative Support", "Calendar Management", "Document Management", "Communication", "Organization"],
        "experienceMin": 1,
        "experienceMax": 2,
        "education": "High School",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733132, "value": "Medical, dental, and vision insurance with multiple plan options." },
            { "id": 1739169733133, "value": "401(k) retirement savings plan with employer-matching contributions." },
            { "id": 1739169733141, "value": "Tuition reimbursement and career development programs." },
            { "id": 1739169733142, "value": "Flexible work schedule to support work-life balance." }
        ],
        "careerPage": true,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [],
        "status": "draft",
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 4024,
        "authorId": authorId,
        "title": "Sales Representative",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": true,
        "salaryMonthMin": 3500,
        "salaryMonthMax": 7000,
        "salaryHourMin": 21.88,
        "salaryHourMax": 43.75,
        "salaryYearMin": 42000,
        "salaryYearMax": 84000,
        "negotiable": true,
        "numberOpenings": "2",
        "jobType": "Full-Time Employees",
        "description": "<p>As a Sales Representative, you will be responsible for driving revenue growth by building strong client relationships and implementing effective sales strategies. Your role will involve identifying new business opportunities, presenting products/services, and closing deals to achieve sales targets.</p><p><br></p><p><strong>Key Responsibilities:</strong></p><ul><li>Identify and pursue new business opportunities through proactive outreach.</li><li>Develop strong client relationships and provide tailored sales solutions.</li><li>Present and demonstrate products/services to potential customers.</li><li>Negotiate contracts and close deals to meet or exceed sales targets.</li><li>Maintain accurate sales records and report on performance metrics.</li></ul>",
        "shortDescription": "Generate new business and close deals through effective sales strategies and client relationships.",
        "preferableShift": "General Shift",
        "industryId": 8,
        "functionalArea": "Sales and Marketing",
        "noticePeriod": "1 Month",
        "skills": ["Sales", "Client Relations", "Negotiation", "Communication", "CRM Systems"],
        "experienceMin": 1,
        "experienceMax": 3,
        "education": "Bachelor's degree",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733134, "value": "Comprehensive health, dental, and vision insurance for employees and dependents." },
            { "id": 1739169733135, "value": "401(k) retirement savings plan with employer-matching contributions." },
            { "id": 1739169733136, "value": "Competitive commission structure with performance-based incentives." },
            { "id": 1739169733143, "value": "Professional development opportunities and sales training programs." }
        ],
        "careerPage": true,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [
            {
              "id": 1,
              "name": "Application Question",
              "question": [
                {
                  "id": 1,
                  "name": "Are you 18 years of age or older?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 2,
                  "name": "Are you legally authorized to work in the United States?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 3,
                  "name": "Will you now, or in the future, require sponsorship for employment visa status (e.g., H1B or TN visa status)?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 4,
                  "name": "Have you ever been terminated or asked to resign, or resigned to avoid termination from any former employer? This includes military service or prior employment with Urecruits.",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 5,
                  "name": "Have you been a contract or temporary employee with xxx(Company Name) in the last year?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 6,
                  "name": "Do you have any friends or relatives currently employed by the Urecruits or any of our affiliates?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 7,
                  "name": "To identify potential conflicts of interest, are you employed by or do you serve on the board of, or hold an elected or appointed position with a government or government related agency?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 8,
                  "name": "Do you currently hold an active life/health insurance license (including annuities)?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 9,
                  "name": "Are you currently subject to any agreement (including, but not limited to, restrictive convenants) with an employer, vendor or government entity that would limit or restrict, in any way, your activities or functions as a Urecruits employee?\n",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 10,
                  "name": "Each time you apply to a position within Urecruits, or any of our affiliates, we ask that you enter your unique identifier, also referred to as an eSignature - which is the equivalent to your handwritten signature. This eSignature certifies that all information on this application is true, correct and complete. It also certifies that you have read and accept the Terms and Conditions outlined on this application. If you have any questions regarding this statement, contact us 1-877-968-7762. Your unique identifier is your month and day of birth followed by the zip code of your residence. For example, enter 010155555 if you were born on January 1 and live in zip code 55555.",
                  "type": "freetextfield",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 11,
                  "name": "Please provide reasons for leaving previous employers. If no previous employment, please state that this is your first employment opportunity.\n",
                  "type": "freetextfield",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 12,
                  "name": "Please provide your salary expectations for this position.",
                  "type": "freetextfield",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                }
              ],
              "activeTab": true
            },
            {
              "id": 2,
              "name": "Voluntary Disclosures",
              "question": [
                {
                  "id": 1,
                  "name": "Gender\n",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "Declined to self identify"
                    },
                    {
                      "id": 2,
                      "name": "Male"
                    },
                    {
                      "id": 3,
                      "name": "Female"
                    },
                    {
                      "id": 4,
                      "name": "Unknown"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 2,
                  "name": "Veteran Status\n",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "I am a protected Veteran"
                    },
                    {
                      "id": 2,
                      "name": "I am not a protected Veteran"
                    },
                    {
                      "id": 3,
                      "name": "I am a Veteran but not a protected veteran"
                    },
                    {
                      "id": 4,
                      "name": "I wish to not self identify"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 3,
                  "name": "Race\n",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "American Indian or Alaska Native (Not Hispanic or Latino) (United States of America)"
                    },
                    {
                      "id": 2,
                      "name": "Asian (Not Hispanic or Latino) (United States of America)"
                    },
                    {
                      "id": 3,
                      "name": "Black or African American (Not Hispanic or Latino) (United States of America)"
                    },
                    {
                      "id": 4,
                      "name": "Declined to Self Identify (United States of America)"
                    },
                    {
                      "id": 5,
                      "name": "Hispanic or Latino (United States of America)"
                    },
                    {
                      "id": 6,
                      "name": "Native Hawaiian or Other Pacific Islander (Not Hispanic or Latino) (United States of America)"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                }
              ],
              "activeTab": true
            },
            {
              "id": 3,
              "name": "Disability",
              "question": [
                {
                  "id": 1,
                  "name": "Voluntary Self-Identification of Disability",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "Yes, I Have A Disability, Or Have A History/Record Of Having A Disability"
                    },
                    {
                      "id": 2,
                      "name": "No, I Don't Have A Disability, Or A History/Record Of Having A Disability"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                }
              ],
              "activeTab": true
            }
          ],
        "status": "draft",
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 1033,
        "authorId": authorId,
        "title": "Content Writer",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": true,
        "salaryMonthMin": 3000,
        "salaryMonthMax": 5500,
        "salaryHourMin": 18.75,
        "salaryHourMax": 34.38,
        "salaryYearMin": 36000,
        "salaryYearMax": 66000,
        "negotiable": true,
        "numberOpenings": "1",
        "jobType": "Full-Time Employees",
        "description": "<p>As a Content Writer, you will be responsible for creating engaging and informative content for various digital and print platforms. Your role will involve writing blog posts, marketing materials, social media content, and SEO-driven articles while maintaining brand consistency.</p><p><br></p><p><strong>Key Responsibilities:</strong></p><ul><li>Research, write, and edit compelling content for blogs, websites, and marketing campaigns.</li><li>Develop content strategies that align with business goals and audience needs.</li><li>Optimize content for search engines (SEO) and ensure readability.</li><li>Collaborate with marketing and design teams to enhance content presentation.</li><li>Stay updated on industry trends and best practices in content creation.</li></ul>",
        "shortDescription": "Write high-quality, engaging content for blogs, social media, and marketing campaigns.",
        "preferableShift": "General Shift",
        "industryId": 9,
        "functionalArea": "Sales and Marketing",
        "noticePeriod": "Within 15 days",
        "skills": ["Content Writing", "SEO", "Social Media", "Editing", "Research"],
        "experienceMin": 1,
        "experienceMax": 3,
        "education": "Bachelor's degree",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733137, "value": "Health, dental, and vision insurance for employees and families." },
            { "id": 1739169733138, "value": "401(k) retirement plan with employer contributions." },
            { "id": 1739169733144, "value": "Flexible work schedule with remote work options." },
            { "id": 1739169733145, "value": "Creative freedom and opportunities for career growth in content marketing." }
        ],
        "careerPage": false,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [],
        "status": "draft",
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 1197,
        "authorId": authorId,
        "title": "Data Entry Specialist",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": true,
        "salaryMonthMin": 2500,
        "salaryMonthMax": 4000,
        "salaryHourMin": 15.63,
        "salaryHourMax": 25.00,
        "salaryYearMin": 30000,
        "salaryYearMax": 48000,
        "negotiable": true,
        "numberOpenings": "2",
        "jobType": "Full-Time Employees",
        "description": "<p>As a Data Entry Specialist, you will ensure the accuracy and integrity of data by entering and managing records efficiently. Your role will involve updating databases, verifying information, and maintaining confidentiality while following company data management procedures.</p><p><br></p><p><strong>Key Responsibilities:</strong></p><ul><li>Enter, update, and verify data with high accuracy.</li><li>Maintain digital and physical records following company guidelines.</li><li>Ensure data integrity and confidentiality while handling sensitive information.</li><li>Collaborate with team members to resolve data inconsistencies.</li><li>Generate reports and provide insights based on collected data.</li></ul>",
        "shortDescription": "Ensure accurate and organized data management by entering and verifying records efficiently.",
        "preferableShift": "General Shift",
        "industryId": 10,
        "functionalArea": "Information Technology (IT)",
        "noticePeriod": "Immediately",
        "skills": ["Data Entry", "Attention to Detail", "Database Management", "Accuracy", "Organization"],
        "experienceMin": 1,
        "experienceMax": 2,
        "education": "High School",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733139, "value": "Health and dental insurance with various coverage options." },
            { "id": 1739169733140, "value": "401(k) retirement savings plan with company matching." },
            { "id": 1739169733146, "value": "Flexible work hours and remote work opportunities." },
            { "id": 1739169733147, "value": "Career advancement opportunities within the data management field." }
        ],
        "careerPage": true,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [],
        "status": "draft",
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 4683,
        "authorId": authorId,
        "title": "Training Coordinator",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": true,
        "salaryMonthMin": 3500,
        "salaryMonthMax": 6000,
        "salaryHourMin": 21.88,
        "salaryHourMax": 37.50,
        "salaryYearMin": 42000,
        "salaryYearMax": 72000,
        "negotiable": true,
        "numberOpenings": "1",
        "jobType": "Full-Time Employees",
        "description": "<p>As a Training Coordinator, you will manage and oversee employee training programs to enhance skills and performance. Your role involves scheduling training sessions, developing materials, tracking attendance, and evaluating effectiveness.</p><p><br></p><p><strong>Key Responsibilities:</strong></p><ul><li>Coordinate and schedule employee training programs.</li><li>Develop and update training materials to align with company goals.</li><li>Track employee participation and assess training effectiveness.</li><li>Work with trainers and department heads to improve training strategies.</li><li>Ensure compliance with company policies and industry standards.</li></ul>",
        "shortDescription": "Manage training programs to improve employee skills and organizational growth.",
        "preferableShift": "General Shift",
        "industryId": 15,
        "functionalArea": "Training and Development",
        "noticePeriod": "1 Month",
        "skills": ["Training Coordination", "Program Management", "Content Development", "Communication", "Organization"],
        "experienceMin": 2,
        "experienceMax": 4,
        "education": "Bachelor's degree",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733141, "value": "Comprehensive health insurance coverage." },
            { "id": 1739169733142, "value": "401(k) matching retirement plan." },
            { "id": 1739169733143, "value": "Professional development budget for continuous learning." },
            { "id": 1739169733150, "value": "Flexible work schedule and remote options." },
            { "id": *************, "value": "Paid certifications and training programs." }
        ],
        "careerPage": true,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [],
        "status": "draft",
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 3713,
        "authorId": authorId,
        "title": "Quality Assurance Specialist",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": false,
        "salaryMonthMin": 4000,
        "salaryMonthMax": 7000,
        "salaryHourMin": 25.00,
        "salaryHourMax": 43.75,
        "salaryYearMin": 48000,
        "salaryYearMax": 84000,
        "negotiable": true,
        "numberOpenings": "1",
        "jobType": "Full-Time Employees",
        "description": "<p>As a Quality Assurance Specialist, you will be responsible for ensuring high product and service standards. Your role involves developing quality procedures, conducting audits, identifying defects, and implementing improvements.</p><p><br></p><p><strong>Key Responsibilities:</strong></p><ul><li>Develop and enforce quality control standards.</li><li>Perform regular product/service inspections to ensure compliance.</li><li>Identify quality issues and recommend improvements.</li><li>Maintain detailed documentation of QA processes.</li><li>Collaborate with teams to enhance product quality and efficiency.</li></ul>",
        "shortDescription": "Ensure high product and service quality through structured QA processes.",
        "preferableShift": "General Shift",
        "industryId": 17,
        "functionalArea": "Quality Assurance (QA)",
        "noticePeriod": "Within 15 days",
        "skills": ["Quality Assurance", "Process Improvement", "Documentation", "Analysis", "Problem Solving"],
        "experienceMin": 2,
        "experienceMax": 4,
        "education": "Bachelor's degree",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733144, "value": "Health insurance with comprehensive coverage." },
            { "id": 1739169733145, "value": "401(k) retirement savings plan with employer contributions." },
            { "id": *************, "value": "Performance-based bonuses and incentives." },
            { "id": 1739169733153, "value": "Opportunities for career advancement." },
            { "id": 1739169733154, "value": "Work-from-home flexibility (if applicable)." }
        ],
        "careerPage": true,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [],
        "status": "draft",
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 2624,
        "authorId": authorId,
        "title": "Logistics Coordinator",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": false,
        "salaryMonthMin": 3500,
        "salaryMonthMax": 6000,
        "salaryHourMin": 21.88,
        "salaryHourMax": 37.50,
        "salaryYearMin": 42000,
        "salaryYearMax": 72000,
        "negotiable": true,
        "numberOpenings": "1",
        "jobType": "Full-Time Employees",
        "description": "<p>As a Logistics Coordinator, you will manage the supply chain and delivery process to ensure timely and cost-effective operations. Your responsibilities include coordinating shipments, tracking inventory, and optimizing logistics strategies.</p><p>Key Responsibilities:</p><ul><li>Plan and coordinate the movement of goods from suppliers to customers.</li><li>Monitor inventory levels and ensure efficient stock management.</li><li>Work with transportation providers to schedule shipments.</li><li>Analyze logistics data to improve supply chain performance.</li><li>Ensure compliance with industry regulations and company policies.</li></ul>",
        "shortDescription": "Manage and optimize logistics and supply chain operations.",
        "preferableShift": "General Shift",
        "industryId": 20,
        "functionalArea": "Procurement and Purchasing",
        "noticePeriod": "1 Month",
        "skills": ["Logistics Management", "Supply Chain", "Inventory Control", "Scheduling", "Problem Solving"],
        "experienceMin": 2,
        "experienceMax": 4,
        "education": "Bachelor's degree",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733146, "value": "Health insurance plan covering medical expenses." },
            { "id": 1739169733147, "value": "401(k) retirement plan with employer matching." },
            { "id": 1739169733155, "value": "Company-sponsored transportation assistance." },
            { "id": 1739169733156, "value": "Tuition reimbursement for relevant courses." },
            { "id": 1739169733157, "value": "Paid overtime and shift allowances." }
        ],
        "careerPage": false,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [
            {
              "id": 1,
              "name": "Application Question",
              "question": [
                {
                  "id": 1,
                  "name": "Are you 18 years of age or older?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 2,
                  "name": "Are you legally authorized to work in the United States?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 3,
                  "name": "Will you now, or in the future, require sponsorship for employment visa status (e.g., H1B or TN visa status)?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 4,
                  "name": "Have you ever been terminated or asked to resign, or resigned to avoid termination from any former employer? This includes military service or prior employment with Urecruits.",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 5,
                  "name": "Have you been a contract or temporary employee with xxx(Company Name) in the last year?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 6,
                  "name": "Do you have any friends or relatives currently employed by the Urecruits or any of our affiliates?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 7,
                  "name": "To identify potential conflicts of interest, are you employed by or do you serve on the board of, or hold an elected or appointed position with a government or government related agency?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 8,
                  "name": "Do you currently hold an active life/health insurance license (including annuities)?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 9,
                  "name": "Are you currently subject to any agreement (including, but not limited to, restrictive convenants) with an employer, vendor or government entity that would limit or restrict, in any way, your activities or functions as a Urecruits employee?\n",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 10,
                  "name": "Each time you apply to a position within Urecruits, or any of our affiliates, we ask that you enter your unique identifier, also referred to as an eSignature - which is the equivalent to your handwritten signature. This eSignature certifies that all information on this application is true, correct and complete. It also certifies that you have read and accept the Terms and Conditions outlined on this application. If you have any questions regarding this statement, contact us 1-877-968-7762. Your unique identifier is your month and day of birth followed by the zip code of your residence. For example, enter 010155555 if you were born on January 1 and live in zip code 55555.",
                  "type": "freetextfield",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 11,
                  "name": "Please provide reasons for leaving previous employers. If no previous employment, please state that this is your first employment opportunity.\n",
                  "type": "freetextfield",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 12,
                  "name": "Please provide your salary expectations for this position.",
                  "type": "freetextfield",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                }
              ],
              "activeTab": true
            },
            {
              "id": 2,
              "name": "Voluntary Disclosures",
              "question": [
                {
                  "id": 1,
                  "name": "Gender\n",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "Declined to self identify"
                    },
                    {
                      "id": 2,
                      "name": "Male"
                    },
                    {
                      "id": 3,
                      "name": "Female"
                    },
                    {
                      "id": 4,
                      "name": "Unknown"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 2,
                  "name": "Veteran Status\n",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "I am a protected Veteran"
                    },
                    {
                      "id": 2,
                      "name": "I am not a protected Veteran"
                    },
                    {
                      "id": 3,
                      "name": "I am a Veteran but not a protected veteran"
                    },
                    {
                      "id": 4,
                      "name": "I wish to not self identify"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 3,
                  "name": "Race\n",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "American Indian or Alaska Native (Not Hispanic or Latino) (United States of America)"
                    },
                    {
                      "id": 2,
                      "name": "Asian (Not Hispanic or Latino) (United States of America)"
                    },
                    {
                      "id": 3,
                      "name": "Black or African American (Not Hispanic or Latino) (United States of America)"
                    },
                    {
                      "id": 4,
                      "name": "Declined to Self Identify (United States of America)"
                    },
                    {
                      "id": 5,
                      "name": "Hispanic or Latino (United States of America)"
                    },
                    {
                      "id": 6,
                      "name": "Native Hawaiian or Other Pacific Islander (Not Hispanic or Latino) (United States of America)"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                }
              ],
              "activeTab": true
            },
            {
              "id": 3,
              "name": "Disability",
              "question": [
                {
                  "id": 1,
                  "name": "Voluntary Self-Identification of Disability",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "Yes, I Have A Disability, Or Have A History/Record Of Having A Disability"
                    },
                    {
                      "id": 2,
                      "name": "No, I Don't Have A Disability, Or A History/Record Of Having A Disability"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                }
              ],
              "activeTab": true
            }
          ],
        "status": "draft",
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 2387,
        "authorId": authorId,
        "title": "IT Support Specialist",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": true,
        "salaryMonthMin": 4000,
        "salaryMonthMax": 7000,
        "salaryHourMin": 25.00,
        "salaryHourMax": 43.75,
        "salaryYearMin": 48000,
        "salaryYearMax": 84000,
        "negotiable": true,
        "numberOpenings": "2",
        "jobType": "Full-Time Employees",
        "description": "<p>As an IT Support Specialist, you will provide technical assistance to employees and ensure smooth IT operations. Your role involves troubleshooting issues, maintaining systems, and supporting hardware and software needs.</p><p><br></p><p><strong>Key Responsibilities:</strong></p><ul><li>Provide technical support to resolve IT issues for employees.</li><li>Maintain and troubleshoot computer systems, networks, and software.</li><li>Install and configure hardware, software, and security updates.</li><li>Monitor system performance and ensure data security.</li><li>Document IT support processes and user guides.</li></ul>",
        "shortDescription": "Deliver IT support and ensure seamless system operations.",
        "preferableShift": "General Shift",
        "industryId": 2,
        "functionalArea": "Information Technology (IT)",
        "noticePeriod": "Within 15 days",
        "skills": ["Technical Support", "System Administration", "Troubleshooting", "Customer Service", "Network Management"],
        "experienceMin": 2,
        "experienceMax": 4,
        "education": "Bachelor's degree",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733148, "value": "Comprehensive health insurance plan." },
            { "id": 1739169733149, "value": "401(k) retirement plan with employer match." },
            { "id": 1739169733158, "value": "Company-provided laptop and equipment." },
            { "id": 1739169733159, "value": "Remote work and flexible scheduling options." },
            { "id": 1739169733160, "value": "Annual performance-based salary increments." }
        ],
        "careerPage": true,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [],
        "status": "draft",
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 1713,
        "authorId": authorId,
        "title": "Financial Analyst",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": true,
        "salaryMonthMin": 4500,
        "salaryMonthMax": 8000,
        "salaryHourMin": 28.13,
        "salaryHourMax": 50.00,
        "salaryYearMin": 54000,
        "salaryYearMax": 96000,
        "negotiable": true,
        "numberOpenings": "1",
        "jobType": "Full-Time Employees",
        "description": "<p>As a Financial Analyst, you will analyze financial data and provide insights to support business decisions. Your role involves financial modeling, forecasting, and performance evaluation.</p><p><br></p><p><strong>Key Responsibilities:</strong></p><ul><li>Conduct financial analysis and prepare reports.</li><li>Develop financial models to support business strategies.</li><li>Monitor financial performance and key metrics.</li><li>Identify cost-saving opportunities and risk mitigation strategies.</li><li>Collaborate with teams to enhance financial planning.</li></ul>",
        "shortDescription": "Analyze financial data and support strategic decision-making.",
        "preferableShift": "General Shift",
        "industryId": 10,
        "functionalArea": "Finance and Accounting",
        "noticePeriod": "1 Month",
        "skills": ["Financial Analysis", "Financial Modeling", "Excel", "Data Analysis", "Report Writing"],
        "experienceMin": 2,
        "experienceMax": 4,
        "education": "Bachelor's degree",
        "screeningQuestions": [],
        "benefits": [
            { "id": *************, "value": "Comprehensive health, dental, and vision insurance." },
            { "id": *************, "value": "401(k) plan with company match." },
            { "id": *************, "value": "Performance-based annual bonuses." },
            { "id": *************, "value": "Tuition reimbursement for CFA and CPA certifications." },
            { "id": *************, "value": "Flexible work schedule with remote options." }
        ],
        "careerPage": true,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [],
        "status": "draft",
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 3879,
        "authorId": authorId,
        "title": "Research Analyst",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": true,
        "salaryMonthMin": 4000,
        "salaryMonthMax": 7000,
        "salaryHourMin": 25.00,
        "salaryHourMax": 43.75,
        "salaryYearMin": 48000,
        "salaryYearMax": 84000,
        "negotiable": true,
        "numberOpenings": "1",
        "jobType": "Full-Time Employees",
        "description": "<p>As a Research Analyst, you will conduct market research and analyze data to provide actionable insights. Your role involves data collection, trend analysis, and strategic recommendations.</p><p><br></p><p><strong>Key Responsibilities:</strong></p><ul><li>Gather and analyze data from various sources.</li><li>Identify market trends and industry patterns.</li><li>Prepare detailed reports and presentations.</li><li>Collaborate with stakeholders to support decision-making.</li><li>Ensure data accuracy and integrity in research findings.</li></ul>",
        "shortDescription": "Conduct research and deliver strategic insights.",
        "preferableShift": "General Shift",
        "industryId": 16,
        "functionalArea": "Research and Development (R&D)",
        "noticePeriod": "Within 15 days",
        "skills": ["Research", "Data Analysis", "Report Writing", "Statistical Analysis", "Market Research"],
        "experienceMin": 2,
        "experienceMax": 4,
        "education": "Bachelor's degree",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733153, "value": "Health and wellness benefits, including mental health support." },
            { "id": 1739169733154, "value": "401(k) retirement plan with company contribution." },
            { "id": 1739169733165, "value": "Access to premium research tools and databases." },
            { "id": 1739169733166, "value": "Paid professional development and industry conferences." },
            { "id": 1739169733167, "value": "Hybrid work environment with flexible hours." }
        ],
        "careerPage": false,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [
            {
              "id": 1,
              "name": "Application Question",
              "question": [
                {
                  "id": 1,
                  "name": "Are you 18 years of age or older?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 2,
                  "name": "Are you legally authorized to work in the United States?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 3,
                  "name": "Will you now, or in the future, require sponsorship for employment visa status (e.g., H1B or TN visa status)?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 4,
                  "name": "Have you ever been terminated or asked to resign, or resigned to avoid termination from any former employer? This includes military service or prior employment with Urecruits.",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 5,
                  "name": "Have you been a contract or temporary employee with xxx(Company Name) in the last year?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 6,
                  "name": "Do you have any friends or relatives currently employed by the Urecruits or any of our affiliates?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 7,
                  "name": "To identify potential conflicts of interest, are you employed by or do you serve on the board of, or hold an elected or appointed position with a government or government related agency?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 8,
                  "name": "Do you currently hold an active life/health insurance license (including annuities)?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 9,
                  "name": "Are you currently subject to any agreement (including, but not limited to, restrictive convenants) with an employer, vendor or government entity that would limit or restrict, in any way, your activities or functions as a Urecruits employee?\n",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 10,
                  "name": "Each time you apply to a position within Urecruits, or any of our affiliates, we ask that you enter your unique identifier, also referred to as an eSignature - which is the equivalent to your handwritten signature. This eSignature certifies that all information on this application is true, correct and complete. It also certifies that you have read and accept the Terms and Conditions outlined on this application. If you have any questions regarding this statement, contact us 1-877-968-7762. Your unique identifier is your month and day of birth followed by the zip code of your residence. For example, enter 010155555 if you were born on January 1 and live in zip code 55555.",
                  "type": "freetextfield",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 11,
                  "name": "Please provide reasons for leaving previous employers. If no previous employment, please state that this is your first employment opportunity.\n",
                  "type": "freetextfield",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 12,
                  "name": "Please provide your salary expectations for this position.",
                  "type": "freetextfield",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                }
              ],
              "activeTab": true
            },
            {
              "id": 2,
              "name": "Voluntary Disclosures",
              "question": [
                {
                  "id": 1,
                  "name": "Gender\n",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "Declined to self identify"
                    },
                    {
                      "id": 2,
                      "name": "Male"
                    },
                    {
                      "id": 3,
                      "name": "Female"
                    },
                    {
                      "id": 4,
                      "name": "Unknown"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 2,
                  "name": "Veteran Status\n",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "I am a protected Veteran"
                    },
                    {
                      "id": 2,
                      "name": "I am not a protected Veteran"
                    },
                    {
                      "id": 3,
                      "name": "I am a Veteran but not a protected veteran"
                    },
                    {
                      "id": 4,
                      "name": "I wish to not self identify"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 3,
                  "name": "Race\n",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "American Indian or Alaska Native (Not Hispanic or Latino) (United States of America)"
                    },
                    {
                      "id": 2,
                      "name": "Asian (Not Hispanic or Latino) (United States of America)"
                    },
                    {
                      "id": 3,
                      "name": "Black or African American (Not Hispanic or Latino) (United States of America)"
                    },
                    {
                      "id": 4,
                      "name": "Declined to Self Identify (United States of America)"
                    },
                    {
                      "id": 5,
                      "name": "Hispanic or Latino (United States of America)"
                    },
                    {
                      "id": 6,
                      "name": "Native Hawaiian or Other Pacific Islander (Not Hispanic or Latino) (United States of America)"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                }
              ],
              "activeTab": true
            },
            {
              "id": 3,
              "name": "Disability",
              "question": [
                {
                  "id": 1,
                  "name": "Voluntary Self-Identification of Disability",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "Yes, I Have A Disability, Or Have A History/Record Of Having A Disability"
                    },
                    {
                      "id": 2,
                      "name": "No, I Don't Have A Disability, Or A History/Record Of Having A Disability"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                }
              ],
              "activeTab": true
            }
          ],
        "status": "draft",
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 965,
        "authorId": authorId,
        "title": "Compliance Specialist",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": true,
        "salaryMonthMin": 4500,
        "salaryMonthMax": 8000,
        "salaryHourMin": 28.13,
        "salaryHourMax": 50.00,
        "salaryYearMin": 54000,
        "salaryYearMax": 96000,
        "negotiable": true,
        "numberOpenings": "1",
        "jobType": "Full-Time Employees",
        "description": "<p>As a Compliance Specialist, you will ensure that the organization adheres to legal and regulatory standards. Your role involves compliance monitoring, audits, and risk assessments.</p><p><br></p><p><strong>Key Responsibilities:</strong></p><ul><li>Monitor and interpret industry regulations.</li><li>Develop and implement compliance policies.</li><li>Conduct internal audits and risk assessments.</li><li>Train employees on compliance best practices.</li><li>Ensure documentation and reporting meet regulatory requirements.</li></ul>",
        "shortDescription": "Ensure regulatory compliance and mitigate organizational risks.",
        "preferableShift": "General Shift",
        "industryId": 17,
        "functionalArea": "Legal and Compliance",
        "noticePeriod": "1 Month",
        "skills": ["Compliance Management", "Regulatory Affairs", "Auditing", "Risk Assessment", "Documentation"],
        "experienceMin": 3,
        "experienceMax": 5,
        "education": "Bachelor's degree",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733156, "value": "Comprehensive health, dental, and vision coverage." },
            { "id": 1739169733157, "value": "401(k) with competitive employer matching." },
            { "id": 1739169733168, "value": "Reimbursement for legal and compliance certifications (CAMS, CCEP, CRCM)." },
            { "id": 1739169733169, "value": "Annual training on regulatory updates and risk management." },
            { "id": 1739169733170, "value": "Flexible remote work options." }
        ],
        "careerPage": true,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [],
        "status": "draft",
        "approverId": authorId,
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 542,
        "authorId": authorId,
        "title": "Business Development Manager",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": true,
        "salaryMonthMin": 5000,
        "salaryMonthMax": 9000,
        "salaryHourMin": 31.25,
        "salaryHourMax": 56.25,
        "salaryYearMin": 60000,
        "salaryYearMax": 108000,
        "negotiable": true,
        "numberOpenings": "1",
        "jobType": "Full-Time Employees",
        "description": "<p>As a Business Development Manager, you will drive business growth through strategic initiatives and market expansion. Your role involves identifying opportunities, building partnerships, and creating growth strategies.</p><p><br></p><p><strong>Key Responsibilities:</strong></p><ul><li>Identify new market opportunities and business prospects.</li><li>Develop and maintain strategic partnerships.</li><li>Create and execute business growth strategies.</li><li>Collaborate with sales and marketing teams.</li><li>Analyze market trends and competitive landscapes.</li></ul>",
        "shortDescription": "Lead business growth through strategic partnerships.",
        "preferableShift": "General Shift",
        "industryId": 1,
        "functionalArea": "Sales and Marketing",
        "noticePeriod": "1 Month",
        "skills": ["Business Development", "Strategic Planning", "Partnership Building", "Market Analysis", "Negotiation"],
        "experienceMin": 3,
        "experienceMax": 5,
        "education": "Bachelor's degree",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733159, "value": "Medical, dental, and vision insurance for employees and dependents." },
            { "id": 1739169733160, "value": "401(k) plan with employer contributions." },
            { "id": 1739169733161, "value": "Commission-based performance incentives." },
            { "id": 1739169733171, "value": "Company-sponsored travel for business development." },
            { "id": 1739169733172, "value": "Professional networking events and executive coaching." }
        ],
        "careerPage": true,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [],
        "status": "draft",
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 3542,
        "authorId": authorId,
        "title": "Product Manager",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": true,
        "salaryMonthMin": 5500,
        "salaryMonthMax": 10000,
        "salaryHourMin": 34.38,
        "salaryHourMax": 62.50,
        "salaryYearMin": 66000,
        "salaryYearMax": 120000,
        "negotiable": true,
        "numberOpenings": "1",
        "jobType": "Full-Time Employees",
        "description": "<p>As a Product Manager, you will oversee the development and execution of product strategies. Your role involves market research, cross-functional collaboration, and product lifecycle management.</p><p><br></p><p><strong>Key Responsibilities:</strong></p><ul><li>Define and manage product requirements.</li><li>Coordinate with engineering, marketing, and sales teams.</li><li>Analyze market needs and customer feedback.</li><li>Ensure timely product launches and feature updates.</li><li>Monitor product performance and adjust strategies accordingly.</li></ul>",
        "shortDescription": "Lead product strategy and development initiatives.",
        "preferableShift": "General Shift",
        "industryId": 2,
        "functionalArea": "Information Technology (IT)",
        "noticePeriod": "1 Month",
        "skills": ["Product Management", "Market Analysis", "Project Management", "Strategic Planning", "Team Leadership"],
        "experienceMin": 3,
        "experienceMax": 5,
        "education": "Bachelor's degree",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733156, "value": "Health insurance with premium coverage options." },
            { "id": 1739169733157, "value": "401(k) plan with employer match." },
            { "id": 1739169733158, "value": "Stock options and long-term equity plans." },
            { "id": 1739169733173, "value": "Annual product management training and certification reimbursement." },
            { "id": 1739169733174, "value": "Flexible PTO policy and work-from-home options." }
        ],
        "careerPage": true,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [],
        "status": "draft",
        "companyId": companyId,
        "isAssessment": false
    },
    {
        "locations": [{ id: 11880, value: "", label: "" }],
        "positionId": 3119,
        "authorId": authorId,
        "title": "Operations Manager",
        "employer": employer,
        "consultancy": "",
        "remoteLocation": false,
        "salaryMonthMin": 6000,
        "salaryMonthMax": 11000,
        "salaryHourMin": 37.50,
        "salaryHourMax": 68.75,
        "salaryYearMin": 72000,
        "salaryYearMax": 132000,
        "negotiable": true,
        "numberOpenings": "1",
        "jobType": "Full-Time Employees",
        "description": "<p>As an Operations Manager, you will oversee business operations and ensure efficiency in processes and resource management. Your role involves managing teams, optimizing workflows, and implementing strategic initiatives.</p><p><br></p><p><strong>Key Responsibilities:</strong></p><ul><li>Manage and optimize business operations.</li><li>Ensure compliance with industry standards and regulations.</li><li>Develop and implement operational strategies.</li><li>Lead and coordinate cross-functional teams.</li><li>Monitor performance metrics and drive continuous improvements.</li></ul>",
        "shortDescription": "Oversee business operations and drive process efficiency.",
        "preferableShift": "General Shift",
        "industryId": 4,
        "functionalArea": "Executive Management",
        "noticePeriod": "1 Month",
        "skills": ["Operations Management", "Team Leadership", "Process Optimization", "Strategic Planning", "Budget Management"],
        "experienceMin": 4,
        "experienceMax": 6,
        "education": "Bachelor's degree",
        "screeningQuestions": [],
        "benefits": [
            { "id": 1739169733159, "value": "Comprehensive health, dental, and vision coverage." },
            { "id": 1739169733160, "value": "401(k) retirement plan with employer contributions." },
            { "id": 1739169733161, "value": "Executive training and leadership development programs." },
            { "id": 1739169733175, "value": "Relocation assistance for eligible candidates." },
            { "id": 1739169733176, "value": "Annual performance-based bonuses and stock options." }
        ],
        "careerPage": true,
        "publicSearch": true,
        "jobBoards": [],
        "applicationForm": [
            {
              "id": 1,
              "name": "Application Question",
              "question": [
                {
                  "id": 1,
                  "name": "Are you 18 years of age or older?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 2,
                  "name": "Are you legally authorized to work in the United States?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 3,
                  "name": "Will you now, or in the future, require sponsorship for employment visa status (e.g., H1B or TN visa status)?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 4,
                  "name": "Have you ever been terminated or asked to resign, or resigned to avoid termination from any former employer? This includes military service or prior employment with Urecruits.",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 5,
                  "name": "Have you been a contract or temporary employee with xxx(Company Name) in the last year?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 6,
                  "name": "Do you have any friends or relatives currently employed by the Urecruits or any of our affiliates?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 7,
                  "name": "To identify potential conflicts of interest, are you employed by or do you serve on the board of, or hold an elected or appointed position with a government or government related agency?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 8,
                  "name": "Do you currently hold an active life/health insurance license (including annuities)?",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 9,
                  "name": "Are you currently subject to any agreement (including, but not limited to, restrictive convenants) with an employer, vendor or government entity that would limit or restrict, in any way, your activities or functions as a Urecruits employee?\n",
                  "type": "yesno",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 10,
                  "name": "Each time you apply to a position within Urecruits, or any of our affiliates, we ask that you enter your unique identifier, also referred to as an eSignature - which is the equivalent to your handwritten signature. This eSignature certifies that all information on this application is true, correct and complete. It also certifies that you have read and accept the Terms and Conditions outlined on this application. If you have any questions regarding this statement, contact us 1-877-968-7762. Your unique identifier is your month and day of birth followed by the zip code of your residence. For example, enter 010155555 if you were born on January 1 and live in zip code 55555.",
                  "type": "freetextfield",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 11,
                  "name": "Please provide reasons for leaving previous employers. If no previous employment, please state that this is your first employment opportunity.\n",
                  "type": "freetextfield",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 12,
                  "name": "Please provide your salary expectations for this position.",
                  "type": "freetextfield",
                  "answer": [],
                  "required": false,
                  "userAnswer": null
                }
              ],
              "activeTab": true
            },
            {
              "id": 2,
              "name": "Voluntary Disclosures",
              "question": [
                {
                  "id": 1,
                  "name": "Gender\n",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "Declined to self identify"
                    },
                    {
                      "id": 2,
                      "name": "Male"
                    },
                    {
                      "id": 3,
                      "name": "Female"
                    },
                    {
                      "id": 4,
                      "name": "Unknown"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 2,
                  "name": "Veteran Status\n",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "I am a protected Veteran"
                    },
                    {
                      "id": 2,
                      "name": "I am not a protected Veteran"
                    },
                    {
                      "id": 3,
                      "name": "I am a Veteran but not a protected veteran"
                    },
                    {
                      "id": 4,
                      "name": "I wish to not self identify"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                },
                {
                  "id": 3,
                  "name": "Race\n",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "American Indian or Alaska Native (Not Hispanic or Latino) (United States of America)"
                    },
                    {
                      "id": 2,
                      "name": "Asian (Not Hispanic or Latino) (United States of America)"
                    },
                    {
                      "id": 3,
                      "name": "Black or African American (Not Hispanic or Latino) (United States of America)"
                    },
                    {
                      "id": 4,
                      "name": "Declined to Self Identify (United States of America)"
                    },
                    {
                      "id": 5,
                      "name": "Hispanic or Latino (United States of America)"
                    },
                    {
                      "id": 6,
                      "name": "Native Hawaiian or Other Pacific Islander (Not Hispanic or Latino) (United States of America)"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                }
              ],
              "activeTab": true
            },
            {
              "id": 3,
              "name": "Disability",
              "question": [
                {
                  "id": 1,
                  "name": "Voluntary Self-Identification of Disability",
                  "type": "onefromlist",
                  "answer": [
                    {
                      "id": 1,
                      "name": "Yes, I Have A Disability, Or Have A History/Record Of Having A Disability"
                    },
                    {
                      "id": 2,
                      "name": "No, I Don't Have A Disability, Or A History/Record Of Having A Disability"
                    }
                  ],
                  "required": false,
                  "userAnswer": null
                }
              ],
              "activeTab": true
            }
          ],
        "status": "draft",
        "approverId": authorId,
        "companyId": companyId,
        "isAssessment": false
    }
]