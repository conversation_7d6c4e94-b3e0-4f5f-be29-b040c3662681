@use "config" as *;
@use "mixins" as *;
.area-chart {
  background: $white;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid $grayTone2;
  width: 100%;
  margin-bottom: 24px;

  &__head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;

    @include media(xs) {
      flex-direction: column;
      gap: 15px;
      align-items: start;
      justify-content: start;
    }

    .table-screen-top__buttons-group {
      margin-left: 40px;
      display: flex;
      align-items: center;

      @include media(md) {
        margin-left: 0;
        grid-template-columns: repeat(2, minmax(0, 1fr));
        display: grid;
        width: 100%;
        gap: 10px;
      }
    }
    .table-screen-top__buttons-group__item.active {
      color: #015462;
      background: #acd8d1;
      border: 1px solid #acd8d1;
    }
    .table-screen-top__buttons-group__item {
      padding: 12px 20px;
      border-radius: 4px;
      font-size: 14px;
      line-height: 1;
      border: 1px solid #dfe2e6;
      background: #ffffff;
      transition: 0.3s ease-in, 0.3s ease-in-out;
      margin-right: 24px;
      color: #343b43;

      @include media(md) {
        margin-right: 10px;
        font-size: 12px;
        padding: 10px;
      }

      @include media(xs) {
        margin-right: 0px;
      }
    }
    .table-screen-top__buttons-group__item:last-child {
      margin-right: 0 !important;
    }

    &__headline {
      color: $black;
      font-size: 20px;
      text-transform: uppercase;
      line-height: 1;
      font-family: "Inter", "Poppins", sans-serif;
      font-weight: 600;
      padding-right: 16px;
      width: calc(100% - 124px);
    }

    &__select {
      width: 140px;
      @include media(sm) {
        // width: 100%;
      }
    }
  }

  &__body {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 4px;

    &.month {
      padding-bottom: 0;

      .area-chart__body__inner {
        width: 1600px;
      }

      &::-webkit-scrollbar {
        height: 4px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      &::-webkit-scrollbar-thumb {
        background: #888;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #555;
      }
    }

    &__inner {
      height: 186px;
      svg {
        width: 100% !important;
      }
    }
  }
}
