import { memo, useEffect, useRef, useState } from 'react';
import EducationsDetailsTabItem from './EducationsDetailsTabItem';
import axios from 'axios';
import { getEnv } from "@urecruits/api";
import patchData from "../../../hook/patchData";
import { AuthGuard, getConfig } from "@ucrecruits/globalstyle/src/ucrecruits-globalstyle";

const editIc = require('../../../image/icon/edit_ic.svg')
const plusIc = require('../../../image/icon/plus_ic.svg')

const EducationsDetailsTab = ({ data, setRefetch }) => {
  const token: string = localStorage.getItem('token')
  const [validateStatus, setValidateStatus] = useState(false)
  const [success, setSuccess] = useState(false)
  const [editMode, setEditMode] = useState(false)
  const [displayButton, setDisplayButton] = useState(false)
  const [addedMoreClicked, setAddedMoreClicked] = useState(false)
  const addedMoreEducationButtonRef = useRef(null)
  const submitButtonRef = useRef(null)

  //form state
  const [educationsDetails, setEducationsDetails] = useState([])

  useEffect(() => {
    if (data) {
      setEducationsDetails(data.educationDetails)
      setAddedMoreClicked(()=>false)
    }
  }, [data])

  const onFormChange = () => {
    setDisplayButton(true)
  }
  const {API_RECRUITMENT} = getEnv()

  const onClearHandler = (e) => {
    e.preventDefault()
    setEditMode(false)
    setDisplayButton(false)
    setEducationsDetails(data.educationDetails)
  }

  const onFormSubmit = async (e) => {
    e.preventDefault()
    setValidateStatus(false)

    if (validateStatus) {

      for (const item of educationsDetails) {
        await patchData(`${API_RECRUITMENT}/api/recruiter/education`, item).then((res) => {
          res.status === 200 ? setSuccess(true) : setSuccess(false)
        })
      }
      setRefetch(new Date())

      setTimeout(() => {
        setSuccess(false)
        setEditMode(false)
        setDisplayButton(false)
      }, 2000)
    }
  }

  const onAddEducationHandler = async () => {
    if (editMode) {
      // empty object
      const obg = {
        universityName: '',
        courseName: '',
        specification: '',
        coursePeriodStart: null,
        coursePeriodEnd: null,
        gpa: '',
        recruiterId: data.id,
      }
      setValidateStatus(false)
      if (validateStatus&&!addedMoreClicked) {
        setAddedMoreClicked(()=>true)
        for (const item of educationsDetails) {
          await patchData(`${API_RECRUITMENT}/api/recruiter/education`, item).then((res) => {
          })
        }
        await axios.post(`${API_RECRUITMENT}/api/recruiter/education`, obg, getConfig()).then((res) => {
          setRefetch(new Date())
        }).catch(err => {
          console.log(err)
        })
      }
    }
  }

  return (
    <AuthGuard module='recruiter'>
    <div className="profile__right__inner">
      <div className="profile__head">
        <div className="profile__head__inner">
          <p className="profile__head__title">
            Educations Details{editMode && (<span> - Editing Mode</span>)}
          </p>
          {
            !editMode && (
              <p className="profile__head__edit" onClick={() => setEditMode(true)}>
                <span>Edit</span>
                <img src={editIc} alt="edit icon"/>
              </p>
            )
          }
        </div>
      </div>
      <form
        className={`profile__form ${editMode ? '' : 'readonly'}`}
        onSubmit={(e) => onFormSubmit(e)}
        onChange={() => onFormChange()}
      >
        {
          educationsDetails.map((item, index) => {
            return (
              <EducationsDetailsTabItem
                educationsDetails={educationsDetails}
                item={item}
                setEducationsDetails={setEducationsDetails}
                editMode={editMode}
                key={index}
                validateStatus={validateStatus}
                setValidateStatus={setValidateStatus}
                displayButton={displayButton}
                addedMoreEducationButtonRef={addedMoreEducationButtonRef}
                submitButtonRef={submitButtonRef}
                index={index}
                recruiterId={data.id}
                setRefetch={setRefetch}
                setDisplayButton={setDisplayButton}
              />
            )
          })
        }
        {
          editMode ?
            <div className="profile__form__group">
              <div className={`profile__form__more`}
                   id="educationsMoreButton"
                   ref={addedMoreEducationButtonRef}
                   onClick={() => onAddEducationHandler()}
              >
                <img src={plusIc} alt="plust icon" className="profile__form__more--icon"/>
                <p className="profile__form__more--text">Add one more education</p>
              </div>
            </div>
            : null
        }

        {
          displayButton && editMode ?
            <div className="profile__form__group">
              <div className="profile__form__item buttons">
                <button className="profile__form__cancel button--empty" onClick={(e) => onClearHandler(e)}>Cancel</button>
                <button className="profile__form__submit button--filled" type="submit" ref={submitButtonRef}>Save</button>
              </div>
              {success ? <p className="success-message">All changes made</p> : null}
            </div>
            : null
        }
      </form>
    </div>
    </AuthGuard>
  )
}

export default memo(EducationsDetailsTab)