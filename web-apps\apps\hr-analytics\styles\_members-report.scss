@use "config" as *;
@use "mixins" as *;
.members-report {
    .table-screen-top__wrapper{
        flex-wrap: wrap;
    }

    & .graph-wrapper {
        display: flex;
        gap: 15px;
        justify-content: start;

        @include media(md) {
            display: block;
           }
        @include media(xs) {
           display: block;
          }
        .graph-member-summary-circle {
            width: 100%;
        }
        .circle-chart__inner{
            justify-content: start;
            gap: 85px;

            @include media(md) {
                gap: 50px;
            }
        }

    }

    &__buttons {
        margin-left: 40px;
        display: flex;
        align-items: center;
        @include media(md) {
          display: none;
    }
    @include media(sm){
        flex-wrap: wrap;
    }
    @include media(xs){
        margin-left: 0px;
        flex-wrap: wrap;
    }
    
        &__item {
          padding: 12px 20px;
          border-radius: 4px;
          font-size: 14px;
          line-height: 1;
          border: 1px solid #DFE2E6;
          background: $white;
          transition: .3s ease-in, .3s ease-in-out;
          margin-right: 24px;
    
          @include media(md) {
            width: 49%;
            margin-right: 0;
            flex-shrink: 0;
        }
    
          @include media(sm) {
            width: 48%;
            margin-right: 0;
            padding: 12px 10px;
            flex-shrink: 0;
          }
    
    
          &:last-child {
            margin-right: 0;
          }
    
          &.active {
            color: $greenBlue2;
            background: #ACD8D1;
            border: 1px solid #ACD8D1;
          }
        }
      }
}