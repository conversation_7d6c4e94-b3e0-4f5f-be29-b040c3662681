import { CalendarService, LiveCoding, LiveCodingService, LiveDraftsService } from '@microservices/db';
import { Controller, Get, Post, Body, Patch, Param, Delete, Query, UseGuards, Req, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AuthUser, Permissions, PermissionsGuard } from '@microservices/auth';
import { liveCodingDto } from 'libs/db/src/lib/live-coding/live-coding.dto';
// import { QdrantClient } from '@qdrant/qdrant-js';
// import { LangchainService } from '@microservices/integrations';

// const langchainService = new LangchainService();

@Controller('live-coding')
@ApiBearerAuth("access-token")
export class LiveCodingController {
  constructor(
    private liveCodingService: LiveCodingService, 
    private liveDraftsService: LiveDraftsService,
    private calendarService: CalendarService
  ) {}


  @ApiOperation({ summary: 'Get Packages' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200, type: LiveCoding })
  @Get('/packages')
  findAllPackages() {
    return this.liveCodingService.findAllPackages();
  }

  @ApiOperation({ summary: 'Create Live task' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 201 })
  @Post()
  @Permissions('recruiter', 'assessment:edit')
  async create(@Body() liveCodingDTO:liveCodingDto, @AuthUser() user: any) {
    return this.liveCodingService.create(liveCodingDTO, user["https://urecruits.com/companyId"], +user["https://urecruits.com.userId"]);
  }

  @ApiOperation({ summary: 'Create live coding drafts' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 201 })
  @Post('/drafts')
  @Permissions('recruiter', 'assessment:edit')
  async createDraft(@Body() draftDTO, @AuthUser() user: any) {
    return this.liveDraftsService.create(draftDTO, user["https://urecruits.com/companyId"], +user["https://urecruits.com.userId"]);
  }

  @ApiOperation({ summary: 'Get all coding assessment' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200 })
  @Get()
  @Permissions('assessment:view')
  async findAll(@Query() query, @AuthUser() user: any) {
    const liveCoding = await this.liveCodingService.findAll(user["https://urecruits.com/companyId"]);
    const liveDraft = await this.liveDraftsService.findAll(user["https://urecruits.com/companyId"]);
    if(query.status==='ACTIVE'){
      return [...liveCoding];
    }else if(query.status==='DRAFT'){
      return [...liveDraft];
    }else{ 
      return [...liveCoding, ...liveDraft];
    }
  }

  @ApiOperation({ summary: 'Get Convergence Token' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200 })
  @Get('/convergence-token')
  getConvergenceToken(@Req() request) {
    return this.liveCodingService.getConvergenceToken(request.headers.authorization);
  }

  @ApiOperation({ summary: 'Get coding assessment by id' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200, type: LiveCoding })
  @Get(':id')
  findOne(@Param('id') id: string, @AuthUser() user: any) {
    return this.liveCodingService.findOneById(+id, user);
  }

  @ApiOperation({ summary: 'Get coding assessment by id' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200, type: LiveCoding })
  @Get('/task/:taskId')
  async findOneByTaskId(@Param('taskId') taskId: string, @AuthUser() user: any) {
    const draft = await this.liveDraftsService.findOneByTaskId(taskId)
    if(draft){
      return draft;
    }else{
      return this.liveCodingService.findOneByTaskId(taskId, user);
    }
  }

  @ApiOperation({ summary: 'Update coding assessment' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200 })
  @Patch(':id')
  @Permissions('assessment:edit')
  update(@Param('id') id: string, @Body() liveCodingDto: LiveCoding, @AuthUser() user: any) {
    return this.liveCodingService.update(+id, liveCodingDto, user["https://urecruits.com/companyId"], +user["https://urecruits.com.userId"]);
  }

  @ApiOperation({ summary: 'Delete coding assessment' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200 })
  @Delete(':id')
  @Permissions('assessment:edit')
  remove(@Param('id') id: string, @AuthUser() user: any) {
    return this.liveCodingService.remove(+id, user["https://urecruits.com/companyId"],  +user["https://urecruits.com.userId"]);
  }

  @ApiOperation({ summary: 'Search live coding  tasks' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200 })
  @Get('search')
  @Permissions('assessment:view')
  async search(@Query() query, @AuthUser() user: any) {
    const liveCoding = await this.liveCodingService.search(query, user["https://urecruits.com/companyId"]);
    const liveDraft = await this.liveDraftsService.search(query, user["https://urecruits.com/companyId"]);

    return [...liveCoding, ...liveDraft];
  }

  @ApiOperation({summary:"Invite interviewers in live coding"})
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({status:200})
  @Post('invite-interviewers')
  @Permissions('recruiter')
  async inviteInterviewers(@Body() body){
      const interviewers = body?.interviewers;
      const redirectLink = body?.redirectLink;
      const roomId = body?.roomId;
      try{
        return Promise.all([...interviewers.map(interviewer=>{
          return this.liveCodingService.inviteInterviewer(interviewer,redirectLink)
        }), this.calendarService.addInterviewers(roomId,interviewers)])
      }
      catch(error){
        Logger.log("error in inviterInterviewers: LiveCodingController",error);
        throw new HttpException("Couldn't invite interviewers",HttpStatus.INTERNAL_SERVER_ERROR);
      }
  }

  @ApiOperation({summary:"Create dummy data for live coding"})
  @ApiResponse({status:200})
  @Post('/dummy')
  async createDummyLiveCodingData( @Body() dto:{companyId:number}) {
    return this.liveCodingService.createDummyLiveCodingData(dto.companyId);
  }

  // @ApiOperation({summary:"Feed live coding tasks to qdrant"})
  // @ApiResponse({status:200})
  // @Post('qdrant/feed')
  // async feedToQdrant(){
  //   const client = new QdrantClient({ url: process.env.QDRANT_DATABASE_URL, apiKey: process.env.QDRANT_API_KEY });
  //   // Feed live coding tasks to qdrant
  //   const batchSize = 10;
  //   // let liveCodingOffset = 0;
  //   // let hasMoreLiveCodings = true;

  //   // while (hasMoreLiveCodings) {
  //   //   const liveCodings = await this.liveCodingService.getAllLiveCoding({ limit: batchSize, offset:liveCodingOffset });
  //   //   if (!liveCodings?.length) {
  //   //     hasMoreLiveCodings = false;
  //   //     break;
  //   //   }

  //   //   const points = [];
  //   //   for (let liveCoding of liveCodings) {
  //   //     const data = this.liveCodingService.getLiveCodingDataForQdrant(liveCoding);
  //   //     if(!data){
  //   //       continue;
  //   //     }
  //   //     console.log({dataStr:data.str});
  //   //     try {
  //   //       const liveCodingVector = await langchainService.getEmbeddings(data.str);
  //   //       points.push({
  //   //         id:data.id,
  //   //         vector:liveCodingVector,
  //   //         payload:data.payload
  //   //       })
  //   //     } catch (error) {
  //   //       console.error("Error fetching embeddings:", error);
  //   //     }

  //   //     // Add a delay to respect rate limits
  //   //     await new Promise(resolve => setTimeout(()=>{
  //   //       resolve(true)
  //   //     }, 3000));
  //   //   }
  //   //   console.log({points});
  //   //   try {
  //   //     const response = await client.upsert("live-coding", { wait: true, points });
  //   //     console.log("Upsert Response:", response);
  //   //   } catch (error) {
  //   //     console.error("Upsert failed:", error);
  //   //   }

  //   //   // Move to the next batch
  //   //   liveCodingOffset += batchSize;
  //   // }


  //   // // Feed live coding drafts to qdrant
  //   let liveDraftOffset = 0;
  //   let hasMoreLiveDrafts = true;

  //   while (hasMoreLiveDrafts) {
  //     const liveDrafts = await this.liveDraftsService.getAllLiveDrafts({ limit: batchSize, offset:liveDraftOffset });
  //     if (!liveDrafts?.length) {
  //       hasMoreLiveDrafts = false;
  //       break;
  //     }

  //     const points = [];
  //     for (let liveDraft of liveDrafts) {
  //       const data = this.liveCodingService.getLiveCodingDataForQdrant(liveDraft);
  //       if(!data){
  //         continue;
  //       }
  //       console.log({dataStr:data.str});
  //       try {
  //         const liveDraftVector = await langchainService.getEmbeddings(data.str);
  //         points.push({
  //           id:data.id,
  //           vector:liveDraftVector,
  //           payload:data.payload
  //         })
  //       } catch (error) {
  //         console.error("Error fetching embeddings:", error);
  //       }

  //       // Add a delay to respect rate limits
  //       await new Promise(resolve => setTimeout(()=>{
  //         resolve(true)
  //       }, 3000));
  //     }
  //     console.log({points});
  //     try {
  //       const response = await client.upsert("live-coding", { wait: true, points });
  //       console.log("Upsert Response:", response);
  //     } catch (error) {
  //       console.error("Upsert failed:", error);
  //     }

  //     // Move to the next batch
  //     liveDraftOffset += batchSize;
  //   }

  //   return { message: "Feeding to qdrant completed" };
  // }
    
}
