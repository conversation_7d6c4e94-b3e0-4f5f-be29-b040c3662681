@use './config' as *;
@use './mixins' as *;

.domain-assessment-answers {
  width: 100%;
  font-size: 16px ;
  color: #2A2C33;
  display: flex;
  flex-direction: column;
  gap: 32px;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center; 
    font-size: 16px;
    font-weight: 600;
    margin: 24px 16px;
    @include media(md){
      margin: 0;
    }
  }

  &__single-view{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 24px 16px;

    button{
      padding: 10px 28px;
      // border: 1px solid $mainGreen;
      border-radius: 4px;
      color: $mainGreen;

      &:disabled{
        color: $grayTone4;
      }
    }

    @include media(xs){
      margin: 0;
    }
  }

  &__container {
    width: 100%;
    padding: 36px;
    border-radius: 12px;
    border: 1px solid $grayTone2;
    box-shadow: $cardShadow;
    display: flex;
    flex-direction: column;
    gap: 24px;

    @include media(md){
      padding: 24px;
    }

    &__question {
      color: black;
      font-weight: 600;
    }

    hr {
      width: 100%;
      border: 1px solid $grayTone2;
    }

    &__score {
      display: flex;
      flex-direction: column;
      gap: 24px;

      @include media(md){
        gap: 0;
      }


      &__content {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 24px;

        @include media(md){
          flex-direction: column;
          align-items: flex-start;
          gap: 20px;
        }

        &.candidate {
          justify-content: space-between;

          @include media(sm) {
            flex-direction: column-reverse;
          }
        }

        &__candidate {
          display: flex;
          justify-content: space-between;
          width: 100%;

          p{
            width: 80%;
            @include media(md){
              width: 100%;
            }
          }

          &.end{
            justify-content: flex-end;
          }

          @include media(md){
            flex-direction: column;
            gap: 20px;
             pre{
              align-self: flex-end;
             }
          }
        }
      }

    }

    &__text {
      color: $grayTone7;
      font-size: 14px;
      font-weight: 500;


      &.gray {
        color: $grayTone5;
      }

      &.green {
        color: $mainGreen;
      }

      &.with-dotted {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        display: block;
      }
    }
  }
}

.text {
  &__textarea {
    border: 1px solid black;
    position: relative;
    overflow: auto;
    max-width: 100%;
    width: 100%;
    min-width: 100%;
    height: 100px;
    min-height: 50px;
    max-height: 200px;
    border-radius: 7px;
    padding: 1rem;
    font-size: 14px;
  }
}

.multiple {
  &__options {

    font-weight: 400;
    display: flex;
    flex-direction: row;
    margin-top: 10px;
    padding: 6px 16px;
    border-radius: 4px;
    display: flex;
    align-items: center;

    &.answers {
      justify-content: space-between;
      font-size:16px;
      @include media(sm){
        font-size: 14px;
      }
    }

  }

  img {
    width: 14px;
    height: 11px;
  }

  .selected {
    background: #ACD8D14D;
  }

  .non-selected {
    background: #FE467226;
  }

  .checkbox-checked[type="checkbox"] {
    align-self: center;
    -webkit-appearance: none;
    appearance: none;
    background-color: var(--form-background);
    margin: 0;
    font: inherit;
    color: rgba(9, 156, 115, 1);
    width: 16px;
    height: 16px;
    margin-right: 6px;
    border: 1px solid rgba(193, 197, 203, 1);
    border-radius: 2px;
    transform: translateY(-0.075em);
    display: grid;
    place-content: center;
    &:checked {
      transform: scale(1);
      border: 1px solid rgba(9, 156, 115, 1);
    }
  }

  .checkbox-checked[type="checkbox"]::before {
    content: "";
    width: 10px;
    height: 8px;
    clip-path: polygon(14% 44%, 0 65%, 50% 100%, 100% 16%, 80% 0%, 43% 62%);
    transform: scale(0);
    transform-origin: bottom left;
    transition: 120ms transform ease-in-out;
    // box-shadow: inset 1em 1em rgba(9, 156, 115, 1);
    /* Windows High Contrast Mode */
    background-color: rgba(9, 156, 115, 1);
  }

  .checkbox-checked[type="checkbox"]:checked::before {
    transform: scale(1);
    // border: 1px solid rgba(9, 156, 115, 1);
  }


  .checkbox-checked[type="checkbox"]:disabled {
    --form-control-color: rgba(193, 197, 203, 1);
    color: rgba(193, 197, 203, 1);
    cursor: not-allowed;
  }
}

.single {
  &__options {
    margin-top: 10px;
    padding: 6px 16px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    img {
      width: 14px;
      height: 11px;
    }
  }

  .selected {
    background: #ACD8D14D;
  }


  .non-selected {
    background: #FE467226;
  }

  .custom-radio {
    display: none;
  }

  .custom-radio+label {
    position: relative;
    padding-left: 25px;
    cursor: pointer;
  }

  .custom-radio+label::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 14px;
    height: 14px;
    border: 1px solid rgba(9, 156, 115, 1);
    border-radius: 50%;
  }

  .custom-radio:checked+label::before {
    background-color: rgba(9, 156, 115, 1);
  }
}

.feedback-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  @include media(md){
    width: 100%;
    align-items: flex-start;
  }


  &__left {
    display: flex;
    flex-direction: row;
    font-size: 14px;

    .show-feedback {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 10px;
      cursor: pointer;
      margin-left: 1rem;
      @include media(md){
        margin: 0;
        flex-direction: column;
        align-items: flex-start;
        gap: 0;
      }

      .show-feedback__data {
        width: auto;
        max-width: 80%;
        flex-shrink: 0;
        word-wrap: break-word;
        @include media(md){
          width: 100%;
        }
          
      }

      img {
        height: 20px;
        margin: 3px;
      }

      &__action {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
      }

    }

    .add-feedback {
      display: flex;
      flex-direction: row;
      cursor: pointer;

      @include media(md){
        align-items: center;
        gap: 8px;
      }

      img {
        height: 50px;
      }

      p {
        min-width: max-content;
        padding: 15px 10px;
        @include media(md){
          padding:0;
        }
      }
    }

  }

  &__right {
    display: flex;
    flex-direction: row;
    width:30%;
    gap: 10px;
    justify-content: flex-end;
    
    @include media(md){
      flex-direction: row;
      gap: 20px;
      width: 100%;

      &.edit{
        width: 60%;
      }
    }

    .action {
      display: flex;
      flex-direction: row;
      max-width: 65%;

      .edit-score{
        display: flex;
        gap: 16px;
      }

      img {
        height: 32px;
        width: 32px;
        padding: 6px;
        border: 1px solid $grayTone2;
        border-radius: 4px;
        cursor: pointer;
      }

      button {
        font-size: 16px;
        color: $mainGreen;
      }
    }

    .marks {
      width: 70px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
    .marks input {
      width: 30px;
      border-bottom: 1px solid $greenBlue1;
      &:disabled{
        border-bottom: 1px solid $grayTone4;
      }
    }

  }

  @media (max-width: 1000px) {
    flex-direction: column;
    gap: 20px;

    &__right {

      .marks {
        align-items: center;
      }
    }

    &__left {
      margin-top: 1rem;
      max-width: 100%;
      align-items: center;
    }

    .show-feedback {
      justify-content: center;
    }
  }

}