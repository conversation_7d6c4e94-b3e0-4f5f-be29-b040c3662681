import {IAsyncCheck<PERSON><PERSON><PERSON><PERSON>, I<PERSON>ol<PERSON><PERSON><PERSON>, ITabsFilter} from "@urecruits/recruitments/types/global/global";

export interface ICandidateJobsTable {
	tableView: string,
	noResultSearch: boolean,
	tableEmpty: boolean,
	// candidateJobs: Array<ICandidateJobItem>
	filters: {
		searchValue: string,
		sortBy: string,
		sortType: string,
		locations: Array<IAsyncCheckBoxList>,
		postedOn: string,
		experience: Array<number>,
		salaryMonth: Array<number>,
		salaryYear: Array<number>,
		education: string,
		skills: Array<string>,
		company: string,
		jobType: string,
		preferableShift: string,
		industryType: string,
		functionalArea: string
	},
	filterType: "" | "applied" | "saved" | "matched",
	pagination: {
		currentPage: number,
		limit: number,
		totalCount: number
	},
	// fixedTab: {
	// 	id: number,
	// 	displayName: string
	// },
	// tabFilter: Array<ITabsFilter>,
	colReorder: Array<IColReorder>,
	filterInfo: {
		tabs: Array<any>
	},
	applyPopup: {
		visible: boolean,
		jobId: number,
		jobTitle: string
	},
}

export interface ICandidateJobItem {
	id: number,
	jobTitle: string,
	applicationForm: Array<any>,
	company: {
		name: string,
		avatar: string
	},
	locations: Array<{
		id: number,
		city: string,
		state: string
	}>,
	subscribes: Array<any>,
	remoteLocation: boolean,
	experience: [number, number],
	salaryRangeMonth: [number, number],
	salaryRangeYear: [number, number],
	skills: Array<string>,
	postedOn: string,
	education: string,
	jobType: string,
	preferableShift: string,
	industryType: string,
	functionalArea: string
}
